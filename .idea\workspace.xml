<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="192b8e64-3376-4256-9c3e-6b29796253d2" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/tiance-industry-finance/service_data_manage/api/routes/data_manage_route.py" beforeDir="false" afterPath="$PROJECT_DIR$/tiance-industry-finance/service_data_manage/api/routes/data_manage_route.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/tiance-base" />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 7
}]]></component>
  <component name="ProjectId" id="2ytRVHrlc5G3eTANmtpN7km61J9" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "dev-current",
    "last_opened_file_path": "C:/Users/<USER>"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="192b8e64-3376-4256-9c3e-6b29796253d2" name="更改" comment="" />
      <created>1750651904868</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750651904868</updated>
    </task>
    <servers />
  </component>
</project>