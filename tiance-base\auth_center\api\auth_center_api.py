#!/usr/bin/env python
# -*- coding: UTF-8 -*-
from auth_center.utils import generate_jwt, check_password,md5
from auth_center.utils import pack_admin_permission
from auth_center.entity.account_entity import AccountEntity,AccountEntityNew
from fastapi import APIRouter, HTTPException,Depends,Request
from base_utils.ret_util import RetUtil
from fastapi.responses import JSONResponse
from fastapi.responses import Response
from base_utils.log_util import LogUtil
from auth_center.service.auth_service import AccountAuthService
from sqlalchemy.orm import Session
from auth_center.model.usr_model import Usr_Model
from base_utils.sm2 import sm2decrypt
from base_utils.mysql_util import query2dict
from service_permission_auth.entity.usr_auth_entity import UsrEntity
from service_permission_auth.service.usr_auth_service import UsrAuthService
router = APIRouter()

def get_db(request: Request):
    db = request.app.state.SessionLocal()
    try:
        yield db
    finally:
        db.close()

api_router = APIRouter()
@api_router.post("/token/access", summary="Token获取", response_model=AccountEntityNew)
async def login(request: Request, model_info: AccountEntityNew,db: Session = Depends(get_db)) -> Response:
    try:
        # Find user in database
        acc = AccountAuthService.query_acc_by_acc_name(db=db, account_name=model_info.account_name)
        if not acc:
            return RetUtil.response_error(message="账号不存在")
        acc_id = query2dict(acc,Usr_Model)['account_id']
        acc_pw = query2dict(acc,Usr_Model)['password']
        enpassword = md5(model_info.password)
        if acc_pw != enpassword:
            return RetUtil.response_error(message="密码错误")

        #   Verify Account Token
        token = await AccountAuthService.token_cache(request=request,account_id=acc_id)
        data = {
                    'account_id':acc_id,
                    'accessToken': token
                    }

        return RetUtil.response_ok(data=data)

    except Exception as e:
        detail = f"服务器错误:{e}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

# @api_router.post("/login", summary="账号登录", response_model=AccountEntityNew)
# async def login(model_info: AccountEntityNew,db: Session = Depends(get_db)) -> Response:
#     try:
#         # Find user in database
#         acc = AccountAuthService.query_acc_by_acc_name(db=db, account_name=model_info.account_name)
#         if not acc:
#             return RetUtil.response_error(message="账号不存在")
#         acc_id = query2dict(acc,Usr_Model)['account_id']
#         acc_pw = query2dict(acc,Usr_Model)['password']
#         acc_type = query2dict(acc,Usr_Model)['attribute']
#         usr_name = query2dict(acc, Usr_Model)['usr_name']
#         decryptpassword = sm2decrypt(model_info.password)
#         enpassword = md5(decryptpassword)
#         if acc_pw != enpassword:
#             return RetUtil.response_error(message="密码错误")
#         role = AccountAuthService.query_role_by_acc_id(db=db, account_id=acc_id)
#         #   Verify Account Token
#         token,refresh_token = AccountAuthService.token_access(db=db,account_id=acc_id)
#
#         data =  {
#                'avatar': "",
#                'nickname': usr_name,
#                'account_id':acc_id,
#                'attribute':acc_type,
#                'accessToken': token,
#                'RefreshToken':refresh_token
#                }
#
#         return RetUtil.response_ok(data=data)
#
#     except Exception as e:
#         detail = f"服务器错误:{e}"
#         LogUtil.error(msg=detail)
#         # 返回HTTP错误响应
#         raise HTTPException(status_code=400, detail=detail)


@api_router.post("/login", summary="账号登录", response_model=AccountEntityNew)
async def login(request: Request, model_info: AccountEntityNew,db: Session = Depends(get_db)) -> Response:
    try:
        # Find user in database
        acc = AccountAuthService.query_acc_by_acc_name(db=db, account_name=model_info.account_name)
        if not acc:
            return RetUtil.response_error(message="账号不存在")
        acc_id = query2dict(acc,Usr_Model)['account_id']
        acc_pw = query2dict(acc,Usr_Model)['password']
        acc_type = query2dict(acc,Usr_Model)['attribute']
        usr_name = query2dict(acc, Usr_Model)['usr_name']
        decryptpassword = sm2decrypt(model_info.password)
        enpassword = md5(decryptpassword)
        if acc_pw != enpassword:
            return RetUtil.response_error(message="密码错误")
        #   Verify Account Token
        token = await AccountAuthService.token_cache(request=request, account_id=acc_id)
        teams = UsrAuthService.usr_team(db=db, auth=UsrEntity(account_id=acc_id))

        data =  {
                   'avatar': "",
                   'nickname': usr_name,
                   'account_id':acc_id,
                   'attribute':acc_type,
                   'accessToken': token["value"],
                   'teams': teams
        }

        return RetUtil.response_ok(data=data)

    except Exception as e:
        detail = f"服务器错误:{e}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

