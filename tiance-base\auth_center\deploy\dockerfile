# Use an official Python runtime as a parent image
FROM python:3.10-buster

# Set the working directory in the container
WORKDIR /app

# Copy the current directory contents into the container at /app
COPY . /app

# Install any needed packages specified in requirements.txt
RUN pip install --no-cache-dir -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN pip uninstall -y jwt PyJWT
RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple PyJWT
# RUN pip install --no-cache-dir -i https://pypi.tuna.tsinghua.edu.cn/simple PyJWT

# Install additional tools (optional, but useful for debugging)
# RUN apt-get update && apt-get install -y \
#     curl \
#     vim \
#     net-tools \
#     && rm -rf /var/lib/apt/lists/*

# Make port 8000 available to the world outside this container (adjust the port if needed)
EXPOSE 8899

# Define environment variable (optional)
# ENV NAME YourAppName

# Run the application (replace with your actual start command)
CMD ["python", "app.py"]