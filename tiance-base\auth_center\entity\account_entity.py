#!/usr/bin/env python
# -*- encoding: utf-8 -*-
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Union

class AccountEntity(BaseModel):
    username: str = Field(..., example="A123", description="账号名称")
    password: str = Field(..., example=123, description="密码")

class AccountEntityNew(BaseModel):
    account_name: str = Field(..., example="A123", description="账号名称")
    password: str = Field(..., example=123, description="密码")


