from auth_center.model.usr_model import Usr_Model
from sqlalchemy.orm import Session
from auth_center.model.account_token import AccountToken_Model
from auth_center.model.role_mem_model import RoleMem_Model
from base_utils.mysql_util import query2dict
from auth_center.utils import generate_access_token, verify_jwt,generate_refresh_token_jwt,generate_refresh_token
import time
import aioredis
from fastapi import FastAPI,Header,HTTPException,Request
from service_permission_auth.service.usr_auth_service import UsrAuthService
from sqlalchemy.orm import Session
from base_utils.redis_util import RedisUtil
import base64
import json
import ast
from service_permission_manage.service.config_service import ConfigService
from service_permission_manage.model.config_model import Config_Model

class AccountAuthService(object):

    @staticmethod
    def query_acc_by_acc_name(db: Session, account_name: str):
        return db.query(Usr_Model).filter(Usr_Model.account_name == account_name,Usr_Model.status == 1).first()

    @staticmethod
    def query_role_by_acc_id(db: Session, account_id: str):
        return [item.role_id for item in db.query(RoleMem_Model.role_id).filter(RoleMem_Model.account_id == account_id,RoleMem_Model.status == 1)]

    @staticmethod
    def local_time():
        local_time = time.strftime('%Y-%m-%d %H:%M:%S',time.localtime(time.time()))
        return local_time

    @staticmethod
    def token_access(db: Session, account_id: str):
        create_time = AccountAuthService.local_time()
        update_time = create_time

        account_info = db.query(AccountToken_Model).filter(AccountToken_Model.account_id == account_id).first()

        if not account_info:
            token=generate_access_token({'account_id': account_id})
            refresh_token= generate_refresh_token_jwt({'account_id': account_id})
            db_usr = AccountToken_Model(account_id=account_id, token=token, refresh_token=refresh_token,
                               create_time=create_time, update_time=update_time)
            db.add(db_usr)
            db.commit()
            db.refresh(db_usr)

        else:
            token = query2dict(account_info,AccountToken_Model)['token']
            refresh_token = query2dict(account_info,AccountToken_Model)['refresh_token']
            if not verify_jwt(token):
                token = generate_access_token({'account_id': account_id})
                account_info.token = token
                account_info.update_time = update_time
                db.commit()
                db.refresh(account_info)

            if not verify_jwt(refresh_token):
                refresh_token = generate_refresh_token_jwt({'account_id': account_id})
                account_info.refresh_token = refresh_token
                account_info.update_time = update_time
                db.commit()
                db.refresh(account_info)

        return token ,refresh_token
    @staticmethod
    async def token_cache(request: Request,account_id: str):
        redis = request.app.state.redis_pool
        account_info = await RedisUtil.get_cached_data(key=account_id,redis=redis)
        if not account_info:
            token=generate_access_token({'account_id': account_id})
            redis_token = await RedisUtil.cache_data(key=account_id, value=token,redis=redis)
        else:
            token = account_info['value']
            redis_token = await RedisUtil.cache_data(key=account_id, value=token,redis=redis)

        return redis_token



    @staticmethod
    def decode_jwt(token):
        try:
            parts = token.split('.')
            if len(parts) != 3:
                raise HTTPException(status_code=401, detail="Token无效")
            payload = json.loads(base64.urlsafe_b64decode(parts[1] + '===').decode('utf-8'))
            account_id = payload.get('account',None)
            return account_id
        except Exception as e:
            detail = f"Token无效"
            # 返回HTTP错误响应
            raise HTTPException(status_code=401, detail=detail)

    @staticmethod
    async def token_verify(request: Request,redis: aioredis.Redis,db: Session):
        url = request.url.components.path
        app_id = request.headers.get('app-id',None)
        API_Whitelist_info = await AccountAuthService.config_verify(redis=redis,db=db,keyword='API_Whitelist')
        if not API_Whitelist_info:
            return None
        if any(keyword in url for keyword in API_Whitelist_info):
            return None
        if url =='/':

            return None
        if request.url.path.startswith("/api/outside"):
            API_OutAuth_info = await AccountAuthService.config_verify(redis=redis, db=db, keyword='API_OutAuth')
            if not API_OutAuth_info:
                raise HTTPException(status_code=403, detail="接口未授权")
            if any(keyword in url for keyword in API_OutAuth_info):
                new_path = request.url.path.replace("/api/outside", "/tc/llm/base")
                request.scope["path"] = new_path
                if app_id is not None:
                    account_id = UsrAuthService.get_account_by_app_id(db=db, app_id=app_id)
                    if account_id == '':
                        raise HTTPException(status_code=402, detail="app_id无效")
                    else:
                        request.state.account_id = account_id
                        return None
                else:
                    raise HTTPException(status_code=402, detail="app_id不能为空")
            else:
                raise HTTPException(status_code=403, detail="接口未授权")
        else:
            token = request.headers.get('Token',None)
            if token is not None:
                account_id = AccountAuthService.decode_jwt(token)
                account_info = await RedisUtil.get_cached_data(key=account_id,redis=redis)
                if not account_info:
                    raise HTTPException(status_code=401, detail="Token已过期")

                elif token != account_info['value']:
                    raise HTTPException(status_code=401, detail="Token无效")

                else:
                    await RedisUtil.cache_data(key=account_id, value=token,redis=redis)
                    request.state.account_id = account_id
                    return None
            else:
                raise HTTPException(status_code=401, detail="请求头Token不可为空")

    @staticmethod
    async def config_cache(redis: aioredis.Redis,db: Session,keyword: str):
        configs = ConfigService.query_config_description_by_name(db,keyword)
        if configs:
            api_list = configs[0]
            return await RedisUtil.cache_data_static(key=keyword, value=str(api_list),redis=redis)
        else:
            return None

    @staticmethod
    async def config_verify(redis: aioredis.Redis,db: Session,keyword: str):
        api_config= await RedisUtil.get_cached_data(key=keyword,redis=redis)
        if not api_config:
            return None
        else:
            api_config = ast.literal_eval(api_config['value'])
            return api_config[keyword]




# @staticmethod
# def generate_captcha(captcha_id: str):
#     chars = string.ascii_letters + string.digits  # 字符集（数字+字母）
#     code = ''.join(random.choices(chars, k=4))   # 随机生成4个字符
#     captcha_store[captcha_id] = code.lower()     # 保存验证码（小写）
#
#     # 图像大小
#     width, height = 120, 40
#     image = Image.new('RGB', (width, height), color=(255, 255, 255))  # 白色背景
#     draw = ImageDraw.Draw(image)
#
#     # 字体路径（更换为你本地字体文件路径）
#     font = ImageFont.truetype('arial.ttf', 32)
#
#     # 绘制验证码字符
#     for i, char in enumerate(code):
#         x = 10 + i * 25
#         y = random.randint(5, 10)
#         draw.text((x, y), char, font=font, fill=(random.randint(0, 100), random.randint(0, 100), random.randint(0, 255)))
#
#     # 添加干扰线
#     for _ in range(5):
#         x1, y1 = random.randint(0, width), random.randint(0, height)
#         x2, y2 = random.randint(0, width), random.randint(0, height)
#         draw.line((x1, y1, x2, y2), fill=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)), width=1)
#
#     # 添加噪点
#     for _ in range(30):
#         x, y = random.randint(0, width), random.randint(0, height)
#         draw.point((x, y), fill=(0, 0, 0))
#
#     return image, code

if __name__ == "__main__":
    token ='eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************.bznU8ENX5GJIQi1xu1_uXgwzxRoi2GSTAADYeFoSoLE'
    header= AccountAuthService.decode_jwt(token)
    print(header)




