<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login</title>
</head>
<body>
    <h2>Login</h2>
    <form id="loginForm">
        <label for="username">Username:</label>
        <input type="text" id="username" name="username" required>

        <label for="password">Password:</label>
        <input type="password" id="password" name="password" required>

        <button type="submit">Login</button>
    </form>

    <script>
        document.getElementById('loginForm').onsubmit = async function(e) {
            e.preventDefault();  // Prevent default form submission

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            // Send user info to the backend
            const response = await fetch('/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            const result = await response.json();
            console.log(result);

            // Handle the response from the server
            if (response.ok) {
                // If login is successful, redirect to a protected route or dashboard
                window.location.href = "/dashboard";
            } else {
                // If login fails, show an error message
                alert(result.error);
            }
        };
    </script>
</body>
</html>
