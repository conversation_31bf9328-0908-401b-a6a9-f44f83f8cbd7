#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base 
@File    ：api_config.py
<AUTHOR>
@Date    ：2024/8/25 22:49 
"""


class ApiConfig(object):
    """
    API配置
    """
    SERVICE_IP = "0.0.0.0"

    # 服务端口
    SERVICE_PORT = 9101

    #服务端口

    # Xinference主节点endpoint
    SUPERVISOR_ENDPOINT = "http://***********:9997"
    
    
    # 根路由
    ROOT_ROUTE = "/tc/llm/base"

    # 配置管理路由
    CONFIG_MANAGE_ROUTE = "/config/manage"

    # 模型管理路由
    MODEL_MANAGE_ROUTE = "/model/manage"

    # 知识库管理路由
    KNOWLEDGE_MANAGE_ROUTE = "/knowledge/manage"

    # 工具管理路由
    TOOLSET_MANAGE_ROUTE = "/toolset/manage"

    # 数据集管理路由
    DATASET_MANAGE_ROUTE = "/dataset/manage"
    
    # 智能体管理路由
    AGENT_MANAGE_ROUTE = "/agent/manage"

    # 工作流管理路由
    WORKFLOW_MANAGE_ROUTE = "/workflow/manage"

    # 提示词管理路由
    PROMPT_MANAGE_ROUTE = "/prompt/manage"

    # 权限管理路由
    PERMISSION_MANAGE_ROLE_ROUTE = "/permission-manage/role"
    PERMISSION_MANAGE_AUTH_ROUTE = "/permission-manage/auth"
    PERMISSION_MANAGE_CONFIG_ROUTE = "/permission-manage/config"
    PERMISSION_MANAGE_RESOURCE_ROUTE = "/permission-manage/resource"
    PERMISSION_MANAGE_TEAM_ROUTE = "/permission-manage/team"

    # 鉴权路由
    PERMISSION_AUTH_ROUTE = "/permission-auth"

    # 用户管理路由
    USR_MANAGE_ROUTE = "/user-manage"


    #API 版本
    MODEL_SERVICE_VERSION = "v0.1"
    

