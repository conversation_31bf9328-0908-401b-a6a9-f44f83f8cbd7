#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base 
@File    ：milvus_config.py
<AUTHOR>
@Date    ：2024/8/25 22:39 
"""


class MilvusConfig(object):
    """
    milvus配置
    """

    # 知识库连接信息
    MILVUS_CONNECT_INFO = {
        "uri": "http://{}".format("10.8.21.165:19530"),
        "user": "",
        "password": "",
        "secure": False,
        "db_name": "qi_wen_kb",
    }

    # Milvus默认输出fields
    DEFAULT_MILVUS_OUTPUT_FIELDS = ["content", "number", "file_name", "file_time"]
