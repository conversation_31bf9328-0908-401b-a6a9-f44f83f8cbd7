#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base
@File    ：mongodb_config.py
<AUTHOR>
@Date    ：2024/8/25 20:36
"""


class MongodbConfig(object):
    """
    mongodb配置信息
    """

    # mongodb配置
    MONGODB_HOST = "***********"
    MONGODB_PORT = 27017
    MONGODB_USER = "tansun"
    MONGODB_PASS = "<EMAIL>"
    MONGODB_DB = "tiance_base"
    AUTH_MECHANISM = "SCRAM-SHA-256"


class CollectionConfig(object):
    """
    collection配置信息
    """
    # 模型启动信息集合名
    MODEL_RUN_COLLECTION = "model_run_info"

    # 模型信息集合名
    MODEL_FAMILY_COLLECTION = "model_list_test"

    # 知识库基础信息
    KB_COLLECTION = "kb_list_info"

    # 工具基础信息
    TOOL_COLLECTION = "tool_list_info"

    # 工具配置信息
    TOOL_INFO_COLLECTION = "tool_config_info"

    # 嵌入模型信息
    EMBEDDING_COLLECTION = "embedding_model_list"

    # 智能体基础信息
    AGENT_COLLECTION = "agent_list_info"

    # 编排智能体基础信息
    ARRANGE_AGENT_COLLECTION = "arrange_agent_list_info"

    # 工作流基础信息
    WORKFLOW_COLLECTION = "workflow_list_info"

    # 工作流编排信息
    WORKFLOW_ARRANGE_COLLECTION = "workflow_arrange_info"

    # 工作流节点执行信息
    WORKFLOW_NODE_EXECUTE_COLLECTION = "workflow_node_execute_info"

    # 角色信息
    ROLE_COLLECTION = "role_list_info"

    # 资源信息
    RES_COLLECTION = "res_list_info"

    # 资源信息
    ROLE_RES_COLLECTION = "role_res_relation"

    # 资源信息
    ROLE_MEM_COLLECTION = "role_mem_relation"

    # 账号信息
    USR_COLLECTION = "usr_info"

    # 配置文件信息
    CONFIG_FILE_COLLECTION = "config_file_info"

    # 配置数据集信息
    CONFIG_DATASET_COLLECTION = "config_dataset_info"

    # 配置训练任务信息
    CONFIG_TRAIN_COLLECTION = "train_task_info"

    # 配置训练参数信息
    CONFIG_TRAIN_PARAMS_COLLECTION = "train_params_info"

    # 配置上传文件信息
    UPLOAD_FILE_INFO_COLLECTION = "upload_file_info"

    # 类别字典表
    SYS_STATIC_DICT_TYPE = "sys_static_dict_type"

    # 子类别字典表
    SYS_STATIC_DICT_ITEMS = "sys_static_dict_items"