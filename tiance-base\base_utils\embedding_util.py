# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base
@File    ：bge_util.py
<AUTHOR>
@Date    ：2024/09/13 17:57
"""

import openai
import traceback
from typing import Union
from base_utils.log_util import LogUtil
from base_configs.model_config import ModelConfig
from base_utils.mongodb_util import MongodbUtil
from base_configs.mongodb_config import CollectionConfig
from bson import ObjectId

class EmbeddingUtil(object):
    """
    embedding工具类
    """

    def __init__(self,embedding_id):
        """
        初始化embedding
        """
        result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_RUN_COLLECTION, doc_id=ObjectId(embedding_id))
        print("result",result)
        LogUtil.info(f"查询结果{result},type{type(result)}")
        if result["is_external"] == True:
            LogUtil.info(f'{result["api_key"]}')
            self.embedding_client = openai.Client(
                api_key=result["api_key"], base_url=result["api_url"]
            )
        else:
            self.embedding_client = openai.Client(
                api_key=ModelConfig.EMBED_API_KEY, base_url=ModelConfig.EMBED_API_BASE
            )

    def get_embedding(self, model_uid: str, input: Union[str, list]):
        """
        获取向量
        :model_uid: 模型UID
        :param input: 句子
        :return: 向量列表
        """
        try:
            embedding_results = self.embedding_client.embeddings.create(
                input=input,
                model=model_uid,
            )

            embeddings = []

            for item in embedding_results.data:
                embeddings.append(item.embedding)

            return embeddings
        except (Exception, RuntimeError) as e:
            LogUtil.log_json(describe="异常信息:", info=str(e))
            LogUtil.error("异常:{0}".format(str(traceback.format_exc())))
            raise


if __name__ == "__main__":
    embedding_client = EmbeddingUtil()
    res = embedding_client.get_embedding(
        model_uid="bge-base-zh-v1.5", input="今天天气如何"
    )
    if res:
        print(type(res))
        print("嵌入成功！")
    else:
        print("出错")
    print(res)
