#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base 
@File    ：log_util.py
<AUTHOR>
@Date    ：2024/8/25 20:30 
"""
import os
import json
import logging
from logging import handlers
from base_configs.log_config import LogConfig
import logging
import logging.handlers as handlers
import os

class LogUtil(object):
    """
    日志工具类
    """
    logger = None

    @staticmethod
    def init(process_name: str):
        """
        初始化
        :param process_name: 进程名称
        :return:
        """
        # 初始化日志对象
        LogUtil.logger = LogUtil.get_logger(process_name)

    @staticmethod
    def get_logger(logger_name):
        """
        获得日志对象
        :param logger_name: 日志名称
        """
        # 获得日志对象
        logger = logging.getLogger(logger_name)
        # 设置日志级别
        logger.setLevel(logging.DEBUG)  # 设置为DEBUG级别，确保所有日志都能被处理
        # 获取日志文件名称
        base_file_name = os.path.join(LogConfig.LOG_DIR, logger_name + '.logs')
        error_file_name = os.path.join(LogConfig.LOG_DIR, 'error.logs')

        # 创建一个处理器，用于写入所有日志到 tiance-base.logs
        th_base = handlers.TimedRotatingFileHandler(
            filename=str(base_file_name), when=LogConfig.WHEN,
            backupCount=LogConfig.BACKUP_COUNT, encoding='utf-8'
        )
        th_base.setLevel(logging.DEBUG)  # 设置为DEBUG级别，确保所有日志都能被写入

        # 创建一个处理器，用于写入错误日志到 error.logs
        th_error = handlers.TimedRotatingFileHandler(
            filename=str(error_file_name), when=LogConfig.WHEN,
            backupCount=LogConfig.BACKUP_COUNT, encoding='utf-8'
        )
        th_error.setLevel(logging.ERROR)  # 只处理ERROR及以上级别的日志

        # 日志格式
        formatter = logging.Formatter(LogConfig.LOG_FORMAT)

        # 设置文件里写入的格式
        th_base.setFormatter(formatter)
        th_error.setFormatter(formatter)

        # 把对象加到logger里
        logger.addHandler(th_base)
        logger.addHandler(th_error)

        return logger

    @staticmethod
    def info(msg):
        """
        普通日志
        :param msg: 需要记录的信息
        :return:
        """
        LogUtil.logger.info(msg.replace('\n', '\\n'), stacklevel=2)

    @staticmethod
    def error(msg):
        """
        错误日志
        :param msg: 需要记录的信息
        :return:
        """
        LogUtil.logger.error(msg, stacklevel=2)

# class LogUtil(object):
#     """
#     日志工具类
#     """
#     logger = None
#
#     @staticmethod
#     def init(process_name: str):
#         """
#         初始化
#         :param process_name: 进程名称
#         :return:
#         """
#         # 初始化日志对象
#         LogUtil.logger = LogUtil.get_logger(process_name)
#
#     @staticmethod
#     def get_logger(logger_name):
#         """
#         获得日志对象
#         :param logger_name: 日志名称
#         """
#         # 获得日志对象
#         logger = logging.getLogger(logger_name)
#         # 设置日志级别
#         logger.setLevel(LogConfig.LOG_LEVEL.upper())
#         # 获取日志文件名称
#         file_name = os.path.join(LogConfig.LOG_DIR, logger_name + '.logs')
#         # 指定间隔时间自动生成文件的处理器
#         th = handlers.TimedRotatingFileHandler(filename=str(file_name), when=LogConfig.WHEN,
#                                                backupCount=LogConfig.BACKUP_COUNT,
#                                                encoding='utf-8')
#         # 日志格式
#         formatter = logging.Formatter(LogConfig.LOG_FORMAT)
#         # 设置文件里写入的格式
#         th.setFormatter(formatter)
#         # 把对象加到logger里
#         logger.addHandler(th)
#         return logger
#
#     @staticmethod
#     def info(msg):
#         """
#         普通日志
#         :param msg: 需要记录的信息
#         :return:
#         """
#         LogUtil.logger.info(msg.replace('\n', '\\n'), stacklevel=2)
#
#     @staticmethod
#     def error(msg):
#         """
#         错误日志
#         :param msg: 需要记录的信息
#         :return:
#         """
#         LogUtil.logger.error(msg, stacklevel=2)

    @staticmethod
    def debug(msg):
        """
        调试日志
        :param msg: 需要记录的信息
        :return:
        """
        LogUtil.logger.debug(msg.replace('\n', '\\n'), stacklevel=2)

    @staticmethod
    def warn(msg):
        """
        警告日志
        :param msg: 需要记录的信息
        :return:
        """
        LogUtil.logger.warn(msg, stacklevel=2)

    @staticmethod
    def log_json(describe, **kwargs):
        """
        记录json格式
        :param describe: 说明
        :param kwargs: 参数信息
        :return:
        """
        msg = "{0}: {1}".format(describe, json.dumps(kwargs.get("kwargs") if "kwargs" in kwargs else kwargs,
                                                     ensure_ascii=False))
        LogUtil.logger.info(msg.replace('\n', '\\n'), stacklevel=2)


if __name__ == '__main__':
    LogUtil.init(process_name='chat-agent')
    args_dict = {'debug': True, 'level': logging.DEBUG, 'name': 'chat-agent',
                 'meta_info': {'host': 'localhost', 'port': 1208}}
    hh = [{'host': 'localhost', 'port': 1208}, {'host': 'localhost', 'port': 1208}]
    LogUtil.log_json(describe="-> 进行测试", kwargs=args_dict)
    LogUtil.log_json(describe="-> 进行测试", messages=hh)

