#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :toolset_entity.py
@Description  :
<AUTHOR>
@Date         :2024/10/29 19:55:29
'''

from pydantic import BaseModel, Field
from typing import Optional
from service_knowledge_manage.entity.knowledge_hub_entity import KnowledgeRetrivalInfo
from fastapi import APIRouter, HTTPException, Body, Request
class AgentInfo(BaseModel):
    agent_name: str = Field(..., example="finaicialRAG",description="智能体名称")
    description: str = Field(..., example="用于RAG检索", description="智能体描述")
    team_code: Optional[str] = Field(None, description="团队code列表", example="1")
    item_type: str = Field(..., example="子类别", description="智能体子类别")
    type_name: str = Field(..., example="子类别", description="智能体子类别名称")

class AgentTeamInfo(BaseModel):
    agent_name: str = Field(..., example="finaicialRAG",description="智能体名称")
    description: str = Field(..., example="用于RAG检索", description="智能体描述")
    team_code: str = Field(..., example="test2", description="团队code")

class ArrangeAgentInfo(BaseModel):
    agent_id: str = Field(..., example="678a0372887170f57a2f976f", description="智能体id")
    model_params: dict = Field(..., example={"model_uid":"qwen2-72B","max_token_length":5096,"temperature": 0.8,"history": 3},description="模型uid")
    prompt_id: str = Field('', example="PROMPT_123", description="提示词id")
    prompt: str = Field(..., example="你是一个数据分析专家",description="智能体提示词")
    promptHtml: str = Field('', example="html",description="智能体提示词html版")
    recall_setting: dict = Field(..., example={"rerank_model": "bge-reranker-large", "top_k": 5, "score": 0.2},description="召回设置")
    kb_list: list = Field(..., example=["6787518dfb8499f13bd3dd52", "678e2729d83327b234b00818"], description="知识库列表id")
    tool_list: list = Field(..., example=["Tool_1881524839102156800","Tool_1881524961131237376"], description="工具集id列表!")
    variable_list: list = Field([], example=["a","b"], description="变量列表")
    
class QueryAgentInfo(BaseModel):
    agent_name: str = Field(..., example="fin",description="智能体模糊查询名称")
    page: int = Field(..., example = 1, description="页码")
    page_size: int = Field(..., example = 2, description="页面大小")
    team_codes: Optional[list] = Field(None, description="团队code列表", example=["1","10001"])
    item_type: str = Field(..., example="agent-type", description="子类别code")

class QueryTeamAgentInfo(BaseModel):
    agent_name: str = Field(..., example="",description="智能体模糊查询名称")
    page: int = Field(..., example = 1, description="页码")
    page_size: int = Field(..., example = 10, description="页面大小")
    team_code: list = Field([], example=["1","10001"], description="团队code列表")

class TestAgentInfo(BaseModel):
    agent_id: str = Field(..., example="6735529d012d26f6f9b9c742",description="智能体id")
    input: str = Field(..., example = "未满十四周岁的未成年信息保护", description="对话输入")
    conversation_id: str = Field("", description="会话ID")
    agent_params: dict = Field({},description="智能体参数")
    system_prompts: str = Field("", description="系统角色提示语")
    prompt_id: str = Field("", description="提示词id")

class TestAgentInfo_v2(BaseModel):
    agent_id: str = Field(..., example="6735529d012d26f6f9b9c742", description="智能体id")
    agent_input: str = Field(..., example="未满十四周岁的未成年信息保护", description="对话输入")
    agent_params: dict = Field({},description="智能体参数")
    conversation_id: str = Field("",description="智能体参数")
    system_prompts: str = Field(
        "", description="系统角色提示语"
    )
    prompt_id: str = Field("", description="提示词id")


class TestAgentInfo_v1(BaseModel):
    account_id: str = Field("", example="6735529d012d26f6f9b9c742", description="account_id")
    agent_id: str = Field(..., example="6735529d012d26f6f9b9c742", description="智能体id")
    input: str = Field(..., example="未满十四周岁的未成年信息保护", description="对话输入")
    conversation_id: str = Field("", description="会话ID")
    agent_params: dict = Field({},description="智能体参数")
    system_prompts: str = Field(
        "", description="系统角色提示语"
    )
    prompt_id: str = Field("", description="提示词id")


class TypeDict(BaseModel):
    code: str = Field(..., example="agent-type", description="编码")
    name: str = Field(..., example="语言模型", description="中文名称")
    description: str = Field(..., example="这是一个语言模型类别", description="描述")

class TypeUpdateDict(BaseModel):
    id: str = Field(..., example="681affff8fbe31c31dd76365", description="主键id")
    code: str = Field(..., example="model-type2", description="编码")
    name: str = Field(..., example="语言模型2", description="中文名称")
    description: str = Field(..., example="这是一个语言模型类别2", description="描述")
    # cn_spell: str = Field("", example="", description="拼音缩写")
    # scope_type: str = Field("", example="", description="范围类型")
    status: str = Field(..., example="1", description="状态")

class TypeItemDict(BaseModel):
    code: str = Field(..., example="LLM-agent", description="编码")
    type_code: str = Field(..., example="agent-type", description="子类别")
    name: str = Field(..., example="语言模型", description="中文名称")
    description: str = Field(..., example="这是一个语言模型类别", description="描述")

class TypeItemUpdateDict(BaseModel):
    id: str = Field(..., example="681affff8fbe31c31dd76365", description="主键id")
    code: str = Field(..., example="model-type2", description="编码")
    type_code: str = Field(..., example="model-type2", description="编码")
    name: str = Field(..., example="语言模型2", description="中文名称")
    description: str = Field(..., example="这是一个语言模型类别2", description="描述")
    status: str = Field(..., example="1", description="状态")

class TypeItemDelete(BaseModel):
    id: str = Field(..., example="681affff8fbe31c31dd76365", description="主键id")
class TypeDelete(BaseModel):
    id: str = Field(..., example="681affff8fbe31c31dd76365", description="主键id")
