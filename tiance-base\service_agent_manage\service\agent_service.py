#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :agent_service.py
@Description  :
<AUTHOR>
@Date         :2024/10/30 09:47:38
'''
import os
import re
import json
import time

#from eval_type_backport.eval_type_backport import original_evaluate
from fastapi import requests

from base_utils.mongodb_util import MongodbUtil
from base_configs.api_config import ApiConfig
from base_configs.mongodb_config import CollectionConfig
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from base_utils.mongodb_util import MongodbUtil
from bson import ObjectId
import traceback
import datetime
import aioredis
from base_utils.page_util import PageUtil
from service_knowledge_manage.service.knowledge_service import KnowledgeService
from service_model_manage.service.chat_completion_service import OpenAILLMService
from service_model_manage.entity.chat_completion_entity import (
    ChatCompletionRequestParams,
)
from service_model_manage.service.model_family_service import ModelFamilyService
from base_utils.redis_util import RedisUtil
from service_permission_auth.model.team_model import Team_Model
from service_permission_auth.model.team_mem_model  import TeamMem_Model
from service_toolset_manage.service.toolset_service import Toolset_service
from sqlalchemy.orm import Session
from service_prompt_manage.service.prompt_service import PromptService


class AgentService(object):


    # @staticmethod
    # async def agent_has_tool_or_kb_import(agent_content_dict: dict):
    #     """
    #     判断该智能体文件是否有工具或者知识库
    #     :param agent_content_dict: 智能体基础数据与编排数据
    #     :return:
    #     """
    #     try:
    #         has_tool = False
    #         has_kb = False
    #         if not agent_content_dict.get("agent_arrange"):
    #             return {"has_tool": has_tool, "has_kb": has_kb, "mode": "import"}
    #         if agent_content_dict["agent_arrange"].get("tool_list"):
    #             has_tool = True
    #         if agent_content_dict["agent_arrange"].get("kb_list"):
    #             has_kb = True
    #         return {"has_tool": has_tool, "has_kb": has_kb, "mode": "import"}
    #
    #     except Exception as e:
    #         LogUtil.error(f"查询智能体文件是否有工具或者知识库 异常: {str(traceback.format_exc())}")
    #         raise e

    @staticmethod
    async def agent_has_tool_or_kb_export(agent_id: str):
        """
        判断该智能体是否有工具或者知识库
        :param agent_id: 智能体id
        :return: 一个字典判断是否有工具或者知识库
        """
        try:
            # 查询智能体的编排信息
            cursor = MongodbUtil.query_docs_by_condition(
                collection_name=CollectionConfig.ARRANGE_AGENT_COLLECTION,
                search_condition={'_id': ObjectId(agent_id)}
            )
            agent_arrange = dict({})
            for doc in cursor:
                agent_arrange = doc

            has_tool = False
            has_kb = False
            if not agent_arrange:
                return {"has_tool": has_tool, "has_kb": has_kb, "mode": "export"}

            if agent_arrange.get("tool_list", None):
                has_tool = True
            if agent_arrange.get("kb_list", None):
                has_kb = True
            return {"has_tool": has_tool, "has_kb": has_kb, "mode": "export"}

        except Exception as e:
            LogUtil.error(f"查询智能体是否有工具或者知识库 异常: {str(traceback.format_exc())}")
            raise e


    @staticmethod
    async def agent_import(agent_content_dict: dict, is_save_kn: bool, is_save_tool: bool,
                           account_id: str, team_code: str, db: Session):
        """
        导入智能体
        :param agent_content_dict:  智能体基础数据与编排数据
        :param is_save_kn:  是否导入知识库
        :param is_save_tool: 是否导入工具
        :param account_id: 用户id
        :param team_code: 团队id，如果为空则为个人工具
        :param db: 数据库连接对象，用于判断源工具的用户是否是管理员
        :return:
        """
        try:
            if team_code is None:
                team_code = ""

            create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # 插入智能体基础信息到MongoDB
            agent_content_dict["agent_doc"]["agent_name"] += "_导入"
            agent_content_dict["agent_doc"]["create_time"] = create_time
            agent_content_dict["agent_doc"]["account_id"] = account_id
            agent_content_dict["agent_doc"]["status"] = 0
            agent_content_dict["agent_doc"]["team_code"] = team_code
            agent_content_dict["agent_arrange"]["account_id"] = account_id

            # 限制插入的表单的格式
            std_agent = ["agent_name", "description", "create_time", "account_id", "team_code", "type_name", "code",
                         "status"]
            std_agent_arrange = ["model_params", "prompt", "recall_setting", "kb_list", "tool_list", "account_id",
                                 "variable_list",
                                 "promptHtml", "prompt_id"]
            agent_key = [key for key in agent_content_dict["agent_doc"].keys()]
            for k in agent_key:
                if k not in std_agent:
                    agent_content_dict["agent_doc"].pop(k, None)
                # 限制长度
                elif k == "description" and len(agent_content_dict["agent_doc"].get(k)) > 500:
                    raise Exception("description过长，不应超过500词")
                elif k != "description" and isinstance(agent_content_dict["agent_doc"].get(k), str) and len(agent_content_dict["agent_doc"].get(k)) > 100:
                    raise Exception("{}过长".format(k))
            agent_arrange_key = [key for key in agent_content_dict["agent_arrange"].keys()]
            for k in agent_arrange_key:
                if k not in std_agent_arrange:
                    agent_content_dict["agent_arrange"].pop(k, None)
                elif k in ["prompt", "promptHtml"] and len(agent_content_dict["agent_arrange"].get(k)) > 10000:
                    raise Exception("{}过长，不应超过10000词".format(k))
                elif k not in ["prompt", "kb_list", "tool_list"] and isinstance(agent_content_dict["agent_arrange"].get(k), str) and len(agent_content_dict["agent_arrange"].get(k)) > 100:
                    raise Exception("{}过长".format(k))

            LogUtil.info("开始插入智能体，{}深拷贝工具，{}深拷贝知识库。".format("需要" if is_save_tool else "不需要", "需要" if is_save_kn else "不需要"))

            # 深度复制一份prompt
            prompt_id_old = agent_content_dict["agent_arrange"].get("prompt_id", "")
            if prompt_id_old:
                # 如果prompt_id存在且不为空，则复制一份提示词
                prompt_id_new = PromptService.copy_prompt_by_id(db=db, prompt_id_old=prompt_id_old, prompt_name="草稿",
                                                               agent_id="wait_for_update", workflow_id="",
                                                               account_id=account_id, team_code=team_code, status=2)
            else:
                prompt_id_new = ""
            agent_content_dict["agent_arrange"]["prompt_id"] = prompt_id_new


            # 插入智能体编排信息到MongoDB
            # 是否需要深拷贝工具
            tool_report = {"tool_admin": [], "tool_exist": [], "tool_delete": [], "tool_deep_copy": []}
            kb_report = {"kb_exist": [], "kb_delete": [], "kb_deep_copy": []}
            if not is_save_tool:
                agent_content_dict["agent_arrange"]["tool_list"] = []
            else:
                LogUtil.info("共有{}个工具需要拷贝".format(len(agent_content_dict["agent_arrange"]["tool_list"])))
                tool_delete_index = []
                for tool_index_id in range(len(agent_content_dict["agent_arrange"]["tool_list"])):
                    tool_id = agent_content_dict["agent_arrange"]["tool_list"][tool_index_id]
                    tool_q = MongodbUtil.query_doc_by_id(CollectionConfig.TOOL_COLLECTION, tool_id)

                    # 如果是管理员工具，则不用复制
                    user_attribute = await ModelFamilyService.get_user_attribute_by_account_id(db, tool_q.get("account_id"))
                    if user_attribute:
                        LogUtil.info("工具{}是管理源工具，直接使用".format(tool_id))
                        tool_report["tool_admin"].append(tool_q.get("tool_name"))
                        continue

                    # 源工具是属于目标用户或目标团队，不用复制
                    if tool_q is not None:
                        if team_code == "":
                            # 对于个人工具的处理情况
                            if tool_q.get("account_id") == account_id:
                                LogUtil.info("个人工具{}不能自己导入自己".format(tool_id))
                                tool_report["tool_exist"].append(tool_q.get("tool_name"))
                                continue
                        else:
                            # 对于团队工具的情况
                            if tool_q.get("team_code") == team_code:
                                LogUtil.info("团队工具{}不能自己导入自己".format(tool_id))
                                tool_report["tool_exist"].append(tool_q.get("tool_name"))
                                continue
                    else:
                        # 这个工具在工具库中找不到
                        LogUtil.info("工具{}不存在，取消复制".format(tool_id))
                        tool_delete_index.append(tool_index_id)
                        tool_report["tool_delete"].append(tool_id)
                        continue


                    # 源工具已经在目标用户或者目标团队导入过一次，不用再复制
                    if team_code == "":
                        tool_f = MongodbUtil.query_docs_by_condition(CollectionConfig.TOOL_COLLECTION,
                                {"from_tool": tool_id, "account_id": account_id, "team_code": team_code})
                    else:
                        tool_f = MongodbUtil.query_docs_by_condition(CollectionConfig.TOOL_COLLECTION,
                                                          {"from_tool": tool_id, "team_code": team_code})
                    ori_tool = dict({})
                    for doc in tool_f:
                        ori_tool = doc
                    if ori_tool:
                        if team_code == "":
                            LogUtil.info("个人工具{}不能重复导入".format(tool_id))
                        else:
                            LogUtil.info("团队工具{}不能重复导入".format(tool_id))
                        agent_content_dict["agent_arrange"]["tool_list"][tool_index_id] = ori_tool.get("_id")
                        tool_report["tool_exist"].append(ori_tool.get("tool_name"))
                        continue

                    # 复制工具
                    new_tool_id, ori_name = await Toolset_service.copy_tool_by_id(tool_id=tool_id, account_id=account_id,
                                                                        team_code=team_code, status=1)
                    if new_tool_id != "":
                        agent_content_dict["agent_arrange"]["tool_list"][tool_index_id] = new_tool_id
                        tool_report["tool_deep_copy"].append(ori_name)
                        LogUtil.info("插入了一个工具，原工具id为{}，新工具id为{}".format(tool_id, new_tool_id))
                    else:
                        LogUtil.info("发生逻辑错误，该工具{}在前段代码能找到，在这段代码找不到".format(tool_id))
                        raise Exception("发生了逻辑错误")
                # 若在工具库中找不到对应的工具，则不复制智能体的工具索引
                for delete_index in reversed(tool_delete_index):
                    del agent_content_dict["agent_arrange"]["tool_list"][delete_index]
            # 是否需要深拷贝知识库
            if not is_save_kn:
                agent_content_dict["agent_arrange"]["kb_list"] = []
            else:
                LogUtil.info("共有{}个知识库需要拷贝".format(len(agent_content_dict["agent_arrange"]["kb_list"])))
                kb_delete_index = []
                for kb_index_id in range(len(agent_content_dict["agent_arrange"]["kb_list"])):
                    kb_id = agent_content_dict["agent_arrange"]["kb_list"][kb_index_id]
                    kb_q = MongodbUtil.query_doc_by_id(CollectionConfig.KB_COLLECTION, ObjectId(kb_id))

                    # 源知识库是属于目标用户或目标团队，不用复制
                    if kb_q is not None:
                        if team_code == "":
                            # 对于个人知识库的情况
                            if kb_q.get("account_id") == account_id:
                                LogUtil.info("个人工具{}不能自己导入自己".format(kb_id))
                                kb_report["kb_exist"].append(kb_q.get("kb_name"))
                                continue
                        else:
                            # 对于团队知识库的情况
                            if kb_q.get("team_code") == team_code:
                                LogUtil.info("团队工具{}不能自己导入自己".format(kb_id))
                                kb_report["kb_exist"].append(kb_q.get("kb_name"))
                                continue
                    else:
                        # 这个知识库不存在
                        LogUtil.info("知识库{}不存在，取消复制".format(kb_id))
                        kb_delete_index.append(kb_index_id)
                        kb_report["kb_delete"].append(kb_id)
                        continue

                    # 源知识库已经在目标用户或者目标团队导入过一次，不用再复制
                    if team_code == "":
                        kb_f = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION,
                                            {"from_kb": kb_id, "account_id": account_id, "team_code": team_code})
                    else:
                        kb_f = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION,
                                                            {"from_kb": kb_id, "team_code": team_code})
                    ori_kb = dict({})
                    for doc in kb_f:
                        ori_kb = doc
                    if ori_kb:
                        LogUtil.info("知识库{}不能重复导入".format(kb_id))
                        agent_content_dict["agent_arrange"]["kb_list"][kb_index_id] = str(ori_kb.get("_id"))
                        kb_report["kb_exist"].append(ori_kb.get("kb_name"))
                        continue

                    # 复制知识库
                    new_kb_id, ori_name = await KnowledgeService.copy_kb_by_id(kb_id=kb_id, account_id=account_id,
                                                                            team_code=team_code)
                    if new_kb_id != "":
                        agent_content_dict["agent_arrange"]["kb_list"][kb_index_id] = new_kb_id
                        kb_report["kb_deep_copy"].append(ori_name)
                        LogUtil.info("插入了一个知识库，原知识库id为{}，新知识库id为{}".format(kb_id, new_kb_id))
                    else:
                        LogUtil.info("发生逻辑错误，这个知识库在前段代码找得到，这里找不到")
                        raise Exception("发生逻辑错误")
                # 若在知识库库中找不到对应的知识库，则不复制智能体的知识库索引
                for delete_index in reversed(kb_delete_index):
                    del agent_content_dict["agent_arrange"]["kb_list"][delete_index]



            # 插入智能体基础信息表和编排表
            insert_result = MongodbUtil.insert_one(CollectionConfig.AGENT_COLLECTION, agent_content_dict["agent_doc"])
            agent_id_new = insert_result.inserted_id
            agent_content_dict["agent_arrange"]["_id"] = ObjectId(agent_id_new)
            MongodbUtil.insert_one(CollectionConfig.ARRANGE_AGENT_COLLECTION, agent_content_dict["agent_arrange"])
            LogUtil.info("成功插入一条智能体基础信息和编排信息，id为{}".format(agent_id_new))

            # 若前段代码有插入prompt信息，更新agent_id
            if prompt_id_new:
                update_agent_result = PromptService.update_agent_by_prompt_id(db=db, prompt_id=prompt_id_new, agent_id=agent_id_new)
                LogUtil.info("成功复制了一个提示词，id为{}".format(update_agent_result))

            # 处理返回信息
            report_m = ""
            temp_report = ""
            for tool_admin in tool_report["tool_admin"]:
                temp_report += " {} ".format(tool_admin)
            if temp_report != "":
                report_m += "工具{}为内置工具，直接使用\n".format(temp_report)

            temp_report = ""
            for tool_exist in tool_report["tool_exist"]:
                temp_report += " {} ".format(tool_exist)
            if temp_report != "":
                report_m += "工具{}已经存在，直接使用\n".format(temp_report)

            temp_report = ""
            for tool_deep_copy in tool_report["tool_deep_copy"]:
                temp_report += " {} ".format(tool_deep_copy)
            if temp_report != "":
                report_m += "工具{}成功复制\n".format(temp_report)

            temp_report = ""
            for tool_delete in tool_report["tool_delete"]:
                temp_report += " {} ".format(tool_delete)
            if temp_report != "":
                report_m += "id为{}的工具不存在\n".format(temp_report)

            temp_report = ""
            for kb_exist in kb_report["kb_exist"]:
                temp_report += " {} ".format(kb_exist)
            if temp_report != "":
                report_m += "知识库{}已经存在，直接使用\n".format(temp_report)

            temp_report = ""
            for kb_deep_copy in kb_report["kb_deep_copy"]:
                temp_report += " {} ".format(kb_deep_copy)
            if temp_report != "":
                report_m += "知识库{}成功复制\n".format(temp_report)

            temp_report = ""
            for kb_delete in kb_report["kb_delete"]:
                temp_report += " {} ".format(kb_delete)
            if temp_report != "":
                report_m += "id为{}的知识库不存在\n".format(temp_report)

            return RetUtil.return_ok(
                data={"message": ("导入个人智能体成功\n" if team_code == "" else "导入团队智能体成功\n") + report_m}
            )
        except Exception as e:
            LogUtil.error(f"导入智能体异常: {str(traceback.format_exc())}")
            raise e


    @staticmethod
    async def agent_export(agent_id: str, is_save_kn: bool, is_save_tool: bool):
        """
        导出智能体
        :param agent_id: 智能体id
        :param is_save_kn: 保存知识库数据
        :param is_save_tool: 保存工具数据
        :return: 是否导出成功
        """
        try:
            original_agent = dict({})
            # 查询智能体的基础信息
            cursor = MongodbUtil.query_docs_by_condition(
                collection_name=CollectionConfig.AGENT_COLLECTION,
                search_condition={'_id': ObjectId(agent_id)}
            )
            agent_doc = dict({})
            for doc in cursor:
                agent_doc = doc

            # 复制基础信息并插入新文档
            agent_doc.pop("_id", None)
            agent_doc['status'] = 0

            # 查询智能体的编排信息
            cursor = MongodbUtil.query_docs_by_condition(
                collection_name=CollectionConfig.ARRANGE_AGENT_COLLECTION,
                search_condition={'_id': ObjectId(agent_id)}
            )
            agent_arrange = dict({})
            for doc in cursor:
                agent_arrange = doc

            agent_arrange.pop("_id", None)
            # 若选择不保存知识库或者工具，则置空
            if not is_save_kn:
                agent_arrange["kb_list"] = []
            if not is_save_tool:
                agent_arrange["tool_list"] = []

            original_agent["agent_doc"] = agent_doc
            original_agent["agent_arrange"] = agent_arrange

            return original_agent
        except Exception as e:
            LogUtil.error(f"导出智能体异常: {str(traceback.format_exc())}")
            raise e




    @staticmethod
    async def create_agent(agent_name, desripition, account_id,code,type_name):
        try:
            create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if code == '':
                code = '0'
                type_name = '其他'
            insert_result = MongodbUtil.insert_one(CollectionConfig.AGENT_COLLECTION,
                                                   {'agent_name': agent_name, 'description': desripition,
                                                    'create_time': create_time, 'account_id': account_id,'status': 0,'code':code,'type_name':type_name})
            if insert_result is not None:
                insert_result = str(insert_result.inserted_id)
            return "新增智能体成功", insert_result

        except Exception as e:
            LogUtil.error(f"新增智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def copy_agent(agent_id: str, db: Session) -> bool:
        """
        复制智能体
        :param agent_id: 原智能体ID
        :param db: 数据库对象
        :return: 新智能体ID或错误信息
        """
        try:
            # 查询原智能体的基础信息
            cursor = MongodbUtil.query_docs_by_condition(
                collection_name=CollectionConfig.AGENT_COLLECTION,
                search_condition={'_id': ObjectId(agent_id)}
            )
            original_agent = dict({})
            for doc in cursor:
                original_agent = doc

            # 查询原智能体的编排信息
            cursor = MongodbUtil.query_docs_by_condition(
                collection_name=CollectionConfig.ARRANGE_AGENT_COLLECTION,
                search_condition={'_id': ObjectId(agent_id)}
            )
            original_arrange_info = dict({})
            for doc in cursor:
                original_arrange_info = doc.copy()
                original_arrange_info.pop("_id", None)

            # 复制基础信息并插入新文档
            doc_copy = original_agent.copy()
            doc_copy.pop("_id", None)  # 移除原始 ID
            new_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            doc_copy['agent_name'] = f"{doc_copy['agent_name']}_copy_{new_time}"
            doc_copy['create_time'] = new_time
            doc_copy['status'] = 0


            # 复制prompt
            # 深度复制一份prompt
            prompt_id_new = ""
            if original_arrange_info != {}:
                prompt_id_old = original_arrange_info.get("prompt_id", "")
                if prompt_id_old:
                    # 如果prompt_id存在且不为空，则复制一份提示词
                    prompt_id_new = PromptService.copy_prompt_by_id(db=db, prompt_id_old=prompt_id_old, prompt_name="草稿",
                                                                    agent_id="wait_for_update", workflow_id="",
                                                                    account_id=original_arrange_info.get("account_id", ""),
                                                                    team_code=original_arrange_info.get("team_code", ""),
                                                                    status=2)
                original_arrange_info["prompt_id"] = prompt_id_new


            insert_result = MongodbUtil.insert_one(CollectionConfig.AGENT_COLLECTION, doc_copy)
            new_agent_id = insert_result.inserted_id
            LogUtil.info(f"新智能体基础信息插入成功，ID: {new_agent_id}")

            if original_arrange_info != {}:
                # 更新编排信息中的 agent_id 和 account_id
                original_arrange_info["_id"] = ObjectId(new_agent_id)
                # 插入新编排信息
                arrange_result = MongodbUtil.insert_one(
                    CollectionConfig.ARRANGE_AGENT_COLLECTION,
                    original_arrange_info
                )
                # 更新前段代码插入的prompt信息
                if prompt_id_new:
                    update_agent_result = PromptService.update_agent_by_prompt_id(db=db, prompt_id=prompt_id_new,
                                                                                  agent_id=new_agent_id)
                    LogUtil.info("成功复制了一个提示词，id为{}".format(update_agent_result))

                if arrange_result:
                    LogUtil.info(f"智能体编排信息复制成功，新智能体ID: {new_agent_id}")
                else:
                    LogUtil.error("复制编排信息失败")
                    return False
            else:
                LogUtil.info("原智能体没有编排信息，跳过编排信息复制")

            return True

        except Exception as e:
            LogUtil.error(f"复制智能体异常: {str(traceback.format_exc())}")
            return False


    @staticmethod
    async def delete_agent(agent_id):
        try:
            MongodbUtil.del_docs_by_condition(CollectionConfig.AGENT_COLLECTION,
                                              {'_id': ObjectId(agent_id)})
            MongodbUtil.del_docs_by_condition(CollectionConfig.ARRANGE_AGENT_COLLECTION,
                                              {'_id': ObjectId(agent_id)})
            return "删除智能体成功"
        except Exception as e:
            LogUtil.error(f"删除智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def arrange_agent(agent_id, account_id, model_params, prompt, recall_setting, kb_list, tool_list,
                            variable_list, promptHtml,prompt_id):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.ARRANGE_AGENT_COLLECTION,
                                                         search_condition={'_id': ObjectId(agent_id)})
            for _ in result:
                MongodbUtil.update_one(CollectionConfig.ARRANGE_AGENT_COLLECTION, {'_id': ObjectId(agent_id)},
                                       update_operation={'$set':
                                           {
                                               'model_params': model_params,
                                               'prompt': prompt,
                                               'recall_setting': recall_setting,
                                               'kb_list': kb_list,
                                               'tool_list': tool_list,
                                               'account_id': account_id,
                                               'variable_list': variable_list,
                                               'promptHtml': promptHtml,
                                               'prompt_id': prompt_id
                                           }
                                       })
                return "更新编排智能体成功"
            else:
                MongodbUtil.insert_one(CollectionConfig.ARRANGE_AGENT_COLLECTION, {
                    '_id': ObjectId(agent_id),
                    'model_params': model_params,
                    'prompt': prompt,
                    'recall_setting': recall_setting,
                    'kb_list': kb_list,
                    'tool_list': tool_list,
                    'account_id': account_id,
                    'variable_list': variable_list,
                    'promptHtml': promptHtml,
                    'prompt_id': prompt_id})
                return "首次创建编排智能体成功"

        except Exception as e:
            LogUtil.error(f"编排智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def test_agent(params):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.ARRANGE_AGENT_COLLECTION,
                                                         search_condition={'_id': ObjectId(params.agent_id)})
            for item in result:
                model_uid = item['model_uid']
                prompt = item['prompt']
                kb_list = item['kb_list']
                tool_list = item['tool_list']

            prompt_tool = f"""
    # 角色
    你是一个精通各种工具的专家，能够准确地分析问题并选择合适的工具来解决。

    您可以使用的工具如下:{tool_list}
    ## 技能
    ### 技能 1: 工具匹配
    1. 当用户提出问题时，仔细分析问题需求，匹配可用工具。



    """

            LogUtil.info(str(tool_list))
            LogUtil.info(str(len(tool_list)))
            if len(tool_list) > 0:
                prompt_tool = prompt_tool
            # 业务逻辑处理
            openAILLMService = OpenAILLMService()
            response_content = ""
            result = openAILLMService.chunk_chat(
                request=ChatCompletionRequestParams(question=params.input,
                                                    system_prompts=prompt_tool,
                                                    chatbot=[],
                                                    history=3,
                                                    max_token_length=4096,
                                                    temperature=0.8,
                                                    model_uid=model_uid, )
            )
            LogUtil.info(f"大模型输出工具为:{result}")
            return result
        except Exception as e:
            LogUtil.error(f"测试智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def use_agent(params):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.ARRANGE_AGENT_COLLECTION,
                                                         search_condition={'_id': ObjectId(params.agent_id)})
            for item in result:
                model_uid = item['model_uid']

                prompt_tool = """
                # 角色
                你是一个能够准确判断用户意图的智能体。
                ## 技能
                ### 技能
                1：判断用户意图 
                1. 仔细分析用户的问题。 
                2. 如果用户问的是时间问题，在 JSON 格式中，“params”字段返回为“获取时区时间”。 
                3. 如果用户问的是天气相关问题，“params”字段返回为“获取时区时间”。 
                4. 如果既不是问时间也不是问天气，“params”字段则为“获取地区天气”。
                ## 限制 - 严格按照 JSON 格式输出结果。 - 只根据用户问题判断意图，不进行其他无关操作。   
                """

                example_prompt = """
                    ## 样例:
                    样例一：
                    问题:长沙市天气怎么样
                    输出:{'params': '获取地区天气'}
                    样例二：
                    问题:今天多少号
                    输出:{'params': '获取时区时间'}
                    样例
                """

                prompt_tool = prompt_tool + example_prompt
                # 业务逻辑处理
                openAILLMService = OpenAILLMService()
                result = openAILLMService.chunk_chat(
                    request=ChatCompletionRequestParams(question=params.input,
                                                        system_prompts=prompt_tool,
                                                        chatbot=[],
                                                        history=3,
                                                        max_token_length=4096,
                                                        temperature=0.8,
                                                        model_uid=model_uid)
                )
                LogUtil.info(f"大模型输出工具为:{result}")
                return json.loads(str(result))
        except Exception as e:
            LogUtil.error(f"测试智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def list_knowledge(account_id: str, team_codes: list):
        try:
            results = []
            if team_codes:
                result = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION,
                                                             search_condition={"team_code": {"$in": team_codes}})
            else:
                result = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION,
                                                             search_condition={"account_id": account_id, "team_code": ""})
            for item in result:
                item["_id"] = str(item["_id"])
                results.append(item)
            # return RetUtil.return_ok(result)
            return results
        except Exception as e:
            LogUtil.error(f"列出知识库列表异常: {str(traceback.format_exc())}")

    @staticmethod
    async def list_tool():
        try:
            results = []
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.TOOL_COLLECTION, search_condition={})
            for item in result:
                tool_params = []
                tool_name = item['tool_name']
                # LogUtil.info(f"查询工具名称:{tool_name}")
                schema = item['schema']
                # LogUtil.info(f"得到的schema:{str(schema)}")
                schema = json.loads(schema)
                # LogUtil.info(f"得到的schema:{str(schema)}")
                # LogUtil.info(f"得到的schema:{str(type(schema))}")
                for path, path_item in schema.get('paths', {}).items():
                    for method, operation in path_item.items():
                        if 'requestBody' in operation and 'content' in operation['requestBody']:
                            for media_type, media_details in operation['requestBody']['content'].items():
                                schema1 = media_details.get('schema', {})
                                properties = schema1.get('properties', {})
                                for prop_name, prop_details in properties.items():
                                    tool_params.append(prop_name)
                LogUtil.info(f"工具参数列表:{tool_params}")
                LogUtil.info(f"得到的schema:{str(schema)}")
                server_url = schema["servers"][0]['url']
                api_path = next(iter(schema['paths']))
                api_url = server_url + api_path
                # results.append(item['_id'])
                LogUtil.info(str(tool_params))
                results.append({"tool_name": item['tool_name'], "tool_params": tool_params, "tool_url": api_url})
            # return RetUtil.return_ok(result)
            return results
        except Exception as e:
            LogUtil.error(f"列出工具列表异常: {str(traceback.format_exc())}")

    @staticmethod
    async def get_first_running_model():
        try:
            # 获取运行中、非推理、未删除的LLM模型
            non_inference_model = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                                    {"model_type": "LLM", "status": "running",
                                                                     "is_think": False, "is_delete": False, "is_external": False,
                                                                     "modalities": {"$exists": True, "$nin": ["image"]}})
            for model in non_inference_model:
                # 返回第一个符合条件模型的id与uid
                LogUtil.info(f"调用的LLM模型id为{model['_id']},LLM模型模型名称为{model['model_uid']}")
                return str(model["_id"]), model["model_uid"]

            # 如果没有运行中、非推理、未删除的LLM模型，那就搜索运行中、推理、未删除的LLM模型
            inference_model = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                                      {"model_type": "LLM", "status": "running",
                                                                       "is_think": True, "is_delete": False, "is_external":False,
                                                                       "modalities": {"$exists": True, "$nin": ["image"]}})
            for model in inference_model:
                # 返回第一个符合条件模型的id与uid
                LogUtil.info(f"调用的LLM模型id为{model['_id']},LLM模型名称为{model['model_uid']}")
                return str(model["_id"]), model["model_uid"]

            # 若没有符合上面两个条件的数据，搜索没有来得及添加is_inference的运行中、未删除的LLM模型
            models = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                                      {"model_type": "LLM", "status": "running",
                                                                       "is_delete": False, "is_external":False})
            for model in models:
                # 返回第一个符合条件模型的id与uid
                LogUtil.info(f"调用的LLM模型id为{model['_id']},LLM模型名称为{model['model_uid']}")
                return str(model["_id"]), model["model_uid"]

            models = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                         {"model_type": "LLM", "status": "running", "is_think": False,
                                                          "is_delete": False, "is_external": True})
            for model in models:
                # 返回第一个符合条件模型的id与uid
                LogUtil.info(f"调用的LLM模型id为{model['_id']},LLM模型名称为{model['model_uid']}")
                return str(model["_id"]), model["model_uid"]

            models = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                         {"model_type": "LLM", "status": "running", "is_think": True,
                                                          "is_delete": False, "is_external": True})
            for model in models:
                # 返回第一个符合条件模型的id与uid
                LogUtil.info(f"调用的LLM模型id为{model['_id']},LLM模型名称为{model['model_uid']}")
                return str(model["_id"]), model["model_uid"]

            return False, False

        except Exception as e:
            detail = f"获取LLM模型失败：{str(traceback.format_exc())}"
            LogUtil.info(detail)
            return False, False

    @staticmethod
    async def get_running_model_list(model_type: str):
        try:
            external_model_list = []
            internal_model_list = []
            # 获取运行中、外部模型
            external_model = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                                      {"model_type": model_type, "status": "running",
                                                                       "is_delete": False, "is_external": True})
            for model in external_model:
                model_info = {
                    "model_name": model["model_uid"] if model.get("model_uid", None) else model["model_name"],
                    "id": str(model["_id"]),
                    "model_uid": model["model_uid"],
                    "is_external": model["is_external"]
                    }
                if model_type == "embedding":
                    dimension = model["max_tokens"]
                    model_info["dimension"] = dimension
                if model_type == "LLM":
                    model_info["max_tokens"] = model["max_tokens"]
                external_model_list.append(model_info)

            # 如果没有运行中、内部模型
            internal_model = MongodbUtil.query_docs_by_condition(CollectionConfig.MODEL_RUN_COLLECTION,
                                                                  {"model_type": model_type, "status": "running",
                                                                   "is_delete": False, "is_external": False})
            for model in internal_model:
                model_info = {
                    "model_name": model["model_uid"],
                    "id": str(model["_id"]),
                    "model_uid": model["model_uid"],
                    "is_external": model["is_external"]
                }
                if model_type == "embedding":
                    result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_FAMILY_COLLECTION, doc_id=model["model_id"])
                    dimension = result["model_emb_details"]["model_embedding_dimension"]
                    model_info["dimension"] = dimension
                if model_type == "LLM":
                    model_info["max_tokens"] = model.get("max_tokens", None)
                internal_model_list.append(model_info)

            return {"name": "内部", "children": internal_model_list}, {"name": "外部", "children": external_model_list}

        except Exception as e:
            detail = f"获取LLM模型失败：{str(traceback.format_exc())}"
            LogUtil.info(detail)
            return [], []

    @staticmethod
    async def running_LLM_model():
        try:
            results = []
            result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.MODEL_RUN_COLLECTION, search_condition={})
            for item in result:
                # LogUtil.info(f"item:{item}")
                if item["status"] == "running" and item["model_type"] == "LLM" and item["is_delete"] == False:
                    if item["is_external"] == False:
                        data = {"model_name": item["model_uid"], "id": str(item["_id"]), "model_uid": item["model_uid"],"is_external": item["is_external"]}
                    else:
                        data = {"model_name": item["model_name"], "id": str(item["_id"]),"model_uid": item["model_uid"],"is_external": item["is_external"]}
                    results.append(data)
            # return RetUtil.return_ok(result)
            return results
        except Exception as e:
            LogUtil.error(f"运行中模型列表异常: {str(traceback.format_exc())}")

    @staticmethod
    async def agent_arrange_info(agent_id: str):
        try:
            result = None
            results = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.ARRANGE_AGENT_COLLECTION,
                                                          search_condition={"_id": ObjectId(agent_id)})
            for item in results:
                LogUtil.info(f"item:{item}")
                # 如果模型配置参数中不存在presence_penalty与frequency_penalty惩罚参数，设置默认值为1
                if item['model_params'].get("presence_penalty", None) is None:
                    item['model_params']["presence_penalty"] = 0
                if item['model_params'].get("frequency_penalty", None) is None:
                    item['model_params']["frequency_penalty"] = 0
                return ({
                    "model_params": item['model_params'],
                    "prompt": item['prompt'],
                    "recall_setting": item['recall_setting'],
                    "kb_list": item['kb_list'],
                    "tool_list": item['tool_list'],
                    "account_id": item['account_id'],
                    "variable_list": item.get('variable_list', []),
                    "promptHtml": item.get('promptHtml', ''),
                    "prompt_id": item.get('prompt_id', '')
                })
            return result
        except Exception as e:
            LogUtil.error(f"智能体编排信息异常: {str(traceback.format_exc())}")

    @staticmethod
    async def agent_exist(agent_name: str, account_id: str):
        try:
            condition = {'agent_name': agent_name, 'account_id': account_id}
            agent = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.ARRANGE_AGENT_COLLECTION,
                                                        search_condition=condition)
            for _ in agent:
                return True
            return False
        except Exception as e:
            LogUtil.error(f"智能体查询异常: {str(traceback.format_exc())}")

    @staticmethod
    async def query_agent(agent_name, page, page_size, account_id,code):
        try:
            from datetime import datetime
            if code!="":
                condition = {"agent_name": {"$regex": f"{re.escape(agent_name)}(\\_.*)?", "$options": "i"},
                             "code": code,
                             "account_id": account_id,"$or": [
            {"team_code": {"$exists": False}},  # team_code 字段不存在
            {"team_code": {"$eq": None}},      # team_code 字段为空
            {"team_code": {"$eq": ""}}         # team_code 字段为空字符串（如果需要检查空字符串）
        ]}
            else:
                condition = {"agent_name": {"$regex": f"{re.escape(agent_name)}(\\_.*)?", "$options": "i"},
                             "account_id": account_id, "$or": [
                        {"team_code": {"$exists": False}},  # team_code 字段不存在
                        {"team_code": {"$eq": None}},  # team_code 字段为空
                        {"team_code": {"$eq": ""}}  # team_code 字段为空字符串（如果需要检查空字符串）
                    ]}

            LogUtil.info(str(condition))
            result = []
            results = MongodbUtil.query_docs_by_condition_pagination(CollectionConfig.AGENT_COLLECTION,
                                                          search_condition=condition,
                                                          page=page, page_size=page_size, sort_field="create_time")
            len_result = MongodbUtil.count_documents_by_condition(CollectionConfig.AGENT_COLLECTION, condition)
            def parse_upload_time(upload_time_str):
                return datetime.strptime(upload_time_str, "%Y-%m-%d %H:%M:%S")

            print("results",results)
            for item in results:
                temp=item.get("code","")
                result.append(
                    {"agent_id": str(item["_id"]), "agent_name": item["agent_name"], "description": item["description"],
                     "account_id": item["account_id"],"status":item["status"],"code": temp})
            LogUtil.info(str(result))
            return {"total": len_result, "result": result}
        except Exception as e:
            LogUtil.error(f"查询智能体列表异常: {str(e)}")
            raise e

    @staticmethod
    async def list_knowledge_test(account_id):
        try:
            results = []
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION,
                                                         search_condition={"account_id": account_id})
            for item in result:
                results.append(item['kb_name'])
            # return RetUtil.return_ok(result)
            return results
        except Exception as e:
            LogUtil.error(f"列出知识库列表异常: {str(traceback.format_exc())}")

    @staticmethod
    async def update_agent(agent_id, agent_name, description,code):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.AGENT_COLLECTION,
                                                         search_condition={"_id": ObjectId(agent_id)})
            LogUtil.info(f"查询智能体搜索结果: {result}")
            for item in result:
                MongodbUtil.update_one(CollectionConfig.AGENT_COLLECTION, query_filter={"_id": ObjectId(agent_id)},
                                       update_operation={
                                           '$set': {
                                               'agent_name': agent_name,
                                               'description': description,
                                               'code': code,
                                           }
                                       })
            return "更新智能体基础信息成功"

        except Exception as e:
            LogUtil.error(f"更新智能体信息异常: {str(traceback.format_exc())}")
            return False

    @staticmethod
    async def tool_query_by_user(account_id_list: list[str] = [], team_codes: list[str] = None):
        """
        查询用户工具列表（支持多个个人工具和多个团队工具）
        :param account_id_list: 用户ID列表
        :param tool_name: 工具名称（支持模糊查询）
        :param team_codes: 团队ID列表（如果为空，则仅查询个人工具）
        :return: 工具列表
        """
        try:
            search_condition = {}
            # 构建查询条件
            query_conditions = []
            if team_codes is not None and len(team_codes) > 0:
                # 当team_codes存在且非空时，添加team_code的查询条件
                team_condition = {
                    'team_code': {'$in': team_codes}
                }
                query_conditions.append(team_condition)
            else:
                # 当team_codes不存在或为空时，添加team_code为空的条件
                team_condition = {
                    'team_code': ""
                }
                query_conditions.append(team_condition)

                # 同时，添加account_id的条件
                if account_id_list:
                    personal_condition = {
                        'account_id': {'$in': account_id_list}
                    }
                    query_conditions.append(personal_condition)
            state_condition = {
                'status': 1
            }
            query_conditions.append(state_condition)

            if not query_conditions:
                LogUtil.info("无法查询")
                return []
            # 使用 $and 操作符合并条件
            search_condition['$and'] = query_conditions
            LogUtil.info(f"search_condition: {search_condition}")

            tool_list = MongodbUtil.query_docs_by_condition(CollectionConfig.TOOL_COLLECTION,
                                                            search_condition=search_condition)
            result = []
            for tool in tool_list:
                print(f"tool_query_by_user: {str(tool['_id'])}")
                # 查询一次获取所有所需字段
                tool_doc = MongodbUtil.query_doc_by_id(CollectionConfig.TOOL_INFO_COLLECTION, doc_id=str(tool["_id"]))
                properties_list = tool_doc.get('properties_list', [])
                url = tool_doc.get('url', '')
                method = tool_doc.get('method', '')
                media_type = tool_doc.get('media_type', '')
                description = tool_doc.get('description', '')

                tool_info = {
                    "_id": str(tool["_id"]),
                    "tool_name": tool["tool_name"],
                    "description": description,
                    "properties_list": properties_list,
                    "url": url,
                    "method": method,
                    "media_type": media_type,
                    "credentials": tool["credentials"],
                }
                result.append(tool_info)
            return result  # 直接返回结果列表
        except Exception as e:
            LogUtil.error(f"查询工具失败: {str(traceback.format_exc())}")
            return RetUtil.response_error(message="查询工具失败")


    @staticmethod
    async def whether_use_tool(input, model_params, tool_list):
        try:

            prompt_tool = f"""
                # 角色
                你是一个精通多种工具的专家，能精准分析问题并选择合适的工具来解决。

                可用工具列表: {tool_list}

                ## 技能
                1. 分析用户问题，判断是否需要使用工具。
                2. 如果需要使用工具，返回True；否则返回False,不需要输出其他任何别的东西。
                """

            # 业务逻辑处理
            openAILLMService = OpenAILLMService()
            result = openAILLMService.chunk_chat(
                request=ChatCompletionRequestParams(question=input,
                                                    system_prompts=prompt_tool,
                                                    chatbot=[],
                                                    history=3,
                                                    max_token_length=4096,
                                                    temperature=0.8,
                                                    model_uid=model_params["model_uid"])
            )
            LogUtil.info(f"大模型意图识别为:{result}")
            return result
        except Exception as e:
            LogUtil.error(f"测试智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def tool_list(result):
        print(result)
        try:
            tool_list = []
            for tool in result['tool_list']:
                tool_info = MongodbUtil.query_doc_by_id(CollectionConfig.TOOL_COLLECTION,
                                                          doc_id=tool)
                if tool_info.get("status") != 1:  # 如果工具未发布（status不等于1），跳过该工具
                    continue
                tool_config = MongodbUtil.query_doc_by_id(CollectionConfig.TOOL_INFO_COLLECTION,
                                                          doc_id=tool)
                # schema = tool_result["schema"]
                # LogUtil.info(f"schema{schema}")
                # schema = json.loads(schema)
                # servers = schema.get('servers', [])
                # if servers:
                #     url = servers[0].get('url', '')
                # else:
                #     print("No servers found.")
                #     url = ""
                #
                # # 提取路径和操作
                # paths = schema.get('paths', {})
                # if paths:
                #     path = next(iter(paths), None)  # 获取第一个路径
                #     if path:
                #         operations = paths[path]  # 获取该路径下的所有操作（如 post, get 等）
                #         method = next(iter(operations), None)  # 获取第一个操作方法
                #         if method:
                #             operation_details = operations[method]  # 获取操作的详细信息
                #             summary = operation_details.get('summary', '')
                #             operation_id = operation_details.get('operationId', '')
                #             request_body = operation_details.get('requestBody', {})
                #             properties = request_body.get('content', {}).get('application/json', {}).get('schema',
                #                                                                                          {}).get(
                #                 'properties', {})
                #             required = request_body.get('content', {}).get('application/json', {}).get('schema',
                #                                                                                        {}).get(
                #                 'required', [])
                print(tool_config)
                tool_url = tool_config["url"]
                # tool_url = url + path
                tool_parm = {
                    "type": "function",
                    "function": {
                        "name": tool_config["tool_name"],
                        "description": tool_config["description"],
                        "parameters": {
                            "type": "object",
                            "properties": tool_config["properties_list"],
                            "required": [param["name"] for param in tool_config["properties_list"] if param["required"]]
                        }
                    }
                }
                method = tool_config["method"]
                tool_list.append({
                    "tool_parm": tool_parm,
                    "tool_url": tool_url,
                    "method": method
                })

            return tool_list
        except Exception as e:
            LogUtil.error(f"工具列表处理异常: {str(traceback.format_exc())}")

    @staticmethod
    async def generate_tool_function(tool_info):
        tool_name = tool_info['tool_parm']['function']['name']
        tool_url = tool_info['tool_url']
        tool_method = tool_info['method']

        async def tool_function(**kwargs):
            headers = {
                "Content-Type": "application/json"
            }
            print("工具调用了", tool_url)
            LogUtil.info(f"接口{tool_url}调用了")
            max_retries = 3  # 最大重试次数
            retry_delay = 2  # 重试间隔（秒）
            for attempt in range(max_retries):
                try:
                    print(f"尝试调用接口: {tool_url}, 尝试次数 {attempt + 1}/{max_retries}")
                    LogUtil.info(f"尝试调用接口: {tool_url}, 尝试次数 {attempt + 1}/{max_retries}")
                    response = requests.request(
                        tool_method,
                        tool_url,
                        headers=headers,
                        json=kwargs,
                        timeout=3  # 设置超时时间为3秒
                    )
                    if response.status_code == 200:
                        result = response.json()
                        print(f"{tool_name}接口的返回值是：", result)
                        LogUtil.info(f"{tool_name}接口的返回值是:{result}")
                        return result
                    else:
                        print(f"接口调用出错: {response.status_code}")
                        LogUtil.info(f"接口调用出错: {response.status_code}")
                        return f"接口调用出错: {response.status_code}"
                except (requests.Timeout, requests.RequestException) as e:
                    print(f"接口调用超时或发生网络错误: {str(e)}，正在重试，尝试次数 {attempt + 1}/{max_retries}")
                    LogUtil.info(f"接口调用超时或发生网络错误: {str(e)}，正在重试，尝试次数 {attempt + 1}/{max_retries}")
                    time.sleep(retry_delay)
                except Exception as e:
                    print(f"接口调用发生异常: {str(e)}，正在重试，尝试次数 {attempt + 1}/{max_retries}")
                    LogUtil.info(f"接口调用发生异常: {str(e)}，正在重试，尝试次数 {attempt + 1}/{max_retries}")
                    time.sleep(retry_delay)
            # 如果所有重试都失败，返回一个明确的错误信息
            # 如果所有重试都失败，返回一个明确的错误信息
            return f"接口调用失败，达到最大重试次数"

        return tool_function

    @staticmethod
    async def extract_content_after_think(response):
        content = response[0]['content']
        return content

    @staticmethod
    async def tool_agent(chat_request,params,model_params,TOOLS,tool_list):
        from qwen_agent.llm import get_chat_model
        async def generate_tool_function(tool_info):
            import aiohttp
            import asyncio
            tool_name = tool_info['tool_parm']['function']['name']
            tool_url = tool_info['tool_url']
            tool_method = tool_info['method']
            print("chat_request.headers11111111111",chat_request.headers)
            token = chat_request.headers.get("token",None)

            if token == None:
                redis = chat_request.app.state.redis_pool
                account_id=chat_request.state.account_id
                print("account_id11111",account_id)
                token_info = await RedisUtil.get_cached_data(key=account_id,redis=redis)
                print("token_info",token_info)
                if token_info!= None:
                        token = token_info["value"]

            # if 'token' in chat_request.headers:
            #     token = chat_request.headers.get("token")
            # else:
            #     token = chat_request.headers.get("postman-token")

            print("token",token)
            async def tool_function(**kwargs):
                headers = {
                    "token":token,
                    "Content-Type": "application/json"
                }
                LogUtil.info(f"接口{tool_url}调用了")
                max_retries = 3  # 最大重试次数
                retry_delay = 0.3  # 重试间隔（秒）
                print("tool_method",tool_method)
                print("tool_url",tool_url)
                print("kwargs",kwargs)
                result = []
                for attempt in range(max_retries):
                    try:
                        LogUtil.info(f"尝试调用接口: {tool_url}, 尝试次数 {attempt + 1}/{max_retries}")

                        # 设置超时时间为3秒
                        timeout = aiohttp.ClientTimeout(total=3)
                        async with aiohttp.ClientSession(timeout=timeout) as session:
                            async with session.request(
                                    tool_method,
                                    tool_url,
                                    headers=headers,
                                    json=kwargs
                            ) as response:
                                if response.status == 200:
                                    result.append(await response.json())
                                    LogUtil.info(f"{tool_name}接口的返回值是:{result}")
                                    return result

                                else:
                                    result.append(f"接口调用出错: {response.status}")
                                    LogUtil.info(f"接口调用出错: {response.status}")
                                    return result
                    except (asyncio.TimeoutError, aiohttp.ClientError) as e:
                        LogUtil.info(
                            f"接口调用超时或发生网络错误: {str(e)}，正在重试，尝试次数 {attempt + 1}/{max_retries}")
                        await asyncio.sleep(retry_delay)
                    except Exception as e:
                        LogUtil.info(f"接口调用发生异常: {str(e)}，正在重试，尝试次数 {attempt + 1}/{max_retries}")
                        await asyncio.sleep(retry_delay)

                # 如果所有重试都失败，返回一个明确的错误信息
                return f"接口调用失败，达到最大重试次数"

            return tool_function

        MESSAGES = [
            {"role": "system", "content": "你是一个有帮助的助手。\n"},
            {"role": "user", "content": params.input},
        ]
        from base_configs.model_config import ModelConfig

        result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_RUN_COLLECTION,
                                             doc_id=ObjectId(model_params["id"]))

        if result["is_external"] == True:

            llm = get_chat_model({
                'model': model_params["model_uid"],
                'model_server': result["api_url"],
                'api_key': result["api_key"],
            })

        else:
            llm = get_chat_model({
                'model': model_params["model_uid"],
                'model_server': ModelConfig.LLM_API_BASE,
                'api_key': ModelConfig.LLM_API_KEY,
            })

        messages = MESSAGES[:]
        LogUtil.info(f"TOOLS{TOOLS}")
        functions = [tool["function"] for tool in TOOLS]
        for responses in llm.chat(
                messages=messages,
                functions=functions,
                extra_generate_cfg=dict(parallel_function_calls=True),
        ):
            pass
        messages.extend(responses)
        tool_use = False
        # 遍历消息列表
        for message in responses:
            # 检查消息中是否有function_call字段
            if 'function_call' in message:
                fn_call = message['function_call']
                # 检查function_call中的name是否不为空
                if fn_call.get('name', '').strip() != '':
                    tool_use = True
                    break  # 如果找到一个非空的fn_name，直接设置tool_use为True并退出循环

        if tool_use == True:
            try:
                # 生成工具函数并添加到 FUNCTION_MAP
                FUNCTION_MAP = {}
                for tool_info in tool_list:
                    tool_name = tool_info['tool_parm']['function']['name']
                    FUNCTION_MAP[tool_name] = await generate_tool_function(tool_info)

                # 定义其他必要的函数和逻辑
                async def get_function_by_name(name):
                    return FUNCTION_MAP.get(name, None)

                for message in responses:
                    if fn_call := message.get("function_call", None):
                        fn_name: str = fn_call['name']
                        fn_args: dict = json.loads(fn_call["arguments"])
                        fn_res: str = json.dumps(await (await get_function_by_name(fn_name))(**fn_args))
                        try:
                            fn_res=json.loads(fn_res)
                        except:
                            fn_res=fn_res
                        messages.append({
                            "role": "function",
                            "name": fn_name,
                            "content": fn_res,
                        })
            except:
                tool_use = False

        # async def extract_content_after_think(response):
        #     content = response[0]['content']
        #     return content

        # if tool_use == True:
        #     for responses in llm.chat(messages=messages, functions=functions):
        #         pass
        #     result = await AgentService.extract_content_after_think(responses)
        result=messages
        print("result",result)

        return result,tool_use

    @staticmethod
    async def intention_recognition(query, system_prompt, model_params, tool_list):
        # try:
        import openai
        import re
        import json
        from service_model_manage.service.chat_completion_service import OpenAILLMService
        from service_model_manage.entity.chat_completion_entity import (
            ChatCompletionRequestParams, ChatCompletionRequestParams_v1, ChatCompletionHistoryParams,
            ChatCompletionListParams
        )
        openAILLMService = OpenAILLMService()
        openAILLMService.llm_model_client = openai.Client(
            api_key="not empty", base_url="http://10.8.21.166:9997/v1"
        )
        openAILLMService.async_llm_model_client = openai.AsyncClient(
            api_key="not empty", base_url="http://10.8.21.166:9997/v1"
        )

        prompt = """Respond to the human as helpfully and accurately as possible. 

            {{instruction}}

            You have access to the following tools:

            {{tools}}

            Use a json blob to specify a tool by providing an action key (tool name) and an action_input key (tool input).
            Valid "action" values: "Final Answer" or {{tool_names}}

            Provide only ONE action per $JSON_BLOB, as shown:

            ```
            {
              "action": $TOOL_NAME,
              "action_input": $ACTION_INPUT
            }
            ```

            Follow this format:

            Question: input question to answer
            Thought: consider previous and subsequent steps
            Action:
            ```
            $JSON_BLOB
            ```
            Observation: action result
            ... (repeat Thought/Action/Observation N times)
            Thought: I know what to respond
            Action:
            ```
            {
              "action": "Final Answer",
              "action_input": "Final response to human"
            }
            ```

           Begin! Reminder to ALWAYS respond with a valid json blob of a single action. Use tools if necessary. Respond directly if appropriate. Format is Action:```$JSON_BLOB```then Observation:.
            {{historic_messages}}
            Question: {{query}}
            {{agent_scratchpad}}
            Thought:

            """
        historic_messages = ""
        agent_scratchpad = ""
        observation = """Observation: {{observation}}
            Question: {{query}}
            Thought:"""
        prompt = prompt.replace("{tools}", str(tool_list))
        prompt = prompt.replace("{instruction}", str(system_prompt))
        observation = observation.replace("{query}", str(query))
        prompt = prompt.replace("{query}", str(query))
        iteration = 0
        max_iteration = 3
        while iteration < max_iteration:
            print("进入循环")
            iteration += 1
            # if historic_messages != "":
            #     prompt = prompt.replace("{historic_messages}", str({"historic_messages": historic_messages}))
            # if agent_scratchpad != "":
            #     prompt = prompt.replace("{agent_scratchpad}", str({"agent_scratchpad": agent_scratchpad}))
            print("query", query)
            response = openAILLMService.chunk_chat(
                request=ChatCompletionRequestParams(model_uid="DeepSeek-R1-Distill-Qwen-32B", question=query,
                                                    system_prompts=prompt)
            )
            print("222222222")
            print("response", response)
            # LogUtil.info(f"Response:{response}\n\n")
            # historic_messages = response
            match = re.search(r'```(.*?)```', response, re.DOTALL)
            if match:
                matched_str = match.group(1).strip()  # 获取匹配到的字符串并去除首尾空白字符
                # 将字符串转换为字典
                data = json.loads(matched_str)
                # LogUtil.info(f"数据{data}")
                historic_messages = str(data)

            if data["action"] == "Final Answer":
                correct_result = data["action_input"]
                break
            for tool in tool_list:
                if data["action"] == tool["tool_name"]:
                    print(data)
                    tool_url = tool["tool_url"]
                    params = data["action_input"]
                    # LogUtil.info(f"Tool_url:{tool_url}")
                    # LogUtil.info(f"Tool_params:{params}")
                    print("params", params)
                    import httpx
                    async with httpx.AsyncClient() as client:
                        response = await client.post(tool_url, json=params)
                    result = response.json()
                    # LogUtil.info(f"Tool_result:{result}")
            observation = observation.replace("{observation}", str(result))
            print(f"Observation: {observation}\n\n")
            response_observation = openAILLMService.chunk_chat(
                request=ChatCompletionRequestParams(model_uid="qwen2-72B", question=query,
                                                    system_prompts=observation)
            )
            correct_result = response_observation
            if iteration == max_iteration - 1:
                correct_result = result
                break
            print(f"Observation_Result:{response_observation}\n")
            agent_scratchpad = response_observation
    #     return correct_result
    #
    # except Exception as e:
    #     LogUtil.error(f"测试智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def query_team_agent(agent_name, page, page_size, account_id, team_code_list,code,db):
        try:

            # team_id = []
            # for i in range(len(team_code_list)):
            #     usr_data = db.query(Team_Model).filter(Team_Model.status == 1,
            #                                            Team_Model.team_code == team_code_list[i]).first()
            #     print("usr_data", usr_data.team_name)
            #     team_id.append(usr_data.id)

            # print("team_id",team_id)
            from datetime import datetime
            result = []
            final_results=[]
            def parse_upload_time(upload_time_str):
                return datetime.strptime(upload_time_str, "%Y-%m-%d %H:%M:%S")

            if code!="":
                condition = {"agent_name": {"$regex": f"{re.escape(agent_name)}(\\_.*)?", "$options": "i"},

                             "code": code,

                             "team_code": {"$in": team_code_list}
                }
                LogUtil.info(str(condition))

                results = MongodbUtil.query_docs_by_condition_pagination(CollectionConfig.AGENT_COLLECTION,
                                                              search_condition=condition,
                                                              page=page, page_size=page_size, sort_field="create_time", reverse=True)
                for doc in results:
                    final_results.append(doc)
                len_result = MongodbUtil.count_documents_by_condition(CollectionConfig.AGENT_COLLECTION, condition)
            else:
                condition = {"agent_name": {"$regex": f"{re.escape(agent_name)}(\\_.*)?", "$options": "i"},
                             "team_code": {"$in": team_code_list}
                }
                LogUtil.info(str(condition))

                results = MongodbUtil.query_docs_by_condition_pagination(CollectionConfig.AGENT_COLLECTION,
                                                              search_condition=condition,
                                                              page=page, page_size=page_size, sort_field="create_time", reverse=True)
                for doc in results:
                    final_results.append(doc)
                len_result = MongodbUtil.count_documents_by_condition(CollectionConfig.AGENT_COLLECTION, condition)


            # print("final_results排序前", final_results)
            # flattened_data = [item for sublist in final_results for item in sublist]
            # final_results = sorted(flattened_data, key=lambda x: parse_upload_time(x['create_time']), reverse=True)
            # print("final_results排序后", final_results)



            for item in final_results:
                    temp = item.get("code", "")
                    result.append(
                        {"agent_id": str(item["_id"]), "agent_name": item["agent_name"], "description": item["description"],
                         "account_id": item["account_id"], "team_code": item["team_code"], "status":item["status"],"code": temp})
            LogUtil.info(str(result))
            return {"total": len_result, "result": result}
        except Exception as e:
            LogUtil.error(f"智能体查询异常: {str(e)}")
            raise e


    @staticmethod
    async def create_team_agent(agent_name, desripition, account_id,team_code,type_name,code,db):
        try:
            # usr_data = db.query(Team_Model).filter(Team_Model.status == 1,
            #                                        Team_Model.team_code == team_code).first()
            create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            if code=='':
                code='0'
                type_name='其他'
            insert_result = MongodbUtil.insert_one(CollectionConfig.AGENT_COLLECTION,
                                                   {'agent_name': agent_name, 'description': desripition,
                                                    'create_time': create_time, 'account_id': account_id,'team_code': team_code,'type_name':type_name,'code':code,'status': 0})
            if insert_result is not None:
                insert_result = str(insert_result.inserted_id)
            return "新增智能体成功", insert_result

        except Exception as e:
            LogUtil.error(f"新增智能体异常: {str(traceback.format_exc())}")

    @staticmethod
    async def update_team_agent(agent_id, agent_name, description,team_code,db,code):
        try:
            # usr_data = db.query(Team_Model).filter(Team_Model.status == 1,
            #                                        Team_Model.team_code == team_code).first()
            # team_id = usr_data.id
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.AGENT_COLLECTION,
                                                         search_condition={"_id": ObjectId(agent_id)})
            LogUtil.info(f"查询智能体搜索结果: {result}")
            for item in result:
                MongodbUtil.update_one(CollectionConfig.AGENT_COLLECTION, query_filter={"_id": ObjectId(agent_id)},
                                       update_operation={
                                           '$set': {
                                               'agent_name': agent_name,
                                               'description': description,
                                               'team_code':team_code,
                                               'code':code
                                           }
                                       })
            return "更新智能体基础信息成功"

        except Exception as e:
            LogUtil.error(f"更新智能体信息异常: {str(traceback.format_exc())}")
            return False


    @staticmethod
    async def user_team_permission(account_id, team_code_list, db):
        team_id=[]
        for i in range(len(team_code_list)):

            result = db.query(Team_Model).filter(Team_Model.status == 1, Team_Model.team_code == team_code_list[i]).first()
            team_id.append(result.id)
        print("account_id",account_id)
        print("team_id",team_id)
        for i in range(len(team_id)):
            result = db.query(TeamMem_Model.team_id).filter(TeamMem_Model.account_id == account_id, TeamMem_Model.status == 1,
                                                            TeamMem_Model.team_id == team_id[i]).first()
            if result != None:
                return True
            else:
                if i==len(team_id):
                    return False
                else:
                    continue

    @staticmethod
    async def update_agent_status(agent_id, status):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.AGENT_COLLECTION,
                                                         search_condition={"_id": ObjectId(agent_id)})
            LogUtil.info(f"查询智能体搜索结果: {result}")
            for item in result:
                MongodbUtil.update_one(CollectionConfig.AGENT_COLLECTION, query_filter={"_id": ObjectId(agent_id)},
                                       update_operation={
                                           '$set': {
                                               'status': status,
                                           }
                                       })
            return "更新智能体基础信息成功"

        except Exception as e:
            LogUtil.error(f"更新智能体信息异常: {str(traceback.format_exc())}")
            return False

    @staticmethod
    async def type_exist(code: str):
        try:
            condition = {'code': code}
            agent = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.SYS_STATIC_DICT_TYPE,
                                                        search_condition=condition)
            for _ in agent:
                return True
            return False
        except Exception as e:
            LogUtil.error(f"智能体查询异常: {str(traceback.format_exc())}")

    @staticmethod
    async def create_type(code,name,description,account_name,usr_name):
        try:
            create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            insert_result = MongodbUtil.insert_one(CollectionConfig.SYS_STATIC_DICT_TYPE,{'code': code, 'name': name,
'cn_spell':"",'scope_type':"",'description': description, 'creator':account_name,'creator_name':usr_name,'create_time': create_time, 'modified_time': create_time,'status':"1",'updator':"",'updator_name':""})
            print("111",insert_result)
            if insert_result is not None:
                insert_result = str(insert_result.inserted_id)
            return "新增类别成功", insert_result

        except Exception as e:
            LogUtil.error(f"新增类别成功: {str(traceback.format_exc())}")

    @staticmethod
    async def update_type(params,account_name,usr_name):
        try:
            updata_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.SYS_STATIC_DICT_TYPE,
                                                         search_condition={'_id': ObjectId(params.id)})
            for item in result:
                subcode=item['code']
            print("**********")

            print("subcode",subcode)

            tt=MongodbUtil.update_one(CollectionConfig.SYS_STATIC_DICT_TYPE, {'_id': ObjectId(params.id)},
                                   update_operation={'$set':
                                                         {'name': params.name,
                                                          'code': params.code,
                                                          'description':  params.description, 'updator':  account_name,
                                                          'updator_name':  usr_name,
                                                          'modified_time':   updata_time, 'status': params.status}
                                   })
            print("tt",tt)
            # 联表修改
            MongodbUtil.update_many(CollectionConfig.SYS_STATIC_DICT_ITEMS, {'type_code':subcode},
                                   update_operation={'$set':
                                                         {
                                                          'type_code': params.code,
                                                          'updator': account_name,
                                                          'updator_name': usr_name,
                                                          'modified_time': updata_time}
                                                     })


            return "更新类别信息成功"


        except Exception as e:
            LogUtil.error(f"更新类别信息异常: {str(traceback.format_exc())}")

    @staticmethod
    async def delete_type(id):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.SYS_STATIC_DICT_TYPE,
                                                         search_condition={'_id': ObjectId(id)})
            for _ in result:
                MongodbUtil.update_one(CollectionConfig.SYS_STATIC_DICT_TYPE, {'_id': ObjectId(id)},
                                       update_operation={'$set':{'status': "0"}})
                return "删除类别信息成功"
        except Exception as e:
            LogUtil.error(f"删除类别信息异常: {str(traceback.format_exc())}")

    @staticmethod
    async def query_type(name, code,status,page, page_size):
        try:
            from datetime import datetime
            condition = {"name": {"$regex": f"{re.escape(name)}(\\_.*)?", "$options": "i"}, "code": {"$regex": f"{re.escape(code)}(\\_.*)?", "$options": "i"},"status": status}
            LogUtil.info(str(condition))
            result = []
            results = MongodbUtil.query_docs_by_condition(CollectionConfig.SYS_STATIC_DICT_TYPE, search_condition=condition)
            for item in results:
                print("item",item)
                result.append({"id": str(item["_id"]),"code": item["code"], "name":item["name"],"cn_spell":item["cn_spell"],"scope_type":item["scope_type"],"description":item["description"],
                               "creator":item["creator"],"creator_name":item["creator_name"],"create_time":item["create_time"],"modified_time":item["modified_time"],"status":item["status"],
                               "updator": item["updator"],"updator_name": item["updator_name"]})
            print("result",result)
            def parse_upload_time(upload_time_str):
                return datetime.strptime(upload_time_str, "%Y-%m-%d %H:%M:%S")

            result = PageUtil.paginate_list(result, page, page_size)
            print("result", result)
            items = result['result']
            results = sorted(items, key=lambda x: parse_upload_time(x["create_time"]), reverse=True)
            result['result'] = results
            print("result", result)
            return result
        except Exception as e:
            LogUtil.error(f"查询类别列表异常: {str(traceback.format_exc())}")

    @staticmethod
    async def type_item_exist(code,type_code):
        try:
            condition = {'code': code,'type_code':type_code}
            results = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.SYS_STATIC_DICT_ITEMS,
                                                        search_condition=condition)
            for _ in results:
                return True
            return False
        except Exception as e:
            LogUtil.error(f"智能体查询异常: {str(traceback.format_exc())}")

    @staticmethod
    async def create_item_type( code, type_code, name, description, account_name, usr_name):
        try:
            condition = {'type_code': type_code}
            results = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.SYS_STATIC_DICT_ITEMS,
                                                          search_condition=condition)
            i=0
            for item in results:
                i=i+1

            create_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            insert_result = MongodbUtil.insert_one(CollectionConfig.SYS_STATIC_DICT_ITEMS,
                                                   {'code': code, 'type_code': type_code,'name': name,
                                                    'description': description, 'creator': account_name,
                                                    'creator_name': usr_name, 'create_time': create_time,
                                                    'modified_time': create_time, 'status': "1","sort_no":i,'updator': "",
                                                    'updator_name': "",})
            print("111", insert_result)
            if insert_result is not None:
                insert_result = str(insert_result.inserted_id)
            return "新增类别成功", insert_result

        except Exception as e:
            LogUtil.error(f"新增类别成功: {str(traceback.format_exc())}")

    @staticmethod
    async def updata_item_type(params, account_name, usr_name):
        try:
            updata_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")


            MongodbUtil.update_one(CollectionConfig.SYS_STATIC_DICT_ITEMS, {'_id': ObjectId(params.id)},
                                   update_operation={'$set':
                                                         {'name': params.name,
                                                          'description': params.description,
                                                          'code':params.code,
                                                          'updator': account_name,
                                                          'updator_name': usr_name,
                                                          'modified_time': updata_time,
                                                          'status': params.status}
                                                     })

            return "更新类别信息成功"


        except Exception as e:
            LogUtil.error(f"更新类别信息异常: {str(traceback.format_exc())}")

    @staticmethod
    async def delete_item_type(id):
        try:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.SYS_STATIC_DICT_ITEMS,
                                                         search_condition={'_id': ObjectId(id)})
            for _ in result:
                MongodbUtil.update_one(CollectionConfig.SYS_STATIC_DICT_ITEMS, {'_id': ObjectId(id)},
                                       update_operation={'$set': {'status': "0"}})
                return "删除子类别信息成功"
        except Exception as e:
            LogUtil.error(f"删除子类别信息异常: {str(traceback.format_exc())}")

    @staticmethod
    async def query_item_type(name, code, status, page, page_size,type_code):
        try:


            from datetime import datetime
            condition = {"name": {"$regex": f"{re.escape(name)}(\\_.*)?", "$options": "i"},
                         "code": {"$regex": f"{re.escape(code)}(\\_.*)?", "$options": "i"},
                         "type_code": {"$regex": f"{re.escape(type_code)}(\\_.*)?", "$options": "i"}, "status": status}
            LogUtil.info(str(condition))
            result = []
            results = MongodbUtil.query_docs_by_condition_pagination(CollectionConfig.SYS_STATIC_DICT_ITEMS,
                                                          search_condition=condition,
                                                          page=page, page_size=page_size, sort_field="create_time", reverse=True)
            for item in results:
                result.append({ "id": str(item["_id"]),"code": item["code"], "type_code": item["type_code"],"name": item["name"], "status": item["status"],"sort_no": item["sort_no"],
                               "description": item["description"], "create_time": item["create_time"]})

            len_result = MongodbUtil.count_documents_by_condition(CollectionConfig.SYS_STATIC_DICT_ITEMS, condition)
            return {"total": len_result, "result": result}
        except Exception as e:
            LogUtil.error(f"查询智能体列表异常: {str(e)}")
            raise e