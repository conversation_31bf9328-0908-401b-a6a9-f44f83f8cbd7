# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base
@File    ：knowledge_retrival.py
<AUTHOR>
@Date    ：2024/08/29 09:36
"""
from base_configs.minio_config import MinioConfig
from base_utils.log_util import LogUtil
import traceback
from typing import Any, Dict, List
from fastapi import APIRouter, HTTPException
from fastapi.responses import Response
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from base_utils.milvus_util import MilvusUtil
from base_utils.mongodb_util import MongodbUtil
from base_utils.embedding_util import EmbeddingUtil
from base_utils.rerank_util import RerankUtil
from base_configs.model_config import ModelConfig
from base_configs.mongodb_config import CollectionConfig
'''
@File         :agent_service.py
@Description  :
<AUTHOR>
@Date         :2024/10/30 09:47:38
'''
from bson import ObjectId
import copy
import os
import re
import json
import requests
from base_utils.mongodb_util import MongodbUtil
from base_configs.api_config import ApiConfig
from base_configs.mongodb_config import CollectionConfig
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from base_utils.mongodb_util import MongodbUtil
from service_model_manage.service.chat_completion_service import OpenAILLMService
from service_model_manage.entity.chat_completion_entity import (
    ChatCompletionRequestParams,
)
from service_knowledge_manage.service.knowledge_service import KnowledgeService

async def knowledge_retrieval(params,recall_setting, kb_list):
    LogUtil.info(f"知识库检索--：{params}")
    try:

        retrieval_text = []
        rerank_results = []
        # 连接MongoDB数据库
        MongodbUtil.connect()
        LogUtil.info(f"kb_list:{kb_list}")
        entity = []
        kb_id=[]

        new_source=[]
        # 根据知识库的名称获取到embedding模型名称
        for kb in kb_list:
            query_result = MongodbUtil.query_docs_by_condition(
                collection_name=CollectionConfig.KB_COLLECTION,
                search_condition={"_id": ObjectId(kb["id"])}
            )
            embedding_model = ModelConfig.DEFAULT_EMBEDDING_MODEL

            for item in query_result:
                if item:
                    print("item",item)
                    embedding_model = item["embedding_model"]
                    embedding_id = item["embedding_id"]
                    rerank_id = item["rerank_id"]
                    recall_num = item.get("retrieval_count", "") if item.get("retrieval_count", "") else 10
                    LogUtil.info(f"查询到的知识库召回数量为{recall_num}")
                else:
                    LogUtil.info("知识库不存在！！")
                    raise ValueError("知识库不存在")

            # 获取向量embedding工具
            embeddingUtil = EmbeddingUtil(embedding_id)
            embeddings = embeddingUtil.get_embedding(
                model_uid=embedding_model, input=params.input
            )


            # 获取milvus工具
            milvusUtil = MilvusUtil()
            docs = await milvusUtil.search_by_vector(
                collection_name="_"+kb["id"], vector=embeddings, limit=recall_num
            )

            docs = docs[0]
            for doc in docs:
                if kb.get("enhance_rounds",0) != 0:
                    doc["entity"]["enhance_rounds"] = kb["enhance_rounds"]
            # LogUtil.info(f"单个知识库检索得到的指定召回数量的切片内容为{docs}")
            LogUtil.info(f"单个知识库检索得到的指定召回数量的切片数量为{len(docs)}")
            if kb["rerank_model"]!= "":
                documents = [item["entity"]["content"] for item in docs]
                query = params.input
                top_n = kb["top_k"]
                model_uid = kb["rerank_model"]
                score = kb["score"]
                if len(documents) > 0:
                    rerank_results = RerankUtil(rerank_id=rerank_id).socre_rerank(
                        model_uid=model_uid,
                        documents=documents,
                        query=query,
                        top_n=top_n,
                        threshold=score,
                        return_documents=True,
                    )
                if len(rerank_results) > 0:
                    for item in rerank_results:
                        print("docs[index]",docs[item["index"]])
                        print("docs[index]", type(docs[item["index"]]))
                        entity.append(docs[item["index"]]["entity"])
                        # enhance_rounds = docs[item["index"]]["entity"]["enhance_rounds"]
                        # if enhance_rounds != 0:
                        #     chunk_index = docs[item["index"]]["entity"]["number"]
                        #     chunk_file = docs[item["index"]]["entity"]["file_name"]
                        #     lower_bound = max(0, chunk_index - enhance_rounds)
                        #     upper_bound = chunk_index + enhance_rounds
                        #     expr = f'number >= {lower_bound} and number <= {upper_bound} and file_name == "{chunk_file}"'
                        #     result = await milvusUtil.query_by_scalar(collection_name="_"+kb["id"],
                        #                                                query_conditions=expr, output_fields=["content"])
                        #     chunks = []
                        #     for chunk in result:
                        #         chunks.append(chunk["content"])
                        #     enhance_chunk = await KnowledgeService.merge_chunks_auto(chunks)
                        #     retrieval_text.append(enhance_chunk)
                        # else:
                        retrieval_text.append(item["document"]["text"])
                        kb_id.append(kb["id"])

            else:
                for item in docs:
                    entity.append(item["entity"])
                    # enhance_rounds = item["entity"]["enhance_rounds"]
                    # if enhance_rounds != 0:
                    #     chunk_index = item["entity"]["number"]
                    #     chunk_file = item["entity"]["file_name"]
                    #     lower_bound = max(0, chunk_index - enhance_rounds)
                    #     upper_bound = chunk_index + enhance_rounds
                    #     expr = f'number >= {lower_bound} and number <= {upper_bound} and file_name == "{chunk_file}"'
                    #     result = await milvusUtil.query_by_scalar(collection_name="_" + kb["id"],
                    #                                               query_conditions=expr, output_fields=["content"])
                    #     chunks = []
                    #     for chunk in result:
                    #         chunks.append(chunk["content"])
                    #     enhance_chunk = await KnowledgeService.merge_chunks_auto(chunks)
                    #     retrieval_text.append(enhance_chunk)
                    # else:
                    retrieval_text.append(item["entity"]["content"])
                    kb_id.append(kb["id"])
            print("retrieval_text",retrieval_text)
            print("kb_id",kb_id)
        # 取topk
        if retrieval_text==[]:
            return "",[]
        if recall_setting.get("recall_num", ""):
            recall_num = int(recall_setting.get("recall_num"))
            retrieval_text = retrieval_text[:recall_num]
            entity = entity[:recall_num]
            kb_id=kb_id[:recall_num]
            retrieval_text = [item['content'] for item in entity]
            # LogUtil.info(f"总体知识库检索得到的指定召回数量的切片内容为{retrieval_text}")
            LogUtil.info(f"总体知识库检索得到的指定召回数量的切片数量为{len(retrieval_text)}")

        results = []
        if recall_setting["rerank_model"]:
            # 开启重排
            query = params.input
            top_n = recall_setting["top_k"]
            LogUtil.info(f"Top_n:{top_n}")
            model_uid = recall_setting["rerank_model"]
            score = recall_setting["score"]
            # LogUtil.info(f"匹配分数:{score}")
            final_rerank_results = RerankUtil(recall_setting["rerank_id"]).socre_rerank(
                model_uid=model_uid,
                documents=retrieval_text,
                query=query,
                top_n=top_n,
                threshold=score,
                return_documents=True,
            )
            LogUtil.info(f"final_rerank_results:{final_rerank_results}")
            # LogUtil.info(f"最后重排长度:{len(final_rerank_results)}")
            # result = "\n".join(item["document"]["text"] for item in final_rerank_results)
            # result = [item["document"]["text"] for item in final_rerank_results]
            # result = "\n".join(result)
            print("final_rerank_results",final_rerank_results)
            for new_index,item in enumerate(final_rerank_results):
                index = item['index']
                number = entity[index].get('number', '')
                file_name = entity[index]['file_name']
                enhance_rounds = entity[index]["enhance_rounds"]
                if enhance_rounds != 0:
                    chunk_index = entity[index].get('number', '')
                    chunk_file = entity[index]['file_name']
                    lower_bound = max(0, chunk_index - enhance_rounds)
                    upper_bound = chunk_index + enhance_rounds
                    expr = f'number >= {lower_bound} and number <= {upper_bound} and file_name == "{chunk_file}"'
                    search_result = await milvusUtil.query_by_scalar(collection_name="_"+kb_id[index],
                                                               query_conditions=expr, output_fields=["content","source_data","number"])
                    chunks = []
                    pages = []
                    enhance_abandons = []
                    search_result = sorted(search_result, key=lambda x: x['number'])
                    for chunk in search_result:
                        for data in chunk.get("source_data",[]):
                            if data["page"] not in pages:
                                enhance_abandons.append(data.get("abandon_area"))
                                pages.append(data.get("page"))
                        chunks.append(chunk["content"])
                    enhance_chunk = await KnowledgeService.merge_chunks_auto(chunks)
                    results.append(enhance_chunk)
                else:
                    results.append(entity[index]["content"])
                file_urls = f"{kb_id[index]}/{file_name}"
                ori_file = file_urls
                kb_name = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.KB_COLLECTION,
                                                      doc_id=ObjectId(kb_id[index])).get("kb_name", "")
                if 'source_data' in entity[index] and entity[index]['source_data']!=[]:
                    # 根据 index 在 ori_data 中查找对应的 source_data
                    source_data=entity[index]['source_data']
                    print("source_data",source_data)
                    abandon_area=[item.get("abandon_area", []) for item in source_data] if source_data else []
                    images_urls = source_data[0].get("images_urls", [""])[0]
                    if file_name.endswith(".pdf"):
                        file_urls = file_urls
                    else:
                        file_urls = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION,
                                                    doc_id=entity[index].get('file_id')).get("pdf_path",file_urls)

                    # if file_urls
                    page= [item['page'] for item in source_data] if source_data else []
                    location = source_data[0].get('location',[])  if source_data else []
                    # new_source.append([item['document']['text'],images_urls,file_urls,page])

                    new_source.append({
                        "chunk_context": enhance_chunk if enhance_rounds !=0 else item['document']['text'],
                        "recall_score": recall_setting["score"],
                        'reference_file':file_name,
                        "images_urls": images_urls,
                        "file_urls": file_urls,
                        "reference_chunk_id":number,
                        "page": pages if enhance_rounds!= 0 else page,
                        'location':location,
                        "abandon_area":enhance_abandons if enhance_rounds!= 0 else abandon_area,
                        "recall_index":new_index,
                        "reference_kb_id":kb_id[index],
                        "reference_kb_name":kb_name
                    })
                else:
                    new_source.append({
                        "chunk_context": enhance_chunk if enhance_rounds !=0 else item['document']['text'],
                        "recall_score": recall_setting["score"],
                        'reference_file':file_name,
                        "images_urls": "",
                        "file_urls": file_urls,
                        "reference_chunk_id":number,
                        "page": "",
                        'location': "",
                        "abandon_area":"",
                        "recall_index":new_index,
                        "reference_kb_id":kb_id[index],
                        "reference_kb_name":kb_name
                    })
        else:
            # 关闭重排的逻辑
            for index in range(len(retrieval_text)):
                number =entity[index].get('number', '')
                file_name = entity[index]['file_name']
                enhance_rounds = entity[index].get("enhance_rounds",0)
                if enhance_rounds != 0:
                    chunk_index = entity[index].get('number', '')
                    chunk_file = entity[index]['file_name']
                    lower_bound = max(0, chunk_index - enhance_rounds)
                    upper_bound = chunk_index + enhance_rounds
                    expr = f'number >= {lower_bound} and number <= {upper_bound} and file_name == "{chunk_file}"'
                    search_result = await milvusUtil.query_by_scalar(collection_name="_" + kb_id[index],
                                                              query_conditions=expr, output_fields=["content","source_data","number"])
                    chunks = []
                    enhance_abandons = []
                    pages = []
                    search_result = sorted(search_result, key=lambda x: x['number'])
                    for chunk in search_result:
                        for data in chunk.get("source_data",[]):
                            if data["page"] not in pages:
                                enhance_abandons.append(data.get("abandon_area"))
                                pages.append(data.get("page"))
                        chunks.append(chunk["content"])
                    enhance_chunk = await KnowledgeService.merge_chunks_auto(chunks)
                    results.append(enhance_chunk)
                else:
                    results.append(retrieval_text[index])
                file_urls = f"{kb_id[index]}/{file_name}"
                ori_file = file_urls
                kb_name = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.KB_COLLECTION,
                                                      doc_id=ObjectId(kb_id[index])).get("kb_name", "")
                if 'source_data' in entity[index] and entity[index]['source_data']!=[]:
                    # 根据 index 在 ori_data 中查找对应的 source_data
                    source_data=entity[index]['source_data']
                    if source_data and 'images_urls' in source_data[0] and source_data[0]['images_urls']:
                        images_urls = source_data[0]['images_urls'][0]
                    else:
                        images_urls = ""
                    if file_name.endswith(".pdf"):
                        file_urls = file_urls
                    else:
                        file_urls = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION,
                                                    doc_id=entity[index].get('file_id')).get("pdf_path",file_urls)
                    page= [item['page'] for item in source_data] if source_data else []
                    abandon_area = [item.get("abandon_area", []) for item in source_data] if source_data else []
                    location = source_data[0].get('location',[])  if source_data else []
                    new_source.append({
                        "chunk_context": enhance_chunk if enhance_rounds != 0 else entity[index]['content'],
                        "images_urls": images_urls,
                        "file_urls": file_urls,
                        "page": pages if enhance_rounds!= 0 else page,
                        'location': location,
                        "abandon_area": enhance_abandons if enhance_rounds!= 0 else abandon_area,
                        "recall_score": recall_setting["score"],
                        'reference_file': file_name,
                        "reference_chunk_id": number,
                        "recall_index": index,
                        "reference_kb_id": kb_id[index],
                        "reference_kb_name": kb_name
                    })
                else:
                    new_source.append({
                        "chunk_context": enhance_chunk if enhance_rounds != 0 else entity[index]['content'],
                        "images_urls": '',
                        "file_urls": '',
                        "page": '',
                        'location': "",
                        "abandon_area": '',
                        "recall_score": recall_setting["score"],
                        'reference_file': file_name,
                        "reference_chunk_id": number,
                        "recall_index": index,
                        "reference_kb_id": kb_id[index],
                        "reference_kb_name": kb_name
                    })
        result = "\n".join(results)
        print(f"最终喂给大模型的结果为:{result}")
        print("new_sourceaa",new_source)
        for item in new_source:
            page = item.get("page", [])
            abandon_area = item.get("abandon_area", [])
            # 确保 page 和 abandon_area 的长度一致
            assert len(page) == len(abandon_area), "Lengths of page and abandon_area must be the same"
            # 构建新的 abandon_area 字典
            new_abandon_area = {str(p): a for p, a in zip(page, abandon_area)}
            # 更新 item 中的 abandon_area
            item["abandon_area"] = new_abandon_area
        # 打印修改后的结果
        print("Modified retrival_info:", new_source)
        return result,new_source
    except Exception as e:
        LogUtil.error(f"查询智能体列表异常: {str(traceback.format_exc())}")
        raise
