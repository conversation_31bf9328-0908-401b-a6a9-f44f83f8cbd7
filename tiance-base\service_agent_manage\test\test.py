import copy
import os
import re
import json
import requests
from base_utils.mongodb_util import MongodbUtil
from base_configs.api_config import ApiConfig
from base_configs.mongodb_config import CollectionConfig
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from base_utils.mongodb_util import MongodbUtil
from base_utils.time_util import TimeUtil
import asyncio
import paramiko
from bson import ObjectId
from sse_starlette.sse import EventSourceResponse
import yaml
from fastapi.responses import StreamingResponse
from base_configs.train_config import TrainConfig
import traceback
import datetime
from base_utils.page_util import PageUtil
from service_model_manage.service.chat_completion_service import OpenAILLMService
from service_model_manage.entity.chat_completion_entity import (
    ChatCompletionRequestParams,
)


result = MongodbUtil.query_docs_by_condition(CollectionConfig.ARRANGE_AGENT_COLLECTION, search_condition={'_id': ObjectId("6735529d012d26f6f9b9c742")})
prompt_tool="你是一个人工智能助手"
for item in result:
    model_uid = item['model_uid']
    openAILLMService = OpenAILLMService()
    result = openAILLMService.chunk_chat(request=ChatCompletionRequestParams(question=ques,system_prompts=prompt_tool,chatbot=[],history=3,max_token_length=4096,temperature=0.8,model_uid=model_uid,))
print("test...")
print(f"response_content: {result}")
