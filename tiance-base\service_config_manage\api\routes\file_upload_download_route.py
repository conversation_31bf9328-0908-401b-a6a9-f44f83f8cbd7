#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base
@File    ：file_upload_download.py
<AUTHOR>
@Date    ：2024/8/28 9.17
"""

import traceback
from typing import List
from fastapi import APIRouter
from fastapi import Body, UploadFile, File, Form, Query
from fastapi.responses import Response,StreamingResponse
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from fastapi.encoders import jsonable_encoder
from service_config_manage.service.file_upload_download_service import FileService

router = APIRouter()

@router.post('/upload_file', description="上传文件")
async def upload_files(
        file_names: List[str] = Form(...),  # 文件名称列表
        file_types: List[str] = Form(...),  # 文件类型列表
        files: List[UploadFile] = File(...)  # 接收的文件列表
) -> Response:
    try:
        file_names = file_names[0].replace("'", "")
        file_types = file_types[0].replace("'", "")
        # 这里假设 FileService 有一个可以接受文件列表的方法
        file_names = file_names.split(',') if isinstance(file_names, str) else file_names
        file_types = file_types.split(',') if isinstance(file_types, str) else file_types

        # 创建文件列表
        file_list = [(file_name, file_type, file_obj)
                     for file_name, file_type, file_obj in zip(file_names, file_types, files)]
        file_urls = await FileService.get_upload_file_urls(file_list)

        return RetUtil.response_ok(data=file_urls)

    except (Exception, RuntimeError) as e:
        LogUtil.error("异常: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="文件上传失败")

@router.get('/download_file', description="下载文件")
async def download_file(
    file_name: str = Query(..., example="1.png", description="文件名称"),
    file_url: str = Query(..., example="http://**************:9000/tiance-base/pytest/%E8%B5%B5%E9%B9%8F%E9%A3%9E.md%2", description="文件地址")
) -> StreamingResponse:
    try:
        LogUtil.log_json(describe="下载文件", kwargs=jsonable_encoder({"文件名称": file_name, "文件地址": file_url}))
        stream_response = await FileService.get_stream_response(file_name, file_url)
        return stream_response
    except (Exception, RuntimeError) as e:
        LogUtil.error("异常:{0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="文件下载失败")
