#!/usr/bin/env python
# -*- coding: UTF-8 -*-
"""
@Project ：tiance-base
@File    ：get_model_info.py
<AUTHOR>
@Date    ：2024/8/27 10.40
"""

import traceback
from fastapi import APIRouter
from fastapi.responses import Response
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from fastapi.encoders import jsonable_encoder
from service_config_manage.service.get_model_info_service import ConfigService

router = APIRouter()

@router.post('/get_model_type', summary='获取模型类型')
async def get_model_type() -> Response:
    """
    获取模型类型
    :return:
    """
    try:
        # 记录日志
        LogUtil.log_json(describe="->获取模型类型", kwargs=jsonable_encoder({}))

        # 业务逻辑处理
        model_type_list = await ConfigService.get_model_type_list()

        return RetUtil.response_ok(data=model_type_list)
    except (Exception, RuntimeError) as e:
        LogUtil.error("异常: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="系统异常，请稍后再试")


@router.post('/get_model_engine', summary='获取模型引擎类型')
async def get_model_engine() -> Response:
    """
    获取模型引擎类型
    :return:
    """
    try:
        # 记录日志
        LogUtil.log_json(describe="->获取模型引擎类型", kwargs=jsonable_encoder({}))

        # 业务逻辑处理
        model_engine_list = await ConfigService.get_model_engine_list()

        return RetUtil.response_ok(data=model_engine_list)
    except (Exception, RuntimeError) as e:
        LogUtil.error("异常: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="系统异常，请稍后再试")


@router.post('/get_model_quantization', summary='获取模型量化类型')
async def get_model_quantization() -> Response:
    """
    获取模型量化类型
    :return:
    """
    try:
        # 记录日志
        LogUtil.log_json(describe="->获取模型量化类型", kwargs=jsonable_encoder({}))

        # 业务逻辑处理
        model_quantization_list = await ConfigService.get_model_quantization_list()

        return RetUtil.response_ok(data=model_quantization_list)
    except (Exception, RuntimeError) as e:
        LogUtil.error("异常: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="系统异常，请稍后再试")


@router.post('/get_model_size', summary='获取模型大小')
async def get_model_size() -> Response:
    """
    获取模型大小
    :return:
    """
    try:
        # 记录日志
        LogUtil.log_json(describe="->获取模型大小", kwargs=jsonable_encoder({}))

        # 业务逻辑处理
        model_size_list = await ConfigService.get_model_size_list()
        return RetUtil.response_ok(data=model_size_list)
    except (Exception, RuntimeError) as e:
        LogUtil.error("异常: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="系统异常，请稍后再试")


@router.post('/get_model_format', summary='获取模型形式')
async def get_model_format() -> Response:
    """
    获取模型形式
    :return:
    """
    try:
        # 记录日志
        LogUtil.log_json(describe="->获取模型形式", kwargs=jsonable_encoder({}))
        # 业务逻辑处理

        model_format_list = await ConfigService.get_model_format_list()

        return RetUtil.response_ok(data=model_format_list)
    except (Exception, RuntimeError) as e:
        LogUtil.error("异常: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="系统异常，请稍后再试")