#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :file_route.py
@Description  :
<AUTHOR>
@Date         :2024/09/03 17:25:51
"""
import subprocess
from fastapi.responses import FileResponse
from modelscope.pipelines import pipeline
from modelscope.utils.constant import Tasks
from pymilvus.exceptions import MilvusException
from typing import List
from datetime import datetime
from bson import ObjectId
from base_configs.minio_config import MinioConfig
from fastapi.encoders import jsonable_encoder
from service_usr_manage.service.snow_util import generate_unique_id
from service_knowledge_manage.service.layout_multimode_parase.multimode_layout_parse import *
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfbase import pdfmetrics
from base_configs.mongodb_config import CollectionConfig
from base_utils.mongodb_util import MongodbUtil
from fastapi import APIRouter, HTTPException, Body, UploadFile, File, BackgroundTasks
from service_model_manage.service.chat_db_service import ChatConversationService
from fastapi import Header
from base_utils.mysql_util import SessionLocal
from service_knowledge_manage.entity.knowledge_hub_entity import KnowledgeRetrivalInfo
from service_knowledge_manage.api.routes.knowledge_hub_route import knwolege_retrieval
from typing import Optional
import asyncio
from fastapi import Request
from fastapi.responses import Response
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from base_utils.page_util import PageUtil
from service_knowledge_manage.service.knowledge_file_service import (
    Knowledge_File_service,
)
from service_knowledge_manage.entity.file_entity import (
    FileQueryInfo,
    ChunkQueryInfo
)
from service_knowledge_manage.service.vector_database_service import (
    VectorDatabaseService,
)
from service_agent_manage.service.agent_service import AgentService
import aiofiles
import traceback
import pandas as pd
import openpyxl
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle
from reportlab.lib import colors
from sqlalchemy.orm import Session
router = APIRouter()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
    
@router.post("/file_upload_parsing", summary="上传文件并解析")
async def file_upload_parsing(
    request:Request,
    file_obj: UploadFile = File(..., description="上传文件")
) -> Response:
    try:
        file_name = file_obj.filename
        LogUtil.info(f"文件名:{file_name}")
        # 1.文档上传
        # local_path = f"/root/tiance-project/tiance-base/upload/{file_name}"
        local_path = f"{Path(__file__).parents[3]}/upload/{file_name}"
        # 异步写入文件内容
        async with aiofiles.open(local_path, "wb") as temp_file:
            content = await file_obj.read()
            await temp_file.write(content)
        LogUtil.info(f"Local_path:{local_path}")
        # 2.文档解析
        docs_service = VectorDatabaseService()
        LogUtil.debug("upload success in :{}".format(local_path))
        docs = await docs_service.file_to_text(local_path,request=request)
        LogUtil.info(str(docs))
        # for doc in docs:
        #     doc.page_content = doc.page_content.replace('<br>',"")
        # csv文件解析返回的是多个doc文档的列表，需要遍历列表取出doc文档的page_content，最后拼接起来
        parse_content = docs[0].page_content if type(docs) == list else docs
        
        return RetUtil.response_ok(parse_content)
    except Exception as e:
        detail = f"上传文件失败：{traceback.format_exc()}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)
    finally:
        if local_path:
            if os.path.exists(local_path):
                os.remove(local_path)

@router.post("/slice_query", summary="查询切片方式")
async def knowledge_query() -> Response:
    try:
        result = {
            "RecursiveCharacterTextSplitter" : [500, 50], "SpacyTextSplitter" : [500, 50], "CharacterTextSplitter" : [500, 50]
        }
        
        return RetUtil.response_ok(result)

    except Exception as e:
        detail = f"查询切片方式失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/file_upload_mutltimode", summary="上传文件")
async def file_upload_mutltimode(
    chat_request: Request,
        file_obj: UploadFile = File(..., description="文件"),

) -> Response:
    try:
        account_id = chat_request.state.account_id
        file_name = file_obj.filename
        LogUtil.info(f"文件名称:{file_name}")
        remote_path = await Knowledge_File_service.get_upload_file_multimode(
            file_name, file_obj, account_id)

        # LogUtil.info(f"文件保存到本地路径:{local_path}")
        return RetUtil.response_ok({"remote_path":remote_path})
    except Exception as e:
        detail = f"上传文件至知识库失败：{traceback.format_exc()}"
        LogUtil.error(msg=detail)
        raise HTTPException(status_code=400, detail=detail)

@router.post("/call_knowledge_create_document", summary="文件上传")
async def call_knowledge_create_document(
    request: Request,
    db: Session = Depends(get_db),
    id: str = Body(..., description="知识库id", embed=True),
    chunk_method: str = Body(..., examples=["RecursiveCharacterTextSplitter"], description="切片方式"),
    file_obj: UploadFile = File(..., description="上传文件"),
    chunk_size: int = Body(500, description="文本块大小", example=500),
    chunk_overlap: int = Body(50, description="文本块重叠大小", example=50),
    separator: list[str] = Body(['\n'], description="文本分隔符"),
    is_generate: bool = Body(False, description="是否生成问答对", example=False),
    background_tasks: BackgroundTasks = BackgroundTasks()
) -> Response:
    try:
        if file_obj.filename.split('.')[-1] not in ["png", "jpg", "xlsx", "xls", "xlsd", "docx", "pdf", "txt", "ppt", "md", "csv", "pptx", "html", "doc"]:
            return RetUtil.response_error(message="文件格式不符合标准，请重新上传")
        if chunk_overlap >= chunk_size:
            return RetUtil.response_error(message="分段重叠长度不能大于分段最大长度")
        for i in range(len(separator)):
            separator[i] = bytes(separator[i], "utf-8").decode("unicode_escape")
        account_id = request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        remote_paths = []
        file_name_list = []
        chunk_list = []
        local_paths = []
        file_id_list = []

        result = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION, search_condition={"_id": ObjectId(id)})
        if len(list(result)) <= 0: return RetUtil.response_error(message="用户所属知识库不存在")

        status, local_path = await Knowledge_File_service.get_upload_file_v1(id, file_obj, file_obj.filename)
        remote_paths.append(local_path)

        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        if chunk_method == "":
            return RetUtil.response_error(message="切块方式不能为空")
        if file_obj == "":
            return RetUtil.response_error(message="上传文件不能为空")
        if local_path == False:
            LogUtil.info("文件已上传至知识库")
            return RetUtil.response_error(message="知识库中已经存在该文件")
        if len(list(MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id": id, "file_name": file_obj.filename}))):
            return RetUtil.response_error(message="知识库中已经存在该文件")

        for remote_path in remote_paths:
            try:
                upload_path = Path(__file__).parents[3] / "upload"
                local_path = f"{upload_path}/{id}$$${remote_path.split('/')[-1]}"
                await run_in_threadpool(
                    MinIoUtil.download_file, "tiance-base-temp-file-bucket", remote_path, local_path
                )
                local_paths.append(local_path)
            except Exception as e:
                LogUtil.info(f"远程文件下载失败，失败原因：<{traceback.format_exc()}>")
                return RetUtil.response_error(message="远程文件下载失败")

        for local_path in local_paths:
            try:
                file_name = local_path.split("/")[-1].split('$$$')[1]
                result, file_id = await Knowledge_File_service.insert_file_info(
                    id, file_name
                )
                file_id_list.append(file_id)
                file_name_list.append(file_name)
                await asyncio.sleep(0.1)
                LogUtil.info(f"文档{local_path}信息入库成功")
            except Exception as e:
                LogUtil.info(f"文档{local_path}信息入库出错<{traceback.format_exc()}>")
        await asyncio.sleep(0.1)

        background_tasks.add_task(run_in_thread_v2, id, remote_paths, [], chunk_method, chunk_size, chunk_overlap, separator, is_generate, chunk_list, local_paths, file_id_list, [], file_name_list, request)
        return RetUtil.response_ok("文件正在上传，请稍后")
    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        return RetUtil.response_error(message="文件上传出错，请重试")

@router.post("/call_knowledge_retrieval", summary="知识库检索")
async def call_knowledge_retrieval(
    request: Request,
    db: Session = Depends(get_db),
    id: str = Body(..., description="知识库id", embed=True),
    recall_num: int = Body(..., description="召回数量"),
    rerank_id: str = Body("", description="重拍模型"),
    rerank_num: int = Body(0, description="重排数量"),
    score: float = Body(0.5, description="阈值"),
    user_query: str = Body('Hello', description="用户检索内容"),
) -> Response:
    try:
        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        if not re.match(r"^[a-fA-F0-9]{24}$", rerank_id) and rerank_id != "":
            return RetUtil.response_error(message="重排模型id不符合规范")
        if rerank_id:
            result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.MODEL_RUN_COLLECTION, search_condition={"_id": ObjectId(rerank_id)})
            for i in result:
                model = i["model_uid"]
            if len(list(MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.MODEL_RUN_COLLECTION, search_condition={"_id": ObjectId(rerank_id)}))) == 0:
                return RetUtil.response_error(message="重排模型id不存在")
        else:
            model = ""

        account_id = request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        knowledgeRetrivalInfo = KnowledgeRetrivalInfo(id=id, kb_name="", user_query=user_query, rerank_model=model, rerank_id=rerank_id,
                                                      recall_num = recall_num, rerank_num=rerank_num, score=score)
        LogUtil.info(f"knowledgeRetrivalInfo{knowledgeRetrivalInfo}")
        result = await knwolege_retrieval(knowledgeRetrivalInfo)
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)
        if result["status"]:
            return RetUtil.response_ok(result["data"]["results"])
        else:
            return RetUtil.response_error(message=str(result["message"]))
    except MilvusException as e:
        return RetUtil.response_error(message=str(e))
    except Exception as e:
        detail = f"知识库检索失败:{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        return RetUtil.response_error(message=detail)

@router.post("/call_knowledge_file_status", summary="入库状态")
async def call_knowledge_file_status(
    request: Request,
    db: Session = Depends(get_db),
    id: str = Body(..., description="知识库id", embed=True),
    file_name: str = Body(..., description="文件名称"),
) -> Response:
    try:
        account_id = request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        result = MongodbUtil.query_docs_by_condition(CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id": id, "file_name": file_name})
        status = ""
        for i in result:
            status = str(i["status"])
        if status != "":
            return RetUtil.response_ok({"status": status})
        else:
            return  RetUtil.response_error(message="知识库或文件名称不存在")
    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/call_knowledge_file_list", summary="知识库文件列表")
async def call_knowledge_file_list(
        request: Request,
        db: Session = Depends(get_db),
        id: str = Body(..., description="知识库id", embed=True),
        file_name: Optional[str] = Body("", embed=True, examples=["file_name"], description="文件名称"),
        page: int = Body(..., embed=True, examples=[1], description="页码"),
        page_size: int = Body(..., embed=True, examples=[1], description="分页大小"),
) -> Response:
    try:
        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        if page < 0:
            return RetUtil.response_error(message="页码数量不能为负数")
        if page == "":
            return RetUtil.response_error(message="页码数量不能为空")
        if page_size < 0:
            return RetUtil.response_error(message="分页大小不能为负数")
        if page_size == "":
            return RetUtil.response_error(message="分页大小不能为空")

        account_id = request.state.account_id
        print(f"11111111111111111111{account_id}")
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id": ObjectId(id)})
        if len(list(result)) == 0:
            return RetUtil.response_error(message="知识库不存在")

        result = await file_query_page(FileQueryInfo(id=id, file_name=file_name, page=page, page_size=page_size))
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)

        if result["status"]:
            return RetUtil.response_ok(result["data"]["result"])
        else:
            return RetUtil.response_error(message=str(result["message"]))

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/call_delete_knowledge_file", summary="知识库文件删除")
async def call_delete_knowledge_file(
        request: Request,
        db: Session = Depends(get_db),
        id: str = Body(..., description="知识库id", embed=True),
        file_name: str = Body(..., embed=True, examples=["file_name"], description="文件名称"),
) -> Response:
    try:
        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        if file_name == "":
            return RetUtil.response_error(message="文件名称不能为空")

        account_id = request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id":ObjectId(id)})
        if len(list(result)) == 0:
            return RetUtil.response_error(message="知识库不存在")
        result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id":id, "file_name":file_name})
        if len(list(result)) == 0:
            return RetUtil.response_error(message="知识库文件不存在")

        file_id = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id":id, "file_name":file_name})[0]["_id"]
        result = await file_delete(file_name=file_name, id=id, file_id=file_id)
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)

        if result["status"]:
            return RetUtil.response_ok("知识库文件删除成功")
        else:
            return RetUtil.response_error(message=str(result["message"]))

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/call_query_knowledge_chunk", summary="查询知识库切片")
async def call_query_knowledge_chunk(
        request: Request,
        db: Session = Depends(get_db),
        id: str = Body(..., description="知识库id", embed=True),
        file_name: Optional[list] = Body([], embed=True, examples=["file_name"], description="文件名称列表"),
        page: int = Body(..., embed=True, examples=[1], description="页码"),
        page_size: int = Body(..., embed=True, examples=[1], description="分页大小"),
) -> Response:
    try:
        id_list = []

        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        if page < 0:
            return RetUtil.response_error(message="页码数量不能为负数")
        if page == "":
            return RetUtil.response_error(message="页码数量不能为空")
        if page_size < 0:
            return RetUtil.response_error(message="分页大小不能为负数")
        if page_size == "":
            return RetUtil.response_error(message="分页大小不能为空")

        account_id = request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id":ObjectId(id)})
        if len(list(result)) == 0:
            return RetUtil.response_error(message="知识库不存在")
        if file_name:
            for file in file_name:
                result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id":id, "file_name":file})
                for i in result:
                    id_list.append(i["_id"])

        result = await chunk_result_query(ChunkQueryInfo(id=id, file_id=id_list, page=page, page_size=page_size))
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)
        if result["status"]:
            return RetUtil.response_ok(result["data"]["result"])
        else:
            return RetUtil.response_error(message=str(result["message"]))

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/file_obj_preview", summary="上传文件预览")
async def file_obj_preview(
        file_obj: UploadFile = File(..., description="文件"),
        background_tasks: BackgroundTasks = BackgroundTasks()
) -> Response:
    try:
        # 获取保存文件夹、文件名称与文件名（不带扩展）
        upload_path = Path(__file__).parents[3] / "upload"
        file_name = file_obj.filename
        file_name_without_extension = Path(file_name).stem

        # 定义保存路径：本地路径、输出路径、最终路径
        unique_id = generate_unique_id('FILE_', datacenter_id=1, worker_id=1)
        local_path = f"{upload_path}/{unique_id}$$${file_name}"
        final_path = ""
        output_path = f"{upload_path}/{unique_id}$$${file_name_without_extension}.pdf"
        local_path = os.path.abspath(local_path).replace('\\', '/')
        output_path = os.path.abspath(output_path).replace('\\', '/')

        def convert_to_pdf(local_path, upload_path):
            try:
                subprocess.run(["soffice", "--headless", "--convert-to", "pdf", "--outdir",
                                upload_path, local_path], check=True)
            except subprocess.CalledProcessError as e:
                LogUtil.info(f"转换pdf文件出错：<{traceback.format_exc()}>")

        # word文件处理 先读取doc/docx/txt文件内容，将其保存为docx文件，在使用docx2pdf的convert方法将文件转换为pdf类型
        if file_obj.filename.endswith('.docx') or file_obj.filename.endswith('.doc'):
            count = 0
            while count < 3:
                try:
                    if file_name.endswith('.doc') or file_name.endswith('.txt'):
                        final_path = f"{upload_path}/{unique_id}$$${file_name[:-4]}.docx"
                        final_path = os.path.abspath(final_path).replace('\\', '/')
                    else:
                        final_path = local_path
                    async with aiofiles.open(final_path, "wb") as temp_file:
                        content = await file_obj.read()
                        await temp_file.write(content)
                        await asyncio.sleep(1)
                    convert_to_pdf(final_path, upload_path)
                    LogUtil.info(f"转换docx文件成功")
                    break
                except Exception as e:
                    LogUtil.info(f"转换docx文件失败：{str(traceback.format_exc())},失败次数：第{count}次")
                    count += 1
            if count == 3:
                return RetUtil.response_error(message="转换docx文件失败")


        # excel文件处理 先使用pandas读取xls/csv文件内容，将其转换为xlsx文件，再使用reportlab库将excel文件内容写入pdf
        elif file_obj.filename.endswith('.xlsx') or file_obj.filename.endswith('.xls') or file_obj.filename.endswith('.csv'):
            try:
                async with aiofiles.open(local_path, "wb") as temp_file:
                    content = await file_obj.read()
                    await temp_file.write(content)
                if file_name.endswith('.csv'):
                    df = pd.read_csv(local_path)
                    df = df.fillna(method="ffill")
                else:
                    df = pd.read_excel(local_path)
                    df = df.fillna(method="ffill")
                final_path = local_path + '.xlsx'
                final_path = os.path.abspath(final_path).replace('\\', '/')
                df.to_excel(final_path, index=False, header=True)
            except Exception as e:
                LogUtil.info(f"转换docx文件失败：{str(traceback.format_exc())}")
                return RetUtil.response_error(message = f"转换excel文件失败，失败原因{e}")

            # 设置文件格式，将excel文件内容写入pdf
            font_path = Path(__file__).parents[3] / "Font/simhei.ttf"
            pdfmetrics.registerFont(TTFont('simsun', font_path))
            wb = openpyxl.load_workbook(final_path)
            sheet = wb.active
            data = []
            for row in sheet.iter_rows(values_only=True):
                data.append(list(row))
            for row in data:
                for col_index, cell in enumerate(row):
                    if isinstance(cell, str) and len(cell) > 100:
                        wrapped_text = "\n".join(
                            [chunk for chunk in [cell[i:i + 100] for i in range(0, len(cell), 100)] if chunk])
                        row[col_index] = wrapped_text
            max_col_widths = [max([len(str(row[i])) for row in data]) for i in range(len(data[0]))]
            max_col_widths = [i if i <= 100 else 100 for i in max_col_widths]
            table_width = sum(max_col_widths) * 8
            table_height = len(data) * 30
            doc = SimpleDocTemplate(output_path, pagesize=(table_width, table_height))
            elements = []
            t = Table(data)
            t_style = TableStyle([
                ('FONT', (0, 0), (-1, -1), 'simsun', 2),
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 0), (-1, -1), 5),
                ('LEADING', (0, 0), (-1, -1), 6),
                ('AUTOPAD', (0, 0), (-1, -1), 1),
                 ])
            t.setStyle(t_style)
            elements.append(t)
            doc.build(elements)

        # pdf文件不做处理，直接返回
        elif file_obj.filename.endswith('.pdf'):
            async with aiofiles.open(local_path, "wb") as temp_file:
                content = await file_obj.read()
                await temp_file.write(content)
            output_path = local_path

        else:
            for i in range(3):
                async with aiofiles.open(local_path, "wb") as temp_file:
                    content = await file_obj.read()
                    await temp_file.write(content)
                    await asyncio.sleep(0.5)
                if os.path.exists(local_path):
                    break
            if not os.path.exists(local_path):
                return RetUtil.response_error(message="文件保存失败，请重试")

            if file_obj.filename.endswith('.jpg') or file_obj.filename.endswith('.png'):
                import fitz  # PyMuPDF
                doc = fitz.open()  # 创建一个空的 PDF 文档
                page = doc.new_page()  # 默认添加 A4 大小的页面
                rect = fitz.Rect(100, 100, 400, 400)  # 定义图片的位置和大小
                page.insert_image(rect, filename=local_path)  # 插入图片

                doc.save(output_path)
                doc.close()
            elif file_name.endswith('.txt'):
                from fpdf import FPDF
                font_path = Path(__file__).parents[3] / "Font/simhei.ttf"
                pdf = FPDF()
                pdf.add_page()
                pdf.add_font("simsun", "", font_path, uni=True)  # uni=True 支持 Unicode 字符
                pdf.set_font("simsun", size=8)

                with open(local_path, "r", encoding="utf-8") as file:
                    lines = file.read().splitlines()  # 使用 splitlines() 读取所有行，自动处理换行符

                for line in lines:
                    pdf.multi_cell(0, 5, txt=line, align="L")  # 写入每一行
                pdf.output(output_path)
            else:
                convert_to_pdf(local_path, upload_path)

        LogUtil.info(f"local_path:==={local_path}===,final_path:==={final_path},output_path==={output_path}")
        def delete_file(file_path: str):
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    LogUtil.info(f"文件已删除: {file_path}")
            except Exception as e:
                LogUtil.error(f"删除文件失败: {file_path}, 错误信息: {e}")

        # 将删除文件的任务添加到 background_tasks
        background_tasks.add_task(delete_file, local_path)
        background_tasks.add_task(delete_file, output_path)
        background_tasks.add_task(delete_file, final_path)

        return FileResponse(
            path=output_path,
            media_type="application/pdf",
            filename=f"{file_name_without_extension}.pdf"
        )

    except Exception as e:
        detail = f"文件预览失败：失败原因<{traceback.format_exc()}>"
        LogUtil.error(msg=detail)
        return RetUtil.response_error(message=detail)


@router.post("/file_url_preview", summary="远程文件预览")
async def file_url_preview(
        id: str = Body(..., description="知识库id", embed=True),
        url: str = Body(..., embed=True, description="远程文件地址"),
        background_tasks: BackgroundTasks = BackgroundTasks()
) -> Response:
    try:
        # 下载文件到本地
        upload_path = Path(__file__).parents[3] / "upload"
        file_name = url.split('/')[-1]
        file_name_without_extension = Path(file_name).stem
        unique_id = generate_unique_id('FILE_', datacenter_id=1, worker_id=1)
        local_path = f"{upload_path}/{unique_id}$$${file_name}"
        final_path = ""
        output_path = f"{upload_path}/{unique_id}$$${file_name_without_extension + '.pdf'}"
        local_path = os.path.abspath(local_path).replace('\\', '/')
        output_path = os.path.abspath(output_path).replace('\\', '/')

        if id in url:
            for i in range(3):
                MinIoUtil.download_file(bucket_name="tiance-base", remote_path=url, local_path=local_path)
                await asyncio.sleep(0.5)
                if os.path.exists(local_path):
                    break
        else:
            for i in range(3):
                MinIoUtil.download_file(bucket_name="tiance-base-temp-file-bucket", remote_path=url, local_path=local_path)
                await asyncio.sleep(0.5)
                if os.path.exists(local_path):
                    break

        if not os.path.exists(local_path):
            return RetUtil.response_error(message="文件保存失败，请重试")

        with open(local_path, "rb") as file:
            content = file.read()

        def convert_to_pdf(local_path, upload_path):
            try:
                subprocess.run(["soffice", "--headless", "--convert-to", "pdf", "--outdir",
                                upload_path, local_path], check=True)
            except subprocess.CalledProcessError as e:
                LogUtil.info(f"转换pdf文件出错：<{traceback.format_exc()}>")
        # word文件处理 先读取doc/docx/txt文件内容，将其保存为docx文件，在使用docx2pdf的convert方法将文件转换为pdf类型
        if file_name.endswith('.docx') or file_name.endswith('.doc'):
            count = 0
            while count < 3:
                try:
                    if file_name.endswith('.doc') or file_name.endswith('.txt'):
                        final_path = f"{upload_path}/{unique_id}$$${file_name[:-4]}.docx"
                        final_path = os.path.abspath(final_path).replace('\\', '/')
                    else:
                        final_path = local_path
                    async with aiofiles.open(final_path, "wb") as temp_file:
                        await temp_file.write(content)
                    await asyncio.sleep(1)
                    convert_to_pdf(final_path, upload_path)
                    LogUtil.info(f"转换docx文件成功")
                    break
                except Exception as e:
                    LogUtil.info(f"转换docx文件失败：{str(traceback.format_exc())},失败次数：第{count}次")
                    count += 1
            if count == 3:
                return RetUtil.response_error(message="转换docx文件失败")

        # excel文件处理 先使用pandas读取xls/csv文件内容，将其转换为xlsx文件，再使用reportlab库将excel文件内容写入pdf
        elif file_name.endswith('.xlsx') or file_name.endswith('.xls') or file_name.endswith('.csv'):
            try:
                async with aiofiles.open(local_path, "wb") as temp_file:
                    await temp_file.write(content)
                if file_name.endswith('.csv'):
                    df = pd.read_csv(local_path)
                    df = df.fillna(method="ffill")
                else:
                    df = pd.read_excel(local_path)
                    df = df.fillna(method="ffill")
                final_path = local_path + '.xlsx'
                final_path = os.path.abspath(final_path).replace('\\', '/')
                df.to_excel(final_path, index=False, header=True)
            except Exception as e:
                LogUtil.info(f"转换docx文件失败：{str(traceback.format_exc())}")
                return RetUtil.response_error(message = f"转换excel文件失败，失败原因{e}")
            font_path = Path(__file__).parents[3] / "Font/simhei.ttf"
            pdfmetrics.registerFont(TTFont('simsun', font_path))
            wb = openpyxl.load_workbook(final_path)
            sheet = wb.active
            data = []
            for row in sheet.iter_rows(values_only=True):
                data.append(list(row))
            for row in data:
                for col_index, cell in enumerate(row):
                    if isinstance(cell, str) and len(cell) > 100:
                        wrapped_text = "\n".join(
                            [chunk for chunk in [cell[i:i + 100] for i in range(0, len(cell), 100)] if chunk])
                        row[col_index] = wrapped_text
            max_col_widths = [max([len(str(row[i])) for row in data]) for i in range(len(data[0]))]
            max_col_widths = [i if i <= 100 else 100 for i in max_col_widths]
            table_width = sum(max_col_widths) * 8
            table_height = len(data) * 30
            doc = SimpleDocTemplate(output_path, pagesize=(table_width, table_height))
            elements = []
            t = Table(data)
            t_style = TableStyle([
                ('FONT', (0, 0), (-1, -1), 'simsun', 2),
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 0), (-1, -1), 5),
                ('LEADING', (0, 0), (-1, -1), 6),
                ('AUTOPAD', (0, 0), (-1, -1), 1),
            ])
            t.setStyle(t_style)
            elements.append(t)
            doc.build(elements)

        # pdf文件不做处理，直接返回
        elif file_name.endswith('.pdf'):
            async with aiofiles.open(local_path, "wb") as temp_file:
                await temp_file.write(content)
            output_path = local_path

        else:
            if file_name.endswith('.jpg') or file_name.endswith('.png'):
                import fitz  # PyMuPDF
                doc = fitz.open()  # 创建一个空的 PDF 文档
                page = doc.new_page()  # 默认添加 A4 大小的页面
                rect = fitz.Rect(100, 100, 400, 400)  # 定义图片的位置和大小
                page.insert_image(rect, filename=local_path)  # 插入图片

                doc.save(output_path)
                doc.close()
            elif file_name.endswith('.txt'):
                from fpdf import FPDF
                font_path = Path(__file__).parents[3] / "Font/simhei.ttf"
                pdf = FPDF()
                pdf.add_page()
                pdf.add_font("simsun", "", font_path, uni=True)  # uni=True 支持 Unicode 字符
                pdf.set_font("simsun", size=8)

                with open(local_path, "r", encoding="utf-8") as file:
                    lines = file.read().splitlines()  # 使用 splitlines() 读取所有行，自动处理换行符

                for line in lines:
                    pdf.multi_cell(0, 5, txt=line, align="L")  # 写入每一行

                pdf.output(output_path)

            else:
                convert_to_pdf(local_path, upload_path)
        LogUtil.info(f"local_path:==={local_path}===,final_path:==={final_path},output_path==={output_path}")

        def delete_file(file_path: str):
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    LogUtil.info(f"文件已删除: {file_path}")
            except Exception as e:
                LogUtil.error(f"删除文件失败: {file_path}, 错误信息: {e}")

        # 将删除文件的任务添加到 background_tasks
        background_tasks.add_task(delete_file, local_path)
        background_tasks.add_task(delete_file, output_path)
        background_tasks.add_task(delete_file, final_path)

        return FileResponse(
            path=output_path,
            media_type="application/pdf",
            filename=f"{file_name_without_extension}.pdf"
        )

    except Exception as e:
        detail = f"上传文件预览失败：失败原因<{traceback.format_exc()}>"
        LogUtil.error(msg=detail)
        return RetUtil.response_error(detail)

@router.post("/knowledge_file_download", summary="知识库文件下载")
async def knowledge_file_download(
        request: Request,
        db: Session = Depends(get_db),
        id: str = Body(..., description="知识库id", embed=True),
        file_name: str = Body(..., embed=True, examples=["file_name"], description="文件名称列表"),
) -> Response:
    try:
        upload_path = Path(__file__).parents[3] / "upload"

        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        if file_name == "":
            return RetUtil.response_error(message="文件名称不能为空")

        account_id = request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        kb_name_result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id":ObjectId(id)})
        if len(list(kb_name_result)) == 0:
            return RetUtil.response_error(message="知识库不存在")
        file_name_result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id":id, "file_name":file_name})
        if len(list(file_name_result)) == 0:
            return RetUtil.response_error(message="知识库文件不存在")

        try:
            remote_path = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, search_condition={"knowledge_id":id, "file_name":file_name})[0]["remote_path"]
            local_path = f"{upload_path}/{id}$$${file_name}"
            await run_in_threadpool(
                MinIoUtil.download_file, "tiance-base", remote_path, local_path
            )
        except Exception as e:
            LogUtil.error(f"远程下载知识库文件失败：{str(traceback.format_exc())}")
            return RetUtil.response_error(message="文件下载失败，请检查下载文件是否成功上传")

        return FileResponse(path=local_path, filename=file_name, media_type='application/octet-stream')

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/call_knowledge_create_document_by_zip", summary="上传压缩包")
async def call_knowledge_create_document_by_zip(
    chat_request: Request,
    db: Session = Depends(get_db),
    id: str = Body(..., description="知识库id", embed=True),
    file_obj: UploadFile = File(..., description="压缩包文件"),
    chunk_method: str = Body("RecursiveCharacterTextSplitter", examples=["RecursiveCharacterTextSplitter"],description="切片方式"),
    chunk_size: int = Body(500, description="文本块大小", example="500"),
    chunk_overlap: int = Body(50, description="文本块重叠大小", example="50"),
    separator: list[str] = Body(['\n'], description="文本分隔符"),
    is_generate: bool = Body(False, description="是否生成问答对", example=False),
    delete_files: list[str] = Body([], description="需要删除的文件ID列表"),
    background_tasks: BackgroundTasks = BackgroundTasks()
) -> Response:
    """
    上传压缩包并解压文件
    """
    try:
        for i in range(len(separator)):
            separator[i] = bytes(separator[i], "utf-8").decode("unicode_escape")

        try:
            account_id = chat_request.state.account_id
        except:
            return RetUtil.response_error(message="app-id无效")
        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")

        account_id = chat_request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        # chunk_overlap、chunk_size判断
        if chunk_size < chunk_overlap:
            return RetUtil.response_error(message="文本块大小必须大于重叠大小")

        if file_obj.filename.split('.')[-1] not in ["zip", "7z"]:
            return RetUtil.response_error(message="文件格式暂不支持，请上传压缩包文件")

        if len(list(MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id":ObjectId(id)}))) == 0:
            return RetUtil.response_error(message="知识库不存在，请重新选择")

        # 上传并解压压缩包
        results = []
        result = await Knowledge_File_service.upload_and_extract_archive(file_obj, account_id)
        if len(result["files"]) != 1:
            result["files"] = result["files"][1:]

        # 检查解压后的文件是否重名
        file_names = [os.path.basename(file) for file in result["files"]]
        repeat_files = await Knowledge_File_service.repeat_file_detect(id, file_names)
        if repeat_files:
            return RetUtil.response_error(message=f"以下文件已存在：{', '.join(repeat_files)}，请重命名后上传")

        for file in result["files"]:
            file_name = os.path.basename(file)
            # 上传文件到MinIO临时桶:tiance-base-temp-file-bucket
            date = datetime.now().strftime('%Y_%m_%d')  # 日期
            folder = generate_unique_id('Temp', datacenter_id=1, worker_id=1)  # 随机id
            remote_path = f"{date}/{folder}/{file_name}"  # 文件路径，以{kb_name}$$${file_name}命名文件名称
            bucket_name = "tiance-base-temp-file-bucket"  # 桶名称
            await run_in_threadpool(
                MinIoUtil.upload_file, bucket_name, remote_path, file)
            results.append({"file_name": file_name, "remote_path": remote_path})
            await asyncio.sleep(1)
        #return RetUtil.response_ok(results)
        remote_paths = [item["remote_path"] for item in results]

        # 调用向量入库接口

        file_id_list = []
        file_name_list = []
        local_paths = result["files"]
        for local_path in local_paths:
            try:
                file_name = os.path.basename(local_path)
                result, file_id = await Knowledge_File_service.insert_file_info(
                    id, file_name
                )
                file_id_list.append(file_id)
                file_name_list.append(file_name)
                await asyncio.sleep(0.1)
                LogUtil.info(f"文档{local_path}信息入库成功")
            except Exception as e:
                LogUtil.info(f"文档{local_path}信息入库出错<{traceback.format_exc()}>")

        background_tasks.add_task(run_in_thread_v2, id, remote_paths, delete_files, chunk_method, chunk_size,
                                  chunk_overlap, separator, is_generate, [], local_paths, file_id_list, [], file_name_list, chat_request)

        return RetUtil.response_ok(f"压缩包上传成功，{remote_paths}文件正在解析和向量入库中")

    except Exception as e:
            detail = f"上传压缩包失败:{str(e)}"
            LogUtil.error(msg=detail)
            # 返回HTTP错误响应
            return RetUtil.response_error(message=detail)

@router.post("/call_upload_parsing", summary="文件解析外部接口")
async def call_upload_parsing(
        request: Request,
        file_obj: UploadFile = File(..., description="上传文件"),
) -> Response:
    try:
        file_extension = os.path.splitext(file_obj.filename)[1].lower()
        ALLOWED_EXTENSIONS = ['png', 'jpg', 'xlsx', 'xls', 'xslx', 'doc', 'docx', 'pdf', 'txt', 'ppt', 'md', 'csv', 'pptx', 'html']
        if file_extension not in [f'.{ext}' for ext in ALLOWED_EXTENSIONS]:
            return RetUtil.response_error(data=f"不支持的文件类型。允许的文件类型包括：{', '.join([f'.{ext}' for ext in ALLOWED_EXTENSIONS])}")
        file_size = len(await file_obj.read())
        # 重置文件指针到开头，以便后续处理
        await file_obj.seek(0)
        # 检查文件大小是否超过50MB
        if file_size > 50 * 1024 * 1024:  # 50MB
            return RetUtil.response_error(data="文件大小只允许50MB")
        result = await file_upload_parsing(request, file_obj)
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)

        if result["status"]:
            return RetUtil.response_ok(data=result["data"])

        else:
            return RetUtil.response_error(message=str(result["message"]))

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

################################################################################# 改造知识库

@router.post("/file_upload_1_v2", summary="上传文件")
async def file_upload_1_v2(
        id: str = Body(..., description="知识库id", embed=True),
        file_obj: UploadFile = File(..., description="文件"),
) -> Response:
    try:
        LogUtil.log_json(
            describe=f"->文件上传到知识库id为 {id} 知识库中"
        )
        file_name = file_obj.filename
        LogUtil.info(f"将文件《{file_name}》上传到《{id}》")

        status, result = await Knowledge_File_service.get_upload_file_v1(
            id, file_obj, file_name)

        if not status:
            return RetUtil.response_error(message=f"文件<{file_name}>上传失败<{result}>")

        return RetUtil.response_ok({"remote_path": result})

    except Exception as e:
        detail = f"上传知识库文件失败：《{e}》"
        LogUtil.info(f"上传知识库文件失败，失败原因：{str(traceback.format_exc())}")
        return RetUtil.response_error(message=detail)

@router.post("/file_upload_2_v2", summary="切片解析")
async def file_upload_2_v2(
    request:Request,
    id: str = Body(..., description="知识库id", embed=True),
    remote_paths: list[str] = Body(..., example=["/tiance-base-temp-file-bucket/2025_03_25/Temp1904468160933924864/test_xyj123456-实验室考勤规则.pdf"], description="文件远程路径列表"),
    chunk_method: str = Body("", examples=["RecursiveCharacterTextSplitter"], description="切片方式"),
    chunk_size: int = Body(500, description="文本块大小", example=500),
    chunk_overlap: int = Body(50, description="文本块重叠大小", example=50),
    separator: list = Body(['\n'], description="文本分隔符"),
    is_generate: bool = Body(False, description="是否生成问答对", example=False),
) -> Response:
    try:

        LogUtil.log_json(
            describe="->切片解析参数",
            kwargs=jsonable_encoder(
                {"id": id, "remote_paths": remote_paths, "chunk_method": chunk_method,
                 "chunk_size": chunk_size, "chunk_overlap": chunk_overlap, "separator": separator, "is_generate": is_generate}))
        local_paths = []
        output_path = ""
        if chunk_overlap >= chunk_size:
            return RetUtil.response_error(message="分段重叠长度不能大于分段最大长度")
        # 定义文件下载路径与模型路径
        upload_path = Path(__file__).parents[3] / "upload"
        result = []
        chunk_list = []
        model_path = Path(__file__).parents[3] / "nlp_bert_document-segmentation_chinese-base"

        for remote_path in remote_paths:
            # 文档解析
            file_name = remote_path.split('/')[-1]
            docs_service = VectorDatabaseService()
            local_path = f"{upload_path}/{id}$$${file_name}"
            local_paths.append(local_path)

            try:
                # 文档下载到本地
                MinIoUtil.download_file(bucket_name="tiance-base-temp-file-bucket", remote_path=remote_path, local_path=local_path)

            except Exception as e:
                LogUtil.info(f"远程服务器下载文件到本地失败，错误原因：<{str(traceback.format_exc())}>")
                return RetUtil.response_error(message="远程服务器下载文件到本地失败")

            try:
                if chunk_method:
                    docs = ""
                    chunks = []
                    total_page = 0
                    # 解析文本
                    time_1 = time.time()

                    if local_path.endswith((".doc", ".docx")):
                        # 获取文件所在目录
                        output_dir = os.path.dirname(local_path)
                        # 执行 soffice 命令进行转换
                        subprocess.run([
                            "soffice",
                            "--headless",
                            "--convert-to", "pdf",
                            "--outdir", output_dir,
                            local_path
                        ], check=True)
                        local_path = os.path.splitext(local_path)[0] + ".pdf"

                    if local_path.endswith(".pdf"):
                        docs_services = layout_multimode_Parse()
                        chunks = await docs_services.extract_content_from_pdf(file_path=local_path, knowledge_id=id, request=request, is_preview=True, file_name=file_name, chunk_method=chunk_method, chunk_size=chunk_size, chunk_overlap=chunk_overlap, separator=separator)
                        chunk_list.extend(chunks)
                        time_2 = time.time()
                        print(f"解析文档总耗时为{time_2 - time_1}")
                    else:
                        time_1 = time.time()
                        docs = await docs_service.file_to_text(local_path, request)
                        time_2 = time.time()
                        print(f"解析文档总耗时为{time_2 - time_1}")
                else:
                    time_1 = time.time()
                    docs = await docs_service.file_to_text(local_path,request)
                    time_2 = time.time()
                    print(f"解析文档总耗时为{time_2 - time_1}")

            except Exception as e:
                LogUtil.info(f"文件解析失败，失败原因：<{str(traceback.format_exc())}>")
                return RetUtil.response_error(message="文件解析失败")
            try:
                # 非智能切片
                if chunk_method:
                    if not local_path.endswith(".pdf"):
                        chunks = await Knowledge_File_service.embedding_document(
                            docs, file_name, chunk_method, chunk_size, chunk_overlap, separator)
                        chunk_list.extend(chunks)
                # 智能切片
                else:
                    p = pipeline(
                        task=Tasks.document_segmentation,
                        model=str(model_path),
                        device='cuda:0')
                    docs = docs[0].page_content if type(docs) == list else docs
                    doc_result = p(documents=docs)
                    tmp = re.split(r'\n\t', re.sub(r'^\t', '', doc_result['text']))
                    text = list(filter(None, tmp))
                    chunk_list.extend(text)
                    LogUtil.info(f"智能切块内容：{chunk_list}")
            except Exception as e:
                LogUtil.info(f"文件切块失败，出错原因：<{str(traceback.format_exc())}>")
                return RetUtil.response_error(message="文件切块失败")
            # 只取前五个切片
            if len(chunk_list) > 5:
                break
        try:
            # 是否生成问答对
            if is_generate:
                for i in chunk_list[:5]:
                    try:
                        # 生成问题
                        question = await Knowledge_File_service.generate_question_answer(i)
                        question = question.replace("\n", " ")
                        question = question.replace("\\n", " ")
                        # 组装问答对
                        question_answer = json.dumps({
                            "QUESTION": question,
                            "ANSWER": i
                        })
                        result.append({"chunk_content": i, "question": question, "token": len(question_answer)})
                    except:
                        result.append({"chunk_content": i, "question": "问答对生成错误，请重试", "token": 0})
                return RetUtil.response_ok(result)

            else:
                for i in chunk_list[:5]:
                    result.append({"chunk_content": i, "token": len(i)})
                return RetUtil.response_ok(result)

        except Exception as e:
            LogUtil.info(f"文件解析失败{str(traceback.format_exc())}")
            return RetUtil.response_error(message="文件解析失败")
    except Exception as e:
        detail = f"预览文件切片错误：失败原因<{traceback.format_exc()}>"
        LogUtil.error(msg=detail)
        return RetUtil.response_error(message=detail)

    finally:
        if local_paths:
            for path in local_paths:
                if os.path.exists(path):
                    os.remove(path)
        if output_path:
            os.remove(output_path)

@router.post("/file_upload_3_v2", summary="向量入库")
async def file_upload_3_v2(
    request:Request,
    id: str = Body(..., description="知识库id", embed=True),
    remote_paths: list[str] = Body(..., embed=True, description="远程文件路径；列表"),
    delete_files: list[str] = Body(..., embed=True, description="远程文件路径；列表"),
    chunk_method: str = Body("", examples=["RecursiveCharacterTextSplitter"], description="切片方式"),
    chunk_size: int = Body(500, description="文本块大小", example="500"),
    chunk_overlap: int = Body(50, description="文本块重叠大小", example="50"),
    is_generate: bool = Body(False, description="是否生成问答对", example=False),
    separator: list = Body(['\n'], description="文本分隔符"),
    background_tasks: BackgroundTasks = BackgroundTasks()
) -> Response:
    try:
        LogUtil.log_json(
            describe="->向量入库参数",
            kwargs=jsonable_encoder(
                {"id": id, "remote_paths": remote_paths, "chunk_method": chunk_method, "delete_files": delete_files,
                 "chunk_size": chunk_size, "chunk_overlap": chunk_overlap, "separator": separator,
                 "is_generate": is_generate}))

        file_name_list = []
        chunk_list = []
        local_paths = []
        file_id_list = []
        upload_path = Path(__file__).parents[3] / "upload"
        if chunk_overlap >= chunk_size:
            return RetUtil.response_error(message="分段重叠长度不能大于分段最大长度")
        # 覆盖文件删除
        for delete_id in delete_files:
            try:
                file_name = MongodbUtil.query_doc_by_id(CollectionConfig.UPLOAD_FILE_INFO_COLLECTION, delete_id)[
                    "file_name"]
                MongodbUtil.del_docs_by_condition(CollectionConfig.UPLOAD_FILE_INFO_COLLECTION,
                                                  del_condition={"_id": delete_id})
                result = await file_delete(file_name, id, delete_id)
                if not result:
                    return RetUtil.response_error(message="覆盖文件失败")
            except Exception as e:
                LogUtil.info(f"覆盖文件失败，失败原因：<{traceback.format_exc()}>")
                return RetUtil.response_error(message="覆盖文件失败")

        # 远程文件下载到本地
        for remote_path in remote_paths:
            local_path = ""
            count = 0
            while not os.path.exists(local_path):
                try:
                    local_path = f"{upload_path}/{remote_path.split('/')[1]}$$${remote_path.split('/')[-1]}"
                    local_path = os.path.abspath(local_path).replace('\\', '/')
                    LogUtil.info(f"文件下载的本地路径为{local_path}")
                    await run_in_threadpool(
                        MinIoUtil.download_file, "tiance-base-temp-file-bucket", remote_path, local_path
                    )
                    await asyncio.sleep(0.1)
                    if os.path.exists(local_path):
                        file_name_list.append(remote_path.split('/')[-1])
                        local_paths.append(local_path)
                        LogUtil.info(f"远程文件<<<{local_path}>>>下载到本地成功")
                        break
                    else:
                        LogUtil.info(f"远程文件<<<{local_path}>>>下载到本地失败，失败次数：{count + 1}")
                        count += 1
                    if count == 3:
                        return RetUtil.response_error(message="远程文件下载失败")
                except Exception as e:
                    LogUtil.info(f"远程文件下载失败，失败原因：<{traceback.format_exc()}>")
                    return RetUtil.response_error(message="远程文件下载失败")

        # 重复文件名称检测，包括已上传和正在上传的文件
        repeat_file_list = await Knowledge_File_service.repeat_file_detect(id, file_name_list)
        if repeat_file_list == False:
            return RetUtil.response_error(message="查询重复文件失败")

        # 上传文件基本信息入库
        for file_name in file_name_list:
            try:
                result, file_id = await Knowledge_File_service.insert_file_info(
                    id, file_name
                )
                file_id_list.append(file_id)
                await asyncio.sleep(0.1)
                LogUtil.info(f"文档{file_name}信息入库成功")
            except Exception as e:
                LogUtil.info(f"文档{file_name}信息入库出错<{traceback.format_exc()}>")

        await asyncio.sleep(0.1)
        # 执行后台任务
        background_tasks.add_task(run_in_thread_v2, id, remote_paths, delete_files, chunk_method, chunk_size,
                                  chunk_overlap, separator, is_generate,
                                  chunk_list, local_paths, file_id_list, repeat_file_list, file_name_list, request)

        LogUtil.info(f"远程文件路径：{remote_paths}")
        LogUtil.info(f"本地文件路径：{local_paths}")
        return RetUtil.response_ok("文件上传成功，向量入库中，请稍等")

    except Exception as e:
        detail = f"上传文件至知识库失败：{traceback.format_exc()}"
        LogUtil.error(msg=detail)
        return RetUtil.response_error(message=f"上传文件至知识库失败，失败原因：<{e}>")

def run_in_thread_v2(id: str, remote_paths: List[str], delete_files: List[str], chunk_method: str, chunk_size: int, chunk_overlap: int, separator: list, is_generate: bool,
                     chunk_list: List[str], local_paths: List[str], file_id_list: List[str], repeat_file_list: List[str], file_name_list: List[str], request:Request
):
    try:
        asyncio.run(process_file_upload_v2(id, remote_paths, delete_files, chunk_method, chunk_size, chunk_overlap, separator, is_generate,
                                           chunk_list, local_paths, file_id_list, repeat_file_list, file_name_list, request=request))
    except Exception as e:
        LogUtil.error(msg=f"处理文件上传时出错: {traceback.format_exc()}")

async def process_file_upload_v2(id: str,
        remote_paths: List[str],
        delete_files: List[str],
        chunk_method: str,
        chunk_size: int,
        chunk_overlap: int,
        separator: list,
        is_generate: bool,
        chunk_list: List[str],
        local_paths: List[str],
        file_id_list: List[str],
        repeat_file_list: List[str],
        file_name_list: List[str],
        request:Request
):
    try:
        for repeat_file in repeat_file_list:
            MongodbUtil.update_docs_by_condition("upload_file_info",
                                                 {'id': id, 'file_name': repeat_file, "status": 1},
                                                 replace_data={"$set": {"status": 2, "info": f"与{repeat_file}文件重复"}})
            LogUtil.info(f"文件<{repeat_file}>重复了")

        for i in range(len(local_paths)):
            try:
                file_name = file_name_list[i]
                if file_name in repeat_file_list:
                    continue
                LogUtil.info(f"开始执行文件12345：{local_paths[i]}")
                file_id = file_id_list[i]
                if not local_paths[i].endswith((".pdf", ".png", ".jpg", ".doc", ".docx")):
                # 旧解析方法
                    pdf_remote_path = ""
                    docs_service = VectorDatabaseService()
                    docs = await docs_service.file_to_text(local_paths[i],request=request)
                    all_text_list,metadata_list,images_list = [],[],[]
                    print(1)
                else:
                    pdf_remote_path = ""
                    if local_paths[i].endswith((".doc", ".docx")):
                        # 获取文件所在目录
                        output_dir = os.path.dirname(local_paths[i])
                        # 执行 soffice 命令进行转换
                        subprocess.run([
                            "soffice",
                            "--headless",
                            "--convert-to", "pdf",
                            "--outdir", output_dir,
                            local_paths[i]
                        ], check=True)
                        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
                        pdf_path = os.path.splitext(local_paths[i])[0] + f".pdf"
                        print(f"pdf_path:==============={pdf_path}")
                        pdf_remote_path = f'{id}/transformer/{os.path.splitext(os.path.basename(local_paths[i]))[0] + f"_{timestamp}.pdf"}'
                        minioutil = MinIoUtil()
                        minioutil.connect()
                        await run_in_threadpool(
                            minioutil.upload_file, MinioConfig.BUCKET_NAME, pdf_remote_path, pdf_path
                        )
                        print(f"path:{pdf_remote_path}")
                        docs_service = layout_multimode_Parse()
                        all_text_list, metadata_list, images_list = await docs_service.extract_content_from_pdf(file_path=pdf_path, knowledge_id=id, request=request)
                        if os.path.exists(pdf_path):
                            os.remove(pdf_path)
                    # if local_paths[i].endswith((".doc", ".docx")):
                    #     # 获取文件所在目录
                    #     output_dir = os.path.dirname(local_paths[i]) + '/transformer'
                    #     # 执行 soffice 命令进行转换
                    #     subprocess.run([
                    #         "soffice",
                    #         "--headless",
                    #         "--convert-to", "pdf",
                    #         "--outdir", output_dir,
                    #         local_paths[i]
                    #     ], check=True)
                    #     file_name = os.path.basename(local_paths[i])
                    #     # 获取不带扩展名的文件名
                    #     file_name_without_ext = os.path.splitext(file_name)[0]
                    #     # 构造正确的 pdf 路径
                    #     pdf_path = os.path.join(output_dir, file_name_without_ext + ".pdf").replace("\\", "/")
                    #     # pdf_path = os.path.splitext(local_paths[i])[0] + ".pdf"
                    #     docs_service = layout_multimode_Parse()
                    #     all_text_list, metadata_list, images_list = await docs_service.extract_content_from_pdf(pdf_path, knowledge_id=id,request=request)
                    #     if os.path.exists(pdf_path):
                    #         os.remove(pdf_path)
                    # 新解析方法
                    else:
                        docs_service = layout_multimode_Parse()
                        all_text_list, metadata_list, images_list = await docs_service.extract_content_from_pdf(local_paths[i], knowledge_id= id,request=request)
                        print(f"{all_text_list}.{metadata_list}.{images_list}")
                    docs = ""
                    for text in all_text_list:
                        docs += text
                if not chunk_method:
                    model_path = Path(__file__).parents[3] / "nlp_bert_document-segmentation_chinese-base"
                    p = pipeline(
                        task=Tasks.document_segmentation,
                        model=str(model_path),
                        device='cpu')
                    docs = docs[0].page_content if type(docs) == list else docs
                    doc_result = p(documents=docs)
                    tmp = re.split(r'\n\t', re.sub(r'^\t', '', doc_result['text']))
                    text = list(filter(None, tmp))
                    chunk_list.extend(text)
                    LogUtil.info(f"智能切块内容：{chunk_list}")
                if is_generate:
                    result = await Knowledge_File_service.add_embedding_document(
                        id, docs, file_name, chunk_method, chunk_size, chunk_overlap, separator, file_id, chunk_list, all_text_list, metadata_list, images_list
                    )
                    if result:
                        if pdf_remote_path != "":
                            MongodbUtil.update_docs_by_condition("upload_file_info",
                                                                 {'knowledge_id': id, 'file_name': file_name},
                                                                 replace_data={"$set": {"status": 0, "remote_path": f"{id}/{file_name}","pdf_path":pdf_remote_path}})
                        else:
                            MongodbUtil.update_docs_by_condition("upload_file_info",
                                                                 {'knowledge_id': id, 'file_name': file_name},
                                                                 replace_data={"$set": {"status": 0,
                                                                                        "remote_path": f"{id}/{file_name}"}})
                        MinIoUtil.copy_object("tiance-base-temp-file-bucket", remote_paths[i], "tiance-base", f"{id}/{file_name}")
                    else:
                        MongodbUtil.update_docs_by_condition("upload_file_info",
                                                             {'knowledge_id': id, 'file_name': file_name, "status": 1},
                                                             replace_data={"$set": {"status": 2}})
                else:
                    result = await Knowledge_File_service.add_embedding_document_without_question(
                        id, docs, file_name, chunk_method, chunk_size, chunk_overlap, separator, file_id, chunk_list,all_text_list, metadata_list, images_list
                    )
                    if result:
                        if pdf_remote_path != "":
                            MongodbUtil.update_docs_by_condition("upload_file_info",
                                                                 {'knowledge_id': id, 'file_name': file_name},
                                                                 replace_data={"$set": {"status": 0,
                                                                                        "remote_path": f"{id}/{file_name}",
                                                                                        "pdf_path": pdf_remote_path}})
                        else:
                            MongodbUtil.update_docs_by_condition("upload_file_info",
                                                                 {'knowledge_id': id, 'file_name': file_name},
                                                                 replace_data={"$set": {"status": 0,
                                                                                        "remote_path": f"{id}/{file_name}"}})
                        MinIoUtil.copy_object("tiance-base-temp-file-bucket", remote_paths[i], "tiance-base", f"{id}/{file_name}")
                    else:
                        MongodbUtil.update_docs_by_condition("upload_file_info",
                                                             {'knowledge_id': id, 'file_name': file_name, "status": 1},
                                                             replace_data={"$set": {"status": 2}})
            except Exception as e:
                LogUtil.info(f"出错了<{traceback.format_exc()}>")
                MongodbUtil.update_docs_by_condition("upload_file_info",
                                                     {'knowledge_id': id, 'file_name': file_name, "status": 1},
                                                     replace_data={"$set": {"status": 2, "info": str(e)}})

    except Exception as e:
        LogUtil.error(msg=f"处理文件上传时出错: {traceback.format_exc()}")
        return RetUtil.response_error(message="文件上传出错，请重试")

    finally:
        if local_paths:
            for local_path in local_paths:
                if os.path.exists(local_path):
                    os.remove(local_path)

@router.post(
    "/chunk_result_query",
    summary="查询文档切片结果",
)
async def chunk_result_query(
    params: ChunkQueryInfo
) -> Response:
    try:
        result, len_result = await Knowledge_File_service.chunk_result_query_v2(
            params.id, params.file_id, params.page, params.page_size
        )

        return RetUtil.response_ok({"total": len_result, "result": result})
    except Exception as e:
        detail = f"查询指定知识库中的文件失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/file_delete", summary="从指定知识库中删除文件")
async def file_delete(
    file_name: str = Body(..., examples=["aaa"], description="文件名称"),
    id: str = Body(..., description="知识库id", embed=True),
    file_id: str = Body(..., examples=["aaa"], description="文件id")
) -> Response:
    try:
        LogUtil.log_json(describe="->从指定知识库中删除文件", kwargs=jsonable_encoder({"file_id": file_id, "id": id, "file_name": file_name}))
        result = await Knowledge_File_service.delete_file(file_id=file_id, knowledge_id=id, file_name=file_name)
        LogUtil.info(f"从指定知识库中删除文件{result}")
        await asyncio.sleep(1)
        if result:
            return RetUtil.response_ok("文件删除成功")
        else:
            return RetUtil.response_error(message="文件删除失败")
    except Exception as e:
        detail = f"文件删除失败：{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/file_query_page", summary="查询指定知识库中的文件(分页)")
async def file_query_page(
    params: FileQueryInfo,
) -> Response:
    try:
        result, len_result = await Knowledge_File_service.file_query_page(
            params.id, params.file_name, page=params.page, page_size=params.page_size
        )
        # def parse_upload_time(upload_time_str):
        #     return datetime.strptime(upload_time_str, "%Y-%m-%d %H:%M:%S")
        # result = sorted(result,key=lambda x: parse_upload_time(x["upload_time"]),reverse=True)
        # LogUtil.info(str(type(result)))
        # result = PageUtil.paginate_list(result, params.page, params.page_size)

        return RetUtil.response_ok({"total": len_result, "result": result})

    except Exception as e:
        detail = f"查询指定知识库中的文件失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=str(e))

@router.post("/file_query_all", summary="查询指定知识库中的所有文件")
async def knowledge_query(
        id: str = Body(..., description="知识库id", embed=True),
        file_name: str = Body('', embed=True, examples=["test_00001"], description="文件名称"),
) -> Response:
    try:
        LogUtil.log_json(describe="->查询指定知识库中的所有文件", kwargs=jsonable_encoder({"file_name": file_name, "id": id}))
        result = await Knowledge_File_service.file_query_all(
            id, file_name
        )
        LogUtil.info(str(result))
        def parse_upload_time(upload_time_str):
            return datetime.strptime(upload_time_str, "%Y-%m-%d %H:%M:%S")
        result = sorted(result, key=lambda x: parse_upload_time(x["upload_time"]), reverse=True)
        LogUtil.info(str(type(result)))
        return RetUtil.response_ok(result)

    except Exception as e:
        detail = f"查询指定知识库中的文件失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/update_chunk", summary="保存切片修改")
async def update_chunk(
    index: int = Body(..., embed=True, description="切片下标"),
    new_content: str = Body(..., embed=True, description="新内容"),
    new_question: str = Body(..., embed=True, description="新内容"),
    id: str = Body(..., description="知识库id", embed=True),
) -> Response:
    try:
        LogUtil.log_json(describe="->保存切片修改", kwargs=jsonable_encoder({"index": index, "new_content": new_content, "new_question": new_question, "id": id}))
        result = await Knowledge_File_service.update_chunk(index, new_content, new_question, id)
        if result:
            return RetUtil.response_ok("切片修改成功")
        else:
            return RetUtil.response_ok("切片修改失败")

    except Exception as e:
        detail = f"查询切片方式失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/upload_archive", summary="上传压缩包")
async def upload_archive(
    id: str = Body(..., description="知识库id", embed=True),
    file_obj: UploadFile = File(..., description="压缩包文件"),
) -> Response:
    """
    上传压缩包并解压文件
    :param file_obj: 压缩包文件对象
    :param kb_name: 知识库名称
    :return: 上传结果。
    """
    try:
        results = []
        result = await Knowledge_File_service.upload_and_extract_archive(file_obj, id)
        if len(result["files"]) != 1:
            result["files"] = result["files"][1:]
        for file in result["files"]:
            file_name = os.path.basename(file)
            # 上传文件到MinIO临时桶:tiance-base-temp-file-bucket
            date = datetime.now().strftime('%Y_%m_%d')  # 日期
            folder = generate_unique_id('Temp', datacenter_id=1, worker_id=1)  # 随机id
            remote_path = f"{date}/{folder}/{file_name}"  # 文件路径，以{kb_name}$$${file_name}命名文件名称
            bucket_name = "tiance-base-temp-file-bucket"  # 桶名称
            await run_in_threadpool(
                    MinIoUtil.upload_file, bucket_name, remote_path, file)
            results.append({"file_name": file_name, "remote_path": remote_path})
        return RetUtil.response_ok(results)

    except Exception as e:
        detail = f"上传压缩包失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

    finally:
        if result:
            for file in result["files"]:
                if os.path.exists(file):
                    os.remove(file)

@router.post("/file_detect", summary="重名/相似文件检测")
async def file_detect(
        id: str = Body(..., examples=["test_upload"], description="知识库id"),
        file_name_list: list = Body(..., examples=[["实验室考勤规则.pdf"]], description="文件名称列表"),
) -> Response:
    try:
        # 获取重名文件列表
        repeat_file_list = await Knowledge_File_service.repeat_file_detect(id, file_name_list)
        if repeat_file_list == False:
            return RetUtil.response_error("获取重名文件失败")

        # 获取相似文件列表
        similar_file_list = await Knowledge_File_service.similar_file_detect(id, file_name_list, repeat_file_list)
        if similar_file_list == False:
            return RetUtil.response_error("获取相似文件失败")

        return RetUtil.response_ok({"repeat_file_list": repeat_file_list, "similar_file_list": similar_file_list})

    except Exception as e:
        detail = f"重名/相似文件检测失败：失败原因<{traceback.format_exc()}>"
        LogUtil.error(msg=detail)
        return RetUtil.response_error(detail)

@router.post("/knowledge_list", summary="知识库列表")
async def knowledge_list(
        chat_request: Request,
        team_codes: Optional[list] = Body([], description="团队id", embed=True)
) -> Response:
    try:
        account_id = chat_request.state.account_id
        LogUtil.log_json(describe="->知识库列表")
        result = await AgentService.list_knowledge(account_id, team_codes)
        LogUtil.info(str(result))
        return RetUtil.response_ok(result)
    except Exception as e:
        LogUtil.error(f"返回知识库列表异常: {str(traceback.format_exc())}")
        return Response(content=f"返回知识库列表异常: {str(traceback.format_exc())}", media_type="text/plain")

@router.post("/get_knowledge_detail", summary="知识库列表")
async def get_knowledge_detail(
        id: str = Body(..., examples=["test_upload"], embed=True),
) -> Response:
    try:
        results = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.KB_COLLECTION, doc_id=ObjectId(id))
        if not results:
            return RetUtil.response_error(message="知识库不存在")
        for result in [results]:
            result["_id"] = str(result["_id"])
        return RetUtil.response_ok(result)
    except Exception as e:
        LogUtil.error(f"返回知识库詳情: {str(traceback.format_exc())}")
        return Response(content=f"返回知识库詳情异常: {str(traceback.format_exc())}", media_type="text/plain")