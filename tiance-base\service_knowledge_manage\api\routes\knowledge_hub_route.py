#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :knowledge_hub_route.py
@Description  :
<AUTHOR>
@Date         :2024/09/04 09:53:32
"""

from typing import Any, Dict, List
import traceback

from bson import ObjectId
from fastapi import APIRouter, HTTPException
from fastapi.responses import Response

from base_configs.minio_config import MinioConfig
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from base_utils.milvus_util import MilvusUtil
from base_utils.mongodb_util import MongodbUtil
from base_utils.embedding_util import EmbeddingUtil
from base_utils.rerank_util import RerankUtil
from base_configs.model_config import ModelConfig
from base_configs.mongodb_config import CollectionConfig
from service_knowledge_manage.entity.knowledge_hub_entity import (
    KnowledgeRetrivalInfo,
    KnowledgeEntity,
    KnowledgeRecallEntity,
    KnowledgeRetrivalResponse,
    KnowledgeDialogInfo,
)
from service_knowledge_manage.service.knowledge_service import KnowledgeService
from service_workflow_manage.service.workflow_service import WorkflowService, WorkflowNodeService
from base_configs.milvus_config import MilvusConfig
router = APIRouter()


@router.post(
    "/knwolege_retrieval",
    summary="知识库检索",
    response_model=KnowledgeRetrivalResponse,
)
async def knwolege_retrieval(

        params: KnowledgeRetrivalInfo) -> Response:

    LogUtil.info(f"知识库检索--：{params}")
    try:
        if params.user_query=="":
            data = KnowledgeRetrivalResponse(
                user_query=params.user_query,
                is_rerank=True if params.rerank_model else False,
                results=[],
            )
            print("用户输入空值")
            return RetUtil.response_ok(data=data.model_dump())
        # 连接MongoDB数据库
        MongodbUtil.connect()
        # LogUtil.info(f"参数为：{params.kb_name}")
        # 根据知识库的名称获取到embedding模型名称
        collection_name = '_' + str(params.id)
        query_result = MongodbUtil.query_docs_by_condition(
            collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id": ObjectId(params.id)}
        )
        embedding_model = ModelConfig.DEFAULT_EMBEDDING_MODEL
        for item in query_result:
            if item:
                embedding_model = item["embedding_model"]
                embedding_id = item["embedding_id"]
                rerank_id = item["rerank_id"]
            else:
                LogUtil.info("知识库不存在！！")
                raise ValueError("该知识库不存在")
        if params.rerank_id:
            rerank_id = params.rerank_id
        # 获取向量embedding工具
        embeddingUtil = EmbeddingUtil(embedding_id=embedding_id)
        LogUtil.info("embedding初始化成功")
        LogUtil.info(f"embedding_model:{embedding_model}")

        # 多路检索
        import jieba.analyse  # 关键词提取库
        embeddings = embeddingUtil.get_embedding(
            model_uid=embedding_model, input=params.user_query
        )
        milvus_util = MilvusUtil()
        docs_semantic = await milvus_util.search_by_vector(
            collection_name=collection_name,
            vector=embeddings,
            limit=params.recall_num
        )
        docs_semantic = docs_semantic[0]
        print("docs_semantic",docs_semantic)


        try:
            keywords = jieba.analyse.extract_tags(params.user_query, topK=3, withWeight=False)
            docs_keyword = []

            # keywords为空？

            if keywords:
                # 构建关键词查询条件
                query_filter = " or ".join([f"content like '%{kw}%'" for kw in keywords])
                print("query_filter",query_filter)
                res = await milvus_util.search_by_vector(
                        collection_name=collection_name,
                        vector=embeddings,  # 查询向量
                        limit=params.recall_num,  # 返回结果数量
                        filter = query_filter,
                    )

            else:
                keywords = jieba.analyse.textrank(params.user_query, topK=3, withWeight=False)
                query_filter = " or ".join([f"content like '%{kw}%'" for kw in keywords])
                print("query_filter", query_filter)
                res = await milvus_util.search_by_vector(
                        collection_name=collection_name,
                        vector=embeddings,  # 查询向量
                        limit=params.recall_num,  # 返回结果数量
                        filter=query_filter,
                    )
        except:
            docs=docs_semantic

        result_dict = {}

        try:
            print("res",res)
            if len(res):
                res=res[0]
                # 遍历第一个列表
                for item in res:
                    result_dict[item['id']] = item
        except:
            docs = docs_semantic

        # 遍历第二个列表
        for item in docs_semantic:
            if item['id'] in result_dict:
                # 如果id已存在，比较distance，保留距离更小的记录
                if item['distance'] < result_dict[item['id']]['distance']:
                    result_dict[item['id']] = item
            else:
                # 如果id不存在，直接添加到字典中
                result_dict[item['id']] = item


        # 将字典转换为列表
        result_list = list(result_dict.values())
        print("result_list",result_list)
        docs=result_list
        print("docsaaaaa", docs)
        data = sorted(docs, key=lambda x: x['distance'])[:params.recall_num]
        docs=data
        if len(docs) == 0:
            data = KnowledgeRetrivalResponse(
                user_query=params.user_query,
                is_rerank=True if params.rerank_model else False,
                results=[],
            )
            return RetUtil.response_ok(data=data.model_dump())

        # 解析输出
        chunks = []
        for index, item in enumerate(docs, start=1):
            entity = item.get("entity", {})
            if "source_data" in entity:
                source_data = entity["source_data"]
            else:
                source_data = []

            filename=item["entity"]["file_name"]
            file_url = f"{params.id}/{filename}"

            print("999999999999",item["entity"]["source_data"][0].get("location", []) if item["entity"].get("source_data") else [])
            print(type(item["entity"]["source_data"][0].get("location", []) if item["entity"].get("source_data") else []))
            abandon_area1=[]
            if item["entity"].get("source_data"):

                for itemccc in  item["entity"]["source_data"]:
                    abandon_area1.append(itemccc.get("abandon_area", []))
            else:
                abandon_area1=[]
            print("abandon_area",abandon_area1)

            chunk = KnowledgeEntity(
                recall_score=round(item["distance"], 2),
                recall_index=index,
                chunk_content=item["entity"]["content"],
                file_name=item["entity"]["file_name"],
                rerank_score=0,
                rerank_index=0,
                number=item["entity"]["number"],
                images_urls = item["entity"]["source_data"][0].get("images_urls", [""])[0] if item["entity"].get("source_data") else "",
                location=item["entity"]["source_data"][0].get("location", []) if item["entity"].get("source_data") else [],
                file_urls=file_url,
                page= [item['page'] for item in source_data] if source_data else [],
                file_id=item['entity']['file_id'],
                abandon_area= abandon_area1,
            )
            chunks.append(chunk)
        print("aaaaaaaaaa")
        print("chunks6666",chunks)
        def process_rerank_results(
            docs: List[Dict[str, Any]], rerank_results: List[Dict[str, Any]]
        ) -> List[Dict[str, Any]]:
            """
            处理重排结果并将分数和顺序添加到原始数据结构中。

            参数:
            - original_data: List[Dict[str, Any]] 原始数据列表
            - rerank_results: List[Dict[str, Any]] 重排函数返回的结果列表

            返回:
            - List[Dict[str, Any]] 更新后的数据列表
            """

            # 创建一个映射，用于快速查找原始数据
            content_to_data = [{item.chunk_content: item} for item in docs ]

            # 按相关性分数排序重排结果
            sorted_results = sorted(
                rerank_results, key=lambda x: x["relevance_score"], reverse=True
            )
            # for item in sorted_results:
            #     pprint(item)

            # 处理重排结果
            docs_only_contain_rerank = []
            for rank, result in enumerate(sorted_results, start=1):
                is_pop = False
                content = result["document"]["text"]
                for item in content_to_data:
                    if content in item:
                        item[content].rerank_score = result["relevance_score"]
                        item[content].rerank_index = rank
                        # item[content].number = result["number"]
                        # item[content].file_name = result["file_name"]
                        docs_only_contain_rerank.append(item[content])
                        is_pop = True
                        delete_item = item
                        break
                if is_pop:
                    content_to_data.remove(delete_item)

            # 返回更新后的数据列表
            # return list(content_to_data.values())
            return docs_only_contain_rerank

        if params.rerank_model != "":
            documents = [item.chunk_content for item in chunks]
            query = params.user_query
            top_n = params.rerank_num
            model_uid = params.rerank_model
            score = params.score
            rerank_results = RerankUtil(rerank_id=rerank_id).socre_rerank(
                model_uid=model_uid,
                documents=documents,
                query=query,
                top_n=top_n,
                return_documents=True,
                threshold=score
            )
            # LogUtil.info(f"Rerank_result:{str(rerank_results)}")
            """
            rerank_results的返回样式
            [
                {'document': {'text': '中国的主要节日包括春节、清明节、端午节和中秋节。春节是中国最重要的传统节日，家家户户都会庆祝。'},
                'index': 0,
                'relevance_score': 0.9988873600959778},
                
                {'document': {'text': '春节通常在农历正月初一庆祝，是中国人团聚和庆祝新年的重要时刻。人们会吃饺子、放鞭炮，并进行各种传统活动。'},
                'index': 1,
                'relevance_score': 0.8788966536521912},
                
                {'document': {'text': '端午节是为了纪念屈原的节日，人们通常在这一天吃粽子、赛龙舟。'},
                'index': 4,
                'relevance_score': 0.7846590876579285}
            ]
            """
            docs_only_contain_rerank = process_rerank_results(chunks, rerank_results)
            """
            docs_only_contain_rerank的返回样式：
            [   {'distance': 0.5920194387435913,
                'entity': {'content': 'adfhgfdkgjfigsnfglij',
                            'file_name': '《中国银保监会关于印发实施车险综合改革指导意见的通知》.pdf',
                            'file_time': '2024-09-21 01:45:16',
                            'number': 8},
                'id': 452450913582844817,
                'rerank_order': 1,
                'rerank_score': 0.9909056425094604},
                {'distance': 0.6637850999832153,
                'entity': {'content': 'dsjklagdbfgjdlkfgjidsfnsdkgids',
                            'file_name': '《中国银保监会关于印发实施车险综合改革指导意见的通知》.pdf',
                            'file_time': '2024-09-21 01:45:16',
                            'number': 9},
                'id': 452450913582844818,
                'rerank_order': 2,
                'rerank_score': 0.982548177242279},
                
                {'distance': 0.7208224534988403,
                'entity': {'content': 'dsaklghdsjfidjgdskljk',
                            'file_time': '2024-09-21 01:45:16',
                            'number': 10},
                'id': 452450913582844819,
                'rerank_order': 3,
                'rerank_score': 0.919082760810852}]
            """
            print(f"docs_only_contain_rerank:{docs_only_contain_rerank}")
            import math
            rerank_chunks = []
            for item in docs_only_contain_rerank:
                if params.enhance_rounds != 0:
                    chunk_index = item.number
                    chunk_file = item.file_name
                    lower_bound = max(0, chunk_index - params.enhance_rounds)
                    upper_bound = chunk_index + params.enhance_rounds
                    expr = f'number >= {lower_bound} and number <= {upper_bound} and file_name == "{chunk_file}"'
                    result = await milvus_util.query_by_scalar(collection_name=collection_name,
                                                               query_conditions=expr, output_fields=["content","source_data","number"])
                    chunks = []
                    pages = []
                    abandons = []
                    result = sorted(result, key=lambda x: x['number'])
                    for chunk in result:
                        if chunk.get("source_data"):
                            for data in chunk["source_data"]:
                                if data["page"] not in pages:
                                    abandons.append(data.get("abandon_area"))
                                    pages.append(data.get("page"))
                        chunks.append(chunk["content"])
                    enhance_chunk = await KnowledgeService.merge_chunks_auto(chunks)
                    item.chunk_content = enhance_chunk
                    item.page = pages
                    item.abandon_area = abandons
                rerank_chunk = KnowledgeEntity(
                    recall_score=round(item.recall_score, 2),
                    recall_index=item.recall_index,
                    chunk_content=item.chunk_content,
                    file_name=item.file_name,
                    rerank_score=math.trunc(item.rerank_score * 100) / 100.0,
                    rerank_index=item.rerank_index,
                    number=item.number,
                    images_urls=item.images_urls,
                    location=item.location,
                    file_urls=item.file_urls,
                    page=item.page,
                    file_id=item.file_id,
                    abandon_area=item.abandon_area
                )
                rerank_chunks.append(rerank_chunk)

        if params.rerank_model != "":
            results = rerank_chunks
            data = KnowledgeRetrivalResponse(
                user_query=params.user_query,
                is_rerank=True if params.rerank_model else False,
                results=results,
            )
        else:

            recall_datas = []
            for item in chunks:
                if params.enhance_rounds != 0:
                    chunk_index = item.number
                    chunk_file = item.file_name
                    lower_bound = max(0, chunk_index - params.enhance_rounds)
                    upper_bound = chunk_index + params.enhance_rounds
                    expr = f'number >= {lower_bound} and number <= {upper_bound} and file_name == "{chunk_file}"'
                    result = await milvus_util.query_by_scalar(collection_name=collection_name,
                                                               query_conditions=expr, output_fields=["content","source_data","number"])
                    chunks = []
                    pages = []
                    abandons = []
                    result = sorted(result, key=lambda x: x['number'])
                    for chunk in result:
                        if chunk.get("source_data"):
                            for data in chunk["source_data"]:
                                if data["page"] not in pages:
                                    abandons.append(data.get("abandon_area"))
                                    pages.append(data.get("page"))
                        chunks.append(chunk["content"])
                    enhance_chunk = await KnowledgeService.merge_chunks_auto(chunks)
                    item.chunk_content = enhance_chunk
                recall_data = KnowledgeRecallEntity(
                    chunk_content=item.chunk_content,
                    file_name=item.file_name,
                    number=item.number,
                    recall_score=round(item.recall_score, 2),
                    recall_index=item.recall_index,
                    images_urls=item.images_urls,
                    location=item.location,
                    file_urls=item.file_urls,
                    page=item.page,
                    file_id=item.file_id,
                    abandon_area=item.abandon_area
                )
                recall_datas.append(recall_data)
            results = recall_datas


            data = KnowledgeRetrivalResponse(
                user_query=params.user_query,
                is_rerank=True if params.rerank_model else False,
                results=results,
            )
        LogUtil.info(f"检索结果{data}")
        return RetUtil.response_ok(data=data.model_dump())
    except Exception as e:
        detail = f"知识库检索失败：{str(traceback.format_exc())}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise


@router.post("/knwolege_dialog", summary="知识库对话")
async def knwolege_dialog(params: KnowledgeDialogInfo) -> Response:
    try:
        import json

        # 调用知识库检索函数
        # retrival_params = {
        #     "kb_name": "tiance_test",
        #     "chunk_num": 10,
        #     "is_rerank": False,
        #     "rerank_model": "",
        #     "recall_num": 10,
        #     "user_query": "第二十三条 网络关键设备",
        # }
        # LogUtil.info(params.retrival_params.kb_name)
        # s=params.retrival_params
        # LogUtil.info(s.kb_name)
        retrival_params = params.retrival_params
        retrival_result = await knwolege_retrieval(retrival_params)
        retrival_result = json.loads(retrival_result.body)
        # 解析知识库检索内容
        chunk_results = retrival_result.get("data").get("results")
        chunk_content = "\n\n".join(
            [item.get("chunk_content") for item in chunk_results]
        )

        messages = [{"role": "system", "content": "请根据知识库信息回答问题"}]

        # 组织提示词
        RAG_CONTEXT_PROMPT = """"
        请根据知识库信息回答问题
        ## 知识库信息
        {chunk_content}
        
        问题：{question}
        """

        # 知识库信息
        content = RAG_CONTEXT_PROMPT.format(
            chunk_content=chunk_content, question=retrival_params.user_query
        )
        # 用户问题
        messages.append({"role": "user", "content": content})

        async def astream_response():
            try:
                from service_model_manage.service.chat_completion_service import (
                    OpenAILLMService,
                )

                # 业务逻辑处理
                openAILLMService = OpenAILLMService()

                response_content = ""
                completion_stream = (
                    openAILLMService.async_llm_model_client.chat.completions.create(
                        model=params.model_uid,
                        messages=messages,
                        temperature=params.temperature,
                        max_tokens=params.max_token_length,
                        stream=True,
                    )
                )
                async for chunk in await completion_stream:
                    if chunk.choices[0].delta.content != "":
                        token = chunk.choices[0].delta.content
                        yield f"{json.dumps(RetUtil.response_stream(data=token), ensure_ascii=False)}"
                        response_content += token
            except Exception as e:
                print("llm推理服务失败,请重试")
                LogUtil.error("异常:{0}".format(e))
                yield f"data: {RetUtil.response_error(message='llm推理服务失败,请重试')}\n\n"

        from sse_starlette.sse import EventSourceResponse

        return EventSourceResponse(astream_response())
    except Exception as e:
        detail = f"知识库对话失败：{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)


if __name__ == '__main__':
    import json

    LogUtil.init('test')
    k = KnowledgeRetrivalInfo(kb_name="test01_A1856602952383467520", user_query="第二十三条 网络关键设备", is_rerank=True,
                              rerank_model="bge-reranker-large", recall_num=10, rerank_num=10)
    retrival_result = knwolege_retrieval(k)
    print(json.loads(retrival_result.body))
