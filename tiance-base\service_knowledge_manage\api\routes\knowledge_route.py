#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :knowledge_route.py
@Description  :
<AUTHOR>
@Date         :2024/09/03 15:10:28
"""
import traceback
from service_permission_auth.service.usr_auth_service import UsrAuthService
from fastapi import APIRouter, HTTPException, Body, Depends, Header, Request, File, Query
from service_model_manage.service.chat_db_service import ChatConversationService
from service_app_getway.service.app_getway import AppGateWayService
import json
from fastapi import APIRouter, HTTPException, Body, Depends, Header
from fastapi.responses import Response
from fastapi.encoders import jsonable_encoder
from base_configs.mongodb_config import CollectionConfig
from bson import ObjectId
from base_utils.log_util import LogUtil
from base_utils.ret_util import RetUtil
from service_knowledge_manage.entity.knowledge_entity import KnowledgeInfo
from service_workflow_manage.service.workflow_service import WorkflowService, WorkflowNodeService
from base_utils.mongodb_util import <PERSON>godbUtil
from base_utils.milvus_util import MilvusUtil
from service_knowledge_manage.service.knowledge_service import KnowledgeService
from base_utils.minio_util import MinIoUtil
from typing import Optional
from sqlalchemy.orm import Session
from base_utils.mysql_util import SessionLocal
from service_usr_manage.model.usr_model import Usr_Model
from service_model_manage.service.model_family_service import ModelFamilyService
import datetime
import re
from service_usr_manage.service.usr_service import UsrService
from base_utils.mysql_util import query2dict_acc
from sqlalchemy.orm import Session
from service_knowledge_manage.service.knowledge_file_service import (
    Knowledge_File_service)
import fitz
import requests

from base_configs.minio_config import MinioConfig
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

router = APIRouter()

@router.post("/chunk_highlight_pdf", summary="在源pdf中找到切片所在位置并高亮")
async def chunks_highlight(
        file_path: str = Body(..., description="pdf文件在minio桶的路径"),
        kb_id: str = Body(..., description="知识库id"),
        chunk: str = Body(..., description="切片字符串"),
        page: list = Body(..., description="切片所在页码（1开头）"),
        abandon_area: dict = Body(..., description="页眉页脚等区域")
):
    try:
        LogUtil.info("高亮pdf中切片所在位置")
        result = await KnowledgeService.chunk_highlight(file_path=file_path, kb_id=kb_id, chunk=chunk,
                                                         page_list=page, abandon_area=abandon_area)
        return RetUtil.response_ok(result)

    except fitz.FileDataError as e:
        raise HTTPException(status_code=400, detail=str(e))

    except Exception as e:
        LogUtil.error(f"切片溯源失败：{str(traceback.format_exc())}")
        return RetUtil.response_error(message="切片溯源失败")

@router.post(
    "/get_all_knowledge",
    summary="获取所有知识库，不分页返回",
    response_model=KnowledgeInfo,
)
async def aget_all_knowledge() -> Response:
    try:
        LogUtil.log_json(
            describe="->获取所有知识库，不分页返回",
            kwargs=jsonable_encoder({}),
        )

        # 头尾下标
        kb_list, kb_len = await KnowledgeService.get_all_kb()
        return RetUtil.response_ok(data=kb_list)

    except Exception as e:
        LogUtil.error("Server Internal Error: {0}".format(str(e)))
        return RetUtil.response_error(message="Server Internal Error")

@router.post(
    "/get_all_emb_model_info",
    summary="获取所有嵌入模型及其维度信息",
)
async def get_all_emb_model_info() -> Response:
    try:
        LogUtil.log_json(
            describe="->获取所有嵌入模型及其维度信息",
            kwargs=jsonable_encoder({}),
        )

        # 头尾下标
        emb_model_list = []
        emb_model = await WorkflowNodeService.running_embedding_model()
        for model in emb_model:
            result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_RUN_COLLECTION,doc_id=ObjectId(model["id"]))
            if result["is_external"] == False:
                result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_FAMILY_COLLECTION,doc_id=result["model_id"])
                emb_model_list.append({"embedding_model": model, "dimension": result["model_emb_details"]["model_embedding_dimension"]})
            else:
                emb_model_list.append({"embedding_model": model, "dimension": result["max_tokens"]})
        LogUtil.info(str(emb_model_list))
        return RetUtil.response_ok(data=emb_model_list)

    except Exception as e:
        LogUtil.error("Server Internal Error: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="Server Internal Error")

@router.post(
    "/get_all_rerank_model_info",
    summary="获取所有嵌入模型及其维度信息",
)
async def get_all_rerank_model_info() -> Response:
    try:
        LogUtil.log_json(
            describe="->获取所有重排模型及其维度信息",
            kwargs=jsonable_encoder({}),
        )

        results = []
        result = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.MODEL_RUN_COLLECTION,
                                                     search_condition={})
        for item in result:
            if item["status"] == "running" and item["model_type"] == "rerank" and item["is_delete"] == False:
                if item["is_external"] == False:
                    data = {"model_name": item["model_uid"], "id": str(item["_id"]), "model_uid": item["model_uid"],
                            "is_external": item["is_external"]}
                else:
                    data = {"model_name": item["model_name"], "id": str(item["_id"]), "model_uid": item["model_uid"],
                            "is_external": item["is_external"]}
                results.append(data)
        return RetUtil.response_ok(data=results)

    except Exception as e:
        LogUtil.error("Server Internal Error: {0}".format(str(traceback.format_exc())))
        return RetUtil.response_error(message="Server Internal Error")

@router.post("/knowledge_query_test1", summary="获取文件列表", response_model=KnowledgeInfo)
async def knowledge_query(
) -> Response:
    try:
        object_list = MinIoUtil.get_file_list("tiance-base", "pytest/")
        return RetUtil.response_ok(data=object_list)

    except Exception as e:
        LogUtil.error("Server Internal Error: {0}".format(str(e)))
        return RetUtil.response_error(message="Server Internal Error")
    
@router.post("/prompt_store", summary="提示词入库")
async def knowledge_query(
    id: str = Body(..., embed=True, examples=[""], description="id"),
    prompt: str = Body(..., embed=True, examples=[""], description="提示词"),
) -> Response:
    try:
        MongodbUtil.update_docs_by_condition(collection_name=CollectionConfig.KB_COLLECTION, search_condition={"_id": ObjectId(id)}, replace_data={"$set": {"prompt": prompt}})
        return RetUtil.response_ok(data="提示词入库成功")
    except Exception as e:
        LogUtil.error("Server Internal Error: {0}".format(str(e)))
        return RetUtil.response_error(message=str(e))
    
@router.post("/retrieval_setting_update", summary="修改切片检索")
async def knowledge_update(
    id: str = Body(..., description="知识库id", embed=True),
    prompt: str = Body("", description="知识库名称", embed=True),
    rerank_model: str = Body("", description="重排模型", embed=True),
    retrieval_count: int = Body(10, description="检索返回文本数量", embed=True),
    score: float = Body(0.5, description="召回阈值", embed=True),
    top_k: int = Body(5, description="召回数量", embed=True),
    rerank_id: str = Body(..., description="重排模型的id", embed=True),
    enhance_rounds: int = Body(...,description="检索增强轮数",embed=True)
) -> Response:
    try:
        await KnowledgeService.slice_retrieval_update(id, rerank_model, retrieval_count, score, top_k,rerank_id,enhance_rounds)
        changing_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return RetUtil.response_ok(data={"changing_time":changing_time})

    except Exception as e:
        LogUtil.error("Server Internal Error: {0}".format(str(e)))
        return RetUtil.response_error(message="Server Internal Error")

@router.post("/call_create_knowledge", summary="创建知识库")
async def call_create_knowledge(
        chat_request: Request,
        kb_name: str = Body(..., embed=True, examples=["test_py"], description="知识库名称"),
        description: Optional[str] = Body("", description="描述"),
        embedding_id: str = Body(..., description="嵌入模型id"),
) -> Response:
    try:
        try:
            account_id = chat_request.state.account_id
        except:
            return RetUtil.response_error(message="app-id无效")
        if kb_name == "":
            return RetUtil.response_error(message="知识库名称不能为空")
        if embedding_id == "":
            return RetUtil.response_error(message="嵌入模型id不能为空")

        result = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION, {"kb_name": kb_name, "account_id": account_id})
        if len(list(result)) > 0:
            return RetUtil.response_error(message="知识库名称已经存在")
        if len(description) > 100:
            return RetUtil.response_error(message="描述长度不能超过一百")
        if not re.match(r"^[a-fA-F0-9]{24}$", embedding_id):
            return RetUtil.response_error(message="嵌入模型id不符合规范")
        model_result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_RUN_COLLECTION, doc_id=ObjectId(embedding_id))
        if len(list(model_result)) == 0:
            return RetUtil.response_error(message="嵌入模型id不存在")
        model = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_RUN_COLLECTION, doc_id=ObjectId(embedding_id))
        if model["is_external"] == False:
            result = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.MODEL_FAMILY_COLLECTION, doc_id=model["model_id"])
            embedding_model = model["model_uid"]
            embedding_dimension = result["model_emb_details"]["model_embedding_dimension"]
        else:
            embedding_model = model["model_uid"]
            embedding_dimension = model["max_tokens"]
        result = await knowledge_create(KnowledgeInfo(kb_name=kb_name, description=description, embedding_model=embedding_model, embedding_dimension=embedding_dimension, embedding_id=embedding_id, rerank_id="", team_code=""), chat_request)
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)

        if result["status"]:
            return RetUtil.response_ok("知识库创建成功")

        else:
            return RetUtil.response_error(message=str(result["message"]))

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)

@router.post("/call_delete_knowledge", summary="删除知识库")
async def call_delete_knowledge(
        chat_request: Request,
        db: Session = Depends(get_db),
        id: str = Body(..., description="知识库id", embed=True),
) -> Response:
    try:
        if id == "":
            return RetUtil.response_error(message="知识库ID不能为空")
        result = MongodbUtil.query_docs_by_condition(CollectionConfig.KB_COLLECTION, {"_id": ObjectId(id)})
        if len(list(result)) == 0:
            return RetUtil.response_error(message="删除的知识库不存在")

        account_id = chat_request.state.account_id
        is_own_knowledge = await Knowledge_File_service.is_own_knowledge(knowledge_id=id, account_id=account_id, db=db)
        if not is_own_knowledge:
            return RetUtil.response_error(message="数据越权")

        result = await knowledge_delete(chat_request=chat_request, id=id)
        response_body = result.body
        response_str = response_body.decode()
        result = json.loads(response_str)

        if result["status"]:
            return RetUtil.response_ok("知识库删除成功")

        else:
            return RetUtil.response_error(message=str(result["message"]))

    except Exception as e:
        detail = f"服务器错误{str(e)}"
        LogUtil.error(msg=detail)
        # 返回HTTP错误响应
        raise HTTPException(status_code=400, detail=detail)


################################################################################# 改造知识库

@router.post("/knowledge_create", summary="创建知识库", response_model=KnowledgeInfo)
async def knowledge_create(
        knowledge_info: KnowledgeInfo,
        chat_request: Request
) -> Response:
    """
    功能说明：新增知识库信息，创建知识库
    :param knowledge_info:
    :param kb_name: 知识库名称
    :param description: 知识库描述
    :param embedding_model: 嵌入模型
    :param embedding_dimension: 嵌入维度
    :param chat_request: 请求信息，获取account_id
    :param team_id: 团队id
    :return: 知识库创建时间
    """
    try:
        # 获取account_id，输出入参基本信息
        account_id = chat_request.state.account_id
        LogUtil.log_json(describe="->创建知识库", kwargs=jsonable_encoder(knowledge_info))
        LogUtil.log_json(describe="->用户id", kwargs=jsonable_encoder({"account_id": account_id}))
        LogUtil.log_json(describe="->团队id", kwargs=jsonable_encoder({"team_code": knowledge_info.team_code}))


        # # 知识库是否存在
        # if not knowledge_info.team_code:
        #     condition = {"account_id": account_id, "kb_name": knowledge_info.kb_name, "team_code": ""}
        #
        #     is_exist = await KnowledgeService.is_knowledge_exist(condition)
        #
        #     if is_exist:
        #         return RetUtil.response_error(message="知识库已经存在")

        result, info = await KnowledgeService.kb_create(knowledge_info, account_id)

        if result:
            build_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            return RetUtil.response_ok(data={"build_time": build_time})
        else:
            return RetUtil.response_error(message=info)

    except Exception as e:
        detail = f"创建知识库失败：《{e}》"
        LogUtil.info(f"创建知识库失败，失败原因：{str(traceback.format_exc())}")
        return RetUtil.response_error(message=detail)


@router.delete("/knowledge_delete", summary="删除知识库", response_model=KnowledgeInfo)
async def knowledge_delete(
        chat_request: Request,
        id: str = Body(..., description="知识库id", embed=True)
) -> Response:
    """
    功能说明：删除知识库，根据知识库id进行删除操作
    :param knowledge_id: 知识库id
    :return: 删除知识库成功
    """
    try:
        LogUtil.log_json(describe="->删除知识库id", kwargs=jsonable_encoder(id))
        account_id = chat_request.state.account_id
        agent_name_list = await KnowledgeService.is_in_agent_list(id, account_id)
        workflow_name_list = await KnowledgeService.is_in_workflow_list(id, account_id)
        if agent_name_list and workflow_name_list:
            return RetUtil.response_error(message=f"知识库被运用于智能体  {','.join(agent_name_list)}  与工作流  {','.join(workflow_name_list)}  中，无法被删除")
        elif agent_name_list:
            return RetUtil.response_error(message=f"知识库被运用于智能体  {','.join(agent_name_list)}  中，无法被删除")
        elif workflow_name_list:
            return RetUtil.response_error(message=f"知识库被运用于工作流  {','.join(workflow_name_list)}  中，无法被删除")
        # 知识库是否存在
        condition = {"_id": ObjectId(id)}
        is_exist = await KnowledgeService.is_knowledge_exist(condition)
        if not is_exist:
            return RetUtil.response_error(message="知识库不存在")

        result, info = await KnowledgeService.kb_delete(id)

        if result:
            return RetUtil.response_ok(data=info)
        else:
            return RetUtil.response_error(message=info)

    except Exception as e:
        detail = f"删除知识库失败：《{e}》"
        LogUtil.info(f"删除知识库失败，失败原因：{str(traceback.format_exc())}")
        return RetUtil.response_error(message=detail)


@router.post("/knowledge_update", summary="修改知识库")
async def knowledge_update(
    id: str = Body(..., description="知识库id", embed=True),
    description: str = Body(..., description="知识库描述", embed=True),
    team_code: Optional[str] = Body("", description="团队id", embed=True),
) -> Response:
    """
    功能说明:修改知识库,根据知识库id搜索知识库,修改知识库描述
    :param knowledge_id: 知识库id
    :param description: 知识库描述
    :return: changing_time: 修改时间
    """
    try:
        LogUtil.log_json(
            describe="->修改知识库", kwargs=jsonable_encoder({"knowledge_id": id, "description": description, "team_code": team_code})
        )

        # 知识库是否存在
        condition = {"_id": ObjectId(id)}
        is_exist = await KnowledgeService.is_knowledge_exist(condition)
        if not is_exist:
            return RetUtil.response_error(message="知识库不存在")

        await KnowledgeService.kb_update(id, description, team_code)

        changing_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return RetUtil.response_ok(data={"changing_time": changing_time})

    except Exception as e:
        detail = f"修改知识库失败：《{e}》"
        LogUtil.info(f"修改知识库失败，失败原因：{str(traceback.format_exc())}")
        return RetUtil.response_error(message=detail)


@router.post("/knowledge_query", summary="查询知识库列表", response_model=KnowledgeInfo)
async def knowledge_query(
        chat_request: Request,
        page: int = Body(..., description="页码", embed=True),
        page_size: int = Body(..., description="分页大小", embed=True),
        kb_name: str = Body("", description="知识库名称", embed=True),
        team_codes: Optional[list] = Body([], description="团队id", embed=True),
) -> Response:
    """
    功能说明：分页查询知识库列表，根据知识库名称进行模糊查询
    :param page: 页码
    :param page_size: 分页大小
    :param kb_name: 知识库名称
    :param chat_request: 请求信息，获取account_id
    :param team_codes: 团队id
    :return: 知识库数量，知识库列表
    """
    try:
        account_id = chat_request.state.account_id

        LogUtil.log_json(
            describe="->分页查询用户/团队创建的知识库",
            kwargs=jsonable_encoder(
                {"page": page, "page_size": page_size, "kb_name": kb_name, "account_id": account_id, "team_codes": team_codes}),
        )

        # 添加查询条件: 模糊查询知识库名称，用户id/团队id，查询该用户/团队创建的知识库
        if team_codes:
            condition = {"kb_name": {"$regex": f"{re.escape(kb_name)}(\\_.*)?", "$options": "i"}, "team_code": {"$in": team_codes},"temp": {"$exists": False}}
        else:
            condition = {"kb_name": {"$regex": f"{re.escape(kb_name)}(\\_.*)?", "$options": "i"}, "account_id": account_id, "team_code": "","temp": {"$exists": False}}

        kb_list, kb_len = await KnowledgeService.get_kb_pagination(condition, page, page_size)
        if isinstance(kb_list, bool):
            return RetUtil.response_error(message=kb_len)


        result = {"total": kb_len, "result": kb_list}
        LogUtil.log_json(describe="->分页获取用户/团队创建的知识库", kwargs=jsonable_encoder({"result": result}))

        return RetUtil.response_ok(data=result)

    except Exception as e:
        detail = f"获取知识库列表失败：《{e}》"
        LogUtil.info(f"获取知识库列表失败，失败原因：{str(traceback.format_exc())}")
        return RetUtil.response_error(message=detail)

@router.post("/knowledge_describe", summary="获取指定知识库文件数与处理结果数")
async def knowledge_describe(
    id: str = Body(..., description="知识库id", embed=True)
) -> Response:
    try:
        LogUtil.log_json(
            describe=f"->获取知识库id为 {id} 的文件数与处理结果数"
        )

        # 知识库是否存在
        condition = {"_id": ObjectId(id)}
        is_exist = await KnowledgeService.is_knowledge_exist(condition)
        if not is_exist:
            return RetUtil.response_error(message="知识库不存在")

        # 获取知识库描述
        description = await KnowledgeService.get_kb_describe(id)

        # 获取知识库详细信息
        file_count, result_count, id, prompt, retrieval_count, rerank_model, top_k, score, rerank_id, enhance_rounds = await KnowledgeService.knowledge_describe(id)
        return RetUtil.response_ok({"file_count": file_count, "result_count": result_count, "description": description, "id": id, "prompt": prompt, "retrieval_count": retrieval_count, "rerank_model": rerank_model, "top_k": top_k, "score": score,"rerank_id": rerank_id,"enhance_rounds":enhance_rounds})

    except Exception as e:
        detail = f"获取指定知识库文件数与处理结果数失败：《{e}》"
        LogUtil.info(f"获取指定知识库文件数与处理结果数失败，失败原因：{str(traceback.format_exc())}")
        return RetUtil.response_error(message=detail)