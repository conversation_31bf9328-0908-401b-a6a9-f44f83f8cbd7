#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :file_entity.py
@Description  :
<AUTHOR>
@Date         :2024/09/03 17:33:49
'''

from pydantic import BaseModel, Field

class FileQueryInfo(BaseModel):
    id: str = Field(..., example="test_00001", description="知识库名id")
    file_name: str = Field(..., example="test_00001", description="知识库名")
    page: int = Field(..., example=1, description="页码")
    page_size: int = Field(..., example=10, description="分页大小")

class ChunkQueryInfo(BaseModel):
    id: str = Field(..., example="test_00001", description="知识库名id")
    file_id: list = Field(..., example=["F1877671369328693248"], description="知识库名")
    page: int = Field(..., example=1, description="页码")
    page_size: int = Field(..., example=10, description="分页大小")

class FileInfoResponse(BaseModel):
    file_name: str = Field(..., example="test.xlsx", description="文件名")
    upload_time: str = Field(..., example="2024-09-03 17:39:34", description="上传时间")

class ChunkResultResponse(BaseModel):
    number: int = Field(..., example=1, description="序号")
    file_name: str = Field(..., example="test.xlsx", description="文件名")
    chunk_content: str = Field(..., example="切片内容", description="切片内容")