#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :knowledge_entity.py
@Description  :
<AUTHOR>
@Date         :2024/09/03 15:14:54
"""

from pydantic import BaseModel, Field
from typing import Optional

class KnowledgeInfo(BaseModel):
    kb_name: str = Field(..., example="test", description="知识库名称")
    description: str = Field("", example="描述知识库", description="知识库描述")
    embedding_model: str = Field(
        ..., example="bge-large-zh-v1.5", description="嵌入模型"
    )
    embedding_dimension: int = Field(..., example=1024, description="嵌入维度")
    embedding_id: str = Field(..., example="", description="嵌入模型id")
    rerank_id: str = Field(..., example="", description="重排模型id")
    team_code: Optional[str] = Field("", description="团队id", example="1")
