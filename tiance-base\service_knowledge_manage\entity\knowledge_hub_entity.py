#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :knowledge_hub_entity.py
@Description  :
<AUTHOR>
@Date         :2024/09/04 10:04:47
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Optional
from base_configs.model_config import ModelConfig


class KnowledgeRetrivalInfo(BaseModel):
    id: Optional[str] = Field("", example="tiance_test", description="知识库id")
    kb_name: Optional[str] = Field("", example="tiance_test", description="知识库名称")
    user_query: str = Field(..., example="个人信息保护", description="用户问题")
    rerank_model: Optional[str] = Field(
        "",
        example="bge-reranker-large",
        description="重排模型模型名称，由用户选择",
    )
    rerank_id: Optional[str] = Field("", example="67e4f62c3119180a08d363aa", description="重排模型id")
    recall_num: Optional[int] = Field(0, example=10, description="向量召回个数")
    rerank_num: int = Field(0, example=3, description="重排返回数量")
    score: float = Field(0.1, example=0.1, description="阈值")
    enhance_rounds: Optional[int] = Field(0, example=3, description="增强轮数")


class KnowledgeRecallEntity(BaseModel):
    chunk_content: str = Field(..., example="", description="切片内容")
    file_name: str = Field("", example="test.xlsx", description="源文件名")
    recall_score: float = Field(..., example=0.1, description="向量召回分数")
    recall_index: int = Field(..., example=1, description="向量召回排名")
    number: int = Field(..., example=1, description="文本chunk的切块序号")
    images_urls: str = Field([],example=[],description="切片的图片路径")
    location: list = Field([], example=[], description="切片的图片位置")
    file_urls: str = Field([], example=[], description="切片的文件路径")
    page: list = Field([], example=[], description="切片的文件页码")
    abandon_area: list = Field([], example=[], description="页眉页脚")
    file_id: str = Field([], example=[], description="切片的文件路径")

class KnowledgeEntity(BaseModel):
    chunk_content: str = Field(..., example="", description="切片内容")
    file_name: str = Field("", example="test.xlsx", description="源文件名")
    recall_score: float = Field(..., example=0.1, description="向量召回分数")
    recall_index: int = Field(..., example=1, description="向量召回排名")
    number: int = Field(..., example=1, description="文本chunk的切块序号")
    rerank_score: float = Field(None, example=0.9, description="重排分数")
    rerank_index: int = Field(None, example=1, description="重排排名")
    images_urls: str = Field([],example=[],description="切片的图片路径")
    location: list = Field([], example=[], description="切片的图片位置")
    file_urls: str = Field([],example=[],description="切片的文件路径")
    page: list = Field([],example=[],description="切片的文件页码")
    abandon_area: list = Field([], example=[], description="页眉页脚")
    file_id: str = Field([], example=[], description="切片的文件路径")

class KnowledgeRetrivalResponse(BaseModel):
    is_rerank: bool = Field(..., example=True, description="是否重排")
    user_query: str = Field(..., example="个人信息保护", description="用户问题")
    results: List = Field(..., example="检索结果，向量召回结果或者重排召回结果")


class KnowledgeDialogInfo(BaseModel):
    model_uid: str = Field(..., example="qwen1.5-7B-chat", description="模型UID")
    retrival_params: KnowledgeRetrivalInfo = Field(..., description="知识库检索参数")
    chatbot: List[Dict] = Field([], description="对话记录")
    history: Optional[int] = Field(3, example=3, description="对话历史轮数")
    max_token_length: Optional[int] = Field(
        4096, example=4096, description="模型最大生成长度"
    )
    temperature: Optional[float] = Field(0.8, example=0.8, description="温度参数")
