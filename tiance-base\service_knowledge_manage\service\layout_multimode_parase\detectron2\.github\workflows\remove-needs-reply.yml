name: Remove needs-more-info label

on:
  issue_comment:
    types: [created]
  issues:
    types: [edited]

jobs:
  remove-needs-more-info-label:
    runs-on: ubuntu-latest
    # 1. issue_comment events could include PR comment, filter them out
    # 2. Only trigger action if event was produced by the original author
    if: ${{ !github.event.issue.pull_request && github.event.sender.login == github.event.issue.user.login }}
    steps:
      - name: Remove needs-more-info label
        uses: octokit/request-action@v2.x
        continue-on-error: true
        with:
          route: DELETE /repos/:repository/issues/:issue/labels/:label
          repository: ${{ github.repository }}
          issue: ${{ github.event.issue.number }}
          label: needs-more-info
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
