name: CI
on:
  push:
  pull_request:
  schedule:
    - cron: "0 0 * * *"  # @daily

# Run linter with github actions for quick feedbacks.
jobs:
  linter:
    runs-on: ubuntu-latest
    # run on PRs, or commits to facebookresearch (not internal)
    if: ${{ github.repository_owner == 'facebookresearch' || github.event_name == 'pull_request' }}
    steps:
      - uses: actions/checkout@v2
      - name: Set up Python 3.9
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - name: Install dependencies
        # flake8-bugbear flake8-comprehensions are useful but not available internally
        run: |
          python -m pip install --upgrade pip
          python -m pip install flake8==6.1.0 isort==4.3.21
          python -m pip install black==24.3.0
          flake8 --version
      - name: Lint
        run: |
          echo "Running isort"
          isort -c -sp .
          echo "Running black"
          black -l 100 --check .
          echo "Running flake8"
          flake8 .

  macos_tests:
    runs-on: macos-latest
    # run on PRs, or commits to facebookresearch (not internal)
    if: ${{ github.repository_owner == 'facebookresearch' || github.event_name == 'pull_request' }}
    strategy:
      fail-fast: false
      matrix:
        torch: ["1.13.1", "2.2.2"]
        include:
          - torch: "1.13.1"
            torchvision: "0.14.1"
          - torch: "2.2.2"
            torchvision: "0.17.2"
    env:
      # point datasets to ~/.torch so it's cached by CI
      DETECTRON2_DATASETS: ~/.torch/datasets
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8
      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: |
            ${{ env.pythonLocation }}/lib/python3.8/site-packages
            ~/.torch
          key: ${{ runner.os }}-torch${{ matrix.torch }}-${{ hashFiles('setup.py') }}-20220119

      - name: Install dependencies
        run: |
          python -m pip install -U pip
          python -m pip install wheel ninja opencv-python-headless onnx pytest-xdist
          python -m pip install torch==${{matrix.torch}} torchvision==${{matrix.torchvision}} -f https://download.pytorch.org/whl/torch_stable.html
          # install from github to get latest; install iopath first since fvcore depends on it
          python -m pip install -U 'git+https://github.com/facebookresearch/iopath'
          python -m pip install -U 'git+https://github.com/facebookresearch/fvcore'
          wget https://raw.githubusercontent.com/pytorch/pytorch/master/torch/utils/collect_env.py
          python collect_env.py

      - name: Build and install
        run: |
          CC=clang CXX=clang++ python -m pip install -e .[all]
          python -m detectron2.utils.collect_env
          ./datasets/prepare_for_tests.sh
      - name: Run unittests
        run: python -m pytest -n 4 --durations=15 -sv tests/

  linux_gpu_tests:
    runs-on: 4-core-ubuntu-gpu-t4
    # run on PRs, or commits to facebookresearch (not internal)
    if: ${{ github.repository_owner == 'facebookresearch' || github.event_name == 'pull_request' }}
    strategy:
      fail-fast: false
      matrix:
        torch: ["1.13.1", "2.2.2"]
        include:
          - torch: "1.13.1"
            torchvision: "0.14.1"
            cuda: "11-7"
          - torch: "2.2.2"
            torchvision: "0.17.2"
            cuda: "12-5"
    env:
      PYTORCH_INDEX: "https://download.pytorch.org/whl/cu118"
      DETECTRON2_DATASETS: ~/.torch/datasets
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Cache dependencies
        uses: actions/cache@v2
        id: load-cache
        with:
          path: |
            ${{ env.pythonLocation }}/lib/python3.8/site-packages
            ~/.torch
          key: ${{ runner.os }}-torch-gpu${{ matrix.torch }}-${{ hashFiles('setup.py') }}-20210827

      - name: Install GPU Dependencies
        uses: ./.github/actions/install_linux_gpu_dep
        with:
          cuda-version: ${{matrix.cuda}}

      - name: Install Dependencies
        uses: ./.github/actions/install_linux_dep
        if: steps.load-cache.outputs.cache-hit != 'true'
        with:
          torch-version: ${{matrix.torch}}
          torchvision-version: ${{matrix.torchvision}}
          pytorch-index: $PYTORCH_INDEX

      - name: Install Detectron2
        uses: ./.github/actions/install_detectron2

      - name: Run Unit Tests
        uses: ./.github/actions/run_unittests

      - name: Run Tests After Uninstalling
        uses: ./.github/actions/uninstall_tests

  linux_cpu_tests:
    runs-on: ubuntu-latest
    # run on PRs, or commits to facebookresearch (not internal)
    if: ${{ github.repository_owner == 'facebookresearch' || github.event_name == 'pull_request' }}
    strategy:
      fail-fast: false
      matrix:
        torch: ["1.13.1", "2.2.2"]
        include:
          - torch: "1.13.1"
            torchvision: "0.14.1"
          - torch: "2.2.2"
            torchvision: "0.17.2"
    env:
      PYTORCH_INDEX: "https://download.pytorch.org/whl/cu118"
      DETECTRON2_DATASETS: ~/.torch/datasets
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Cache dependencies
        uses: actions/cache@v2
        id: load-cache
        with:
          path: |
            ${{ env.pythonLocation }}/lib/python3.8/site-packages
            ~/.torch
          key: ${{ runner.os }}-torch${{ matrix.torch }}-${{ hashFiles('setup.py') }}-20210827

      - name: Install Dependencies
        uses: ./.github/actions/install_linux_dep
        if: steps.load-cache.outputs.cache-hit != 'true'
        with:
          torch-version: ${{matrix.torch}}
          torchvision-version: ${{matrix.torchvision}}
          pytorch-index: $PYTORCH_INDEX

      - name: Install Detectron2
        uses: ./.github/actions/install_detectron2

      - name: Run Unit Tests
        uses: ./.github/actions/run_unittests

      - name: Run Tests After Uninstalling
        uses: ./.github/actions/uninstall_tests

  windows_cpu_tests:
    runs-on: windows-latest
    # run on PRs, or commits to facebookresearch (not internal)
    if: ${{ github.repository_owner == 'facebookresearch' || github.event_name == 'pull_request' }}
    strategy:
      fail-fast: false
      matrix:
        torch: ["1.13.1", "2.2.2"]
        include:
          - torch: "1.13.1"
            torchvision: "0.14.1"
          - torch: "2.2.2"
            torchvision: "0.17.2"
    env:
      PYTORCH_INDEX: "https://download.pytorch.org/whl/cu118"
      DETECTRON2_DATASETS: ~/.torch/datasets
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Cache dependencies
        uses: actions/cache@v2
        id: load-cache
        with:
          path: |
            ${{ env.pythonLocation }}\Lib\site-packages
            ~\.torch
          key: ${{ runner.os }}-torch${{ matrix.torch }}-${{ hashFiles('setup.py') }}-20210404

      - name: Install Dependencies
        uses: ./.github/actions/install_windows_dep
        if: steps.load-cache.outputs.cache-hit != 'true'
        with:
          torch-version: ${{matrix.torch}}
          torchvision-version: ${{matrix.torchvision}}
          pytorch-index: $PYTORCH_INDEX

      - name: Install Detectron2
        uses: ./.github/actions/install_detectron2_win

      - name: Run Unit Tests
        uses: ./.github/actions/run_unittests_win
