_BASE_: "../Base-RCNN-FPN.yaml"
MODEL:
  # WEIGHTS: "detectron2://ImageNetPretrained/MSRA/R-50.pkl"
  # For better, more stable performance initialize from COCO
  WEIGHTS: "detectron2://COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x/137849600/model_final_f10217.pkl"
  MASK_ON: True
  ROI_HEADS:
    NUM_CLASSES: 8
# This is similar to the setting used in Mask R-CNN paper, Appendix A
# But there are some differences, e.g., we did not initialize the output
# layer using the corresponding classes from COCO
INPUT:
  MIN_SIZE_TRAIN: (800, 832, 864, 896, 928, 960, 992, 1024)
  MIN_SIZE_TRAIN_SAMPLING: "choice"
  MIN_SIZE_TEST: 1024
  MAX_SIZE_TRAIN: 2048
  MAX_SIZE_TEST: 2048
DATASETS:
  TRAIN: ("cityscapes_fine_instance_seg_train",)
  TEST: ("cityscapes_fine_instance_seg_val",)
SOLVER:
  BASE_LR: 0.01
  STEPS: (18000,)
  MAX_ITER: 24000
  IMS_PER_BATCH: 8
TEST:
  EVAL_PERIOD: 8000
