#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :ai_assistant.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:58:57
'''

import json
from fastapi import APIRouter
from configs.model_config import ModelConfig
from configs.prompt_config import PromptConfig
from configs.collection_config import CollectionConfig
from configs.run_config import RunConfig
from entity.message_entity import SystemMessage, UserMessage, AssistantMessage, MessageConverter
from entity.response_entity import FalseResponse
from entity.request_entity import AIAssistantRequest
from service.llm_service import Llm_Service
from sse_starlette.sse import EventSourceResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.time_util import TimeUtil
from utils.uuid_util import UuidUtil
from utils.text_utils import TextUtil

router = APIRouter()
@router.post("/ai_assistant", summary="问答助手")
async def ai_assistant(request: AIAssistantRequest) -> EventSourceResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        user_id = request.user_id
        question = request.question
        collection_name = CollectionConfig.AI_ASSISTANT

        LogUtil.info("1.获取历史消息")
        history_messages = MongodbUtil.coll(collection_name).find({"user_id": user_id}).sort('answer_date', -1).limit(RunConfig.HISTORY_LEN)
        
        LogUtil.info("2.组织消息")
        messages = []
        messages.append(SystemMessage(PromptConfig.AI_ASSISTANT_SYSTEM_PROMPT))
        for item in reversed(list(history_messages)):
            messages.append(UserMessage(item.get("question","")))
            messages.append(AssistantMessage(item.get("answer","")))
        messages.append(UserMessage(question))
        messages = MessageConverter.convert_messages(messages)
        
        llm_service = Llm_Service(model=ModelConfig.MAX_LLM_MODEL_NAME)
        
        async def generate_answer():
            # 获得模型回答
            complete_answer = ""
            async for chunk in await llm_service.stream_answer_question(messages=messages, model=ModelConfig.MAX_LLM_MODEL_NAME, max_tokens=4096):
                if chunk.choices[0].delta.content != "" and chunk.choices[0].delta.content != None:
                    token = chunk.choices[0].delta.content
                    yield json.dumps({"code": 200, "message": "success", "data": token}, ensure_ascii=False)
                    # 拼接完整的答案
                    complete_answer += token
            
            # 是否入库
            if RunConfig.IS_SAVE_DATABASE:
                answer_date = TimeUtil.current_timestamp()
                complete_answer = TextUtil.remove_think(complete_answer)
                save_data = {"_id": UuidUtil.get_uuid(), "user_id": user_id, "question": question, "answer": complete_answer,"answer_date": answer_date}
                MongodbUtil.insert_one(collection_name=collection_name, doc_content=save_data)
        
        LogUtil.info("3.生成回答")
        answer = generate_answer()
        return EventSourceResponse(answer)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
