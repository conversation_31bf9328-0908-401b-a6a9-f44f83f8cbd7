
#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :annual_report_api.py
@Description  :该API用于从年报中提取各类公司信息。
<AUTHOR>
@Date         :2024/12/25 10:01:57
"""


from fastapi import APIRouter, HTTPException
from service.annual_report_service import AnnualReportService
from entity.annual_report_entity import AnnualReportEntity,AnnualReportFileEntity,SuccessResponse,FalseResponse
from utils.log_util import LogUtil
from configs.collection_config import CollectionConfig
from utils.mongodb_util import MongodbUtil
import re
from service.llm_extract_name_service import LlmHelper
llm_helper = LlmHelper()

router = APIRouter()
# def common_build_ext(company_name,pdf_filename,year,industry_chain):
#         # 开始存储数据
#         MongodbUtil.connect()
#         result = MongodbUtil.query_docs_by_condition(CollectionConfig.ANNUAL_REPORT_HISTORY,{"company_name":company_name,"year":year})
#         result = list(result)
#         if len(result) > 0:
#             print("has data")
#             response = SuccessResponse(data=result[0])
#             return response
#      # 暂时实例化一些类
#         milvus_util = MilvusUtil()
#         embutil = TextEmbedService()
#         com_analysis = AnnualReportCompanyAnalysis(milvus_util,embutil)
#     # 母公司
#         parentand_result = com_analysis.company_parentand(company_name,pdf_filename)
#
#         # 子公司
#         subsidiary_result = com_analysis.company_subsidiary(company_name,pdf_filename)
#
#         # 合营联营企业
#         cooperative_enterprise_result = com_analysis.company_cooperative_enterprise(company_name,pdf_filename)
#
#         # 其他关联方企业
#         other_related_result = com_analysis.company_other_related(company_name,pdf_filename)
#
#         # 采购商品或接受劳务
#         purchase_goods_result = com_analysis.company_purchase_goods(company_name,pdf_filename)
#
#         # 出售商品或提供劳务
#         sell_goods_result = com_analysis.company_sell_goods(company_name,pdf_filename)
#
#         # 租赁或者出租方
#         lessor_result = com_analysis.company_lessor(company_name,pdf_filename)
#
#         # 担保或者担保方
#         guarantee_result = com_analysis.company_guarantee(company_name,pdf_filename)
#
#         # 被担保或者被担保方
#         guaranteed_result = com_analysis.company_guaranteed(company_name,pdf_filename)
#
#         # 资金拆借的资金拆入
#         funds_borrowed_result = com_analysis.company_funds_borrowed(company_name,pdf_filename)
#
#         # 资金拆借的资金拆出
#         funds_withdrawal_result = com_analysis.company_funds_withdrawal(company_name,pdf_filename)
#
#         # 资产转让
#         asset_transfer_result = com_analysis.company_asset_transfer(company_name,pdf_filename)
#
#         # 债务重组
#         debt_restructuring_result = com_analysis.company_debt_restructuring(company_name,pdf_filename)
#
#
#         results_list = []
#         results_list.append(parentand_result)
#         results_list.append(subsidiary_result)
#         results_list.append(cooperative_enterprise_result)
#         results_list.append(other_related_result)
#         results_list.append(purchase_goods_result)
#         results_list.append(sell_goods_result)
#         results_list.append(lessor_result)
#         results_list.append(guarantee_result)
#         results_list.append(guaranteed_result)
#         results_list.append(funds_borrowed_result)
#         results_list.append(funds_withdrawal_result)
#         results_list.append(asset_transfer_result)
#         results_list.append(debt_restructuring_result)
#         for result in results_list:
#             for key,value in result.items():
#                 if key == "is_keyword_hit":
#                     continue
#                 # print(key,value)
#                 value = common_clean_process(value)
#                 result[key] = value
#
#
#
#
#         search_condition = {'source_type': {'$regex': "年报"},'title': {"$regex": ".*" + re.escape(pdf_filename) + ".*", "$options": "i"}}
#
#
#         id_list = []
#         doc_list = MongodbUtil.query_docs_by_condition(collection_name='source_notice', search_condition=search_condition)
#         for item in doc_list:
#             id_list.append(item["_id"])
#
#         doc_info = MongodbUtil.query_doc_by_id(collection_name='source_notice', doc_id=id_list[0])
#
#         # 公司名称
#         company_name = company_name
#
#         # 年报url
#         file_url = doc_info['source_url']
#         print(file_url)
#         # 年报文件发布时间
#         document_path = doc_info['document_path']
#         parts = document_path.split('/')
#         file_time_list = parts[2:5]
#         ful_file_time_list = []
#         for i in file_time_list:
#             if len(i) ==1:
#                 ful_i = "0" + i
#                 ful_file_time_list.append(ful_i)
#             else:
#                 ful_file_time_list.append(i)
#         file_time = '-'.join(ful_file_time_list)
#
#         # 文件mongodbid
#         source_id = id_list[0]
#
#
#
#         final_list = []
#
#         # 结构化
#         for data_dict in results_list:
#             is_hit = data_dict["is_keyword_hit"]
#             for classes, company_list in data_dict.items():
#                 if classes == 'is_keyword_hit':
#                     # 这里是判断是否keyword命中
#                     is_hit = company_list
#                     continue
#                 for i,value in enumerate(company_list):
#                     # print(company_list)
#                     # 存在简称，如比亚迪
#                     # if len(value)>=4:
#                     result_dict = {
#                                 "industry_chain":industry_chain,
#                                 "links":"",
#                                 "company_name":company_name,
#                                 "released_time":file_time,
#                                 "source_id":source_id,
#                                 "affiliate":value,
#                                 "relation_type":classes,
#                                 "affiliate_register_address":"",
#                                 "affiliate_business_address":"",
#                                 "year":"",
#                                 "ranking":"",
#                                 "product":"",
#                                 "notes":"",
#                                 "is_keyword_hit":is_hit
#                             }
#                     final_list.append(result_dict)
#         save_data={"公告url":file_url,"公司数量":len(final_list),"年报关联公司汇总":results_list, "待入库具体信息": final_list,"company_name":company_name,"file_time":file_time, "source_id":source_id}
#         save_data["_id"] = uuid.uuid1().hex
#         save_data["year"] = year
#         result = MongodbUtil.query_docs_by_condition(CollectionConfig.ANNUAL_REPORT_HISTORY,{"company_name":company_name,"year":year})
#         if len(list(result)) > 0:
#             print("has data")
#             # print(len(list(result)))
#         else:
#             result = MongodbUtil.insert_one(collection_name=CollectionConfig.ANNUAL_REPORT_HISTORY, doc_content=save_data)
#         # result = MongodbUtil.insert_one(collection_name=CollectionConfig.ANNUAL_REPORT_HISTORY, doc_content=save_data)
#
#         # print(result)
#         response = SuccessResponse(data=save_data)
#         return response
# def common_build_ext(company_name,pdf_filename,year,industry_chain):
#         # 开始存储数据
#         MongodbUtil.connect()
#         result = MongodbUtil.query_docs_by_condition(CollectionConfig.ANNUAL_REPORT_HISTORY,{"company_name":company_name,"year":year})
#         result = list(result)
#         if len(result) > 0:
#             print("has data")
#             response = SuccessResponse(data=result[0])
#             return response
#      # 暂时实例化一些类
#         milvus_util = MilvusUtil()
#         embutil = TextEmbedService()
#         com_analysis = AnnualReportCompanyAnalysis(milvus_util,embutil)
#     # 母公司
#         parentand_result = com_analysis.company_parentand(company_name,pdf_filename)
#
#         # 子公司
#         subsidiary_result = com_analysis.company_subsidiary(company_name,pdf_filename)
#
#         # 合营联营企业
#         cooperative_enterprise_result = com_analysis.company_cooperative_enterprise(company_name,pdf_filename)
#
#         # 其他关联方企业
#         other_related_result = com_analysis.company_other_related(company_name,pdf_filename)
#
#         # 采购商品或接受劳务
#         purchase_goods_result = com_analysis.company_purchase_goods(company_name,pdf_filename)
#
#         # 出售商品或提供劳务
#         sell_goods_result = com_analysis.company_sell_goods(company_name,pdf_filename)
#
#         # 租赁或者出租方
#         lessor_result = com_analysis.company_lessor(company_name,pdf_filename)
#
#         # 担保或者担保方
#         guarantee_result = com_analysis.company_guarantee(company_name,pdf_filename)
#
#         # 被担保或者被担保方
#         guaranteed_result = com_analysis.company_guaranteed(company_name,pdf_filename)
#
#         # 资金拆借的资金拆入
#         funds_borrowed_result = com_analysis.company_funds_borrowed(company_name,pdf_filename)
#
#         # 资金拆借的资金拆出
#         funds_withdrawal_result = com_analysis.company_funds_withdrawal(company_name,pdf_filename)
#
#         # 资产转让
#         asset_transfer_result = com_analysis.company_asset_transfer(company_name,pdf_filename)
#
#         # 债务重组
#         debt_restructuring_result = com_analysis.company_debt_restructuring(company_name,pdf_filename)
#
#
#         results_list = []
#         results_list.append(parentand_result)
#         results_list.append(subsidiary_result)
#         results_list.append(cooperative_enterprise_result)
#         results_list.append(other_related_result)
#         results_list.append(purchase_goods_result)
#         results_list.append(sell_goods_result)
#         results_list.append(lessor_result)
#         results_list.append(guarantee_result)
#         results_list.append(guaranteed_result)
#         results_list.append(funds_borrowed_result)
#         results_list.append(funds_withdrawal_result)
#         results_list.append(asset_transfer_result)
#         results_list.append(debt_restructuring_result)
#         for result in results_list:
#             for key,value in result.items():
#                 if key == "is_keyword_hit":
#                     continue
#                 # print(key,value)
#                 value = common_clean_process(value)
#                 result[key] = value
#
#
#
#
#         search_condition = {'source_type': {'$regex': "年报"},'title': {"$regex": ".*" + re.escape(pdf_filename) + ".*", "$options": "i"}}
#
#
#         id_list = []
#         doc_list = MongodbUtil.query_docs_by_condition(collection_name='source_notice', search_condition=search_condition)
#         for item in doc_list:
#             id_list.append(item["_id"])
#
#         doc_info = MongodbUtil.query_doc_by_id(collection_name='source_notice', doc_id=id_list[0])
#
#         # 公司名称
#         company_name = company_name
#
#         # 年报url
#         file_url = doc_info['source_url']
#         print(file_url)
#         # 年报文件发布时间
#         document_path = doc_info['document_path']
#         parts = document_path.split('/')
#         file_time_list = parts[2:5]
#         ful_file_time_list = []
#         for i in file_time_list:
#             if len(i) ==1:
#                 ful_i = "0" + i
#                 ful_file_time_list.append(ful_i)
#             else:
#                 ful_file_time_list.append(i)
#         file_time = '-'.join(ful_file_time_list)
#
#         # 文件mongodbid
#         source_id = id_list[0]
#
#
#
#         final_list = []
#
#         # 结构化
#         for data_dict in results_list:
#             is_hit = data_dict["is_keyword_hit"]
#             for classes, company_list in data_dict.items():
#                 if classes == 'is_keyword_hit':
#                     # 这里是判断是否keyword命中
#                     is_hit = company_list
#                     continue
#                 for i,value in enumerate(company_list):
#                     # print(company_list)
#                     # 存在简称，如比亚迪
#                     # if len(value)>=4:
#                     result_dict = {
#                                 "industry_chain":industry_chain,
#                                 "links":"",
#                                 "company_name":company_name,
#                                 "released_time":file_time,
#                                 "source_id":source_id,
#                                 "affiliate":value,
#                                 "relation_type":classes,
#                                 "affiliate_register_address":"",
#                                 "affiliate_business_address":"",
#                                 "year":"",
#                                 "ranking":"",
#                                 "product":"",
#                                 "notes":"",
#                                 "is_keyword_hit":is_hit
#                             }
#                     final_list.append(result_dict)
#         save_data={"公告url":file_url,"公司数量":len(final_list),"年报关联公司汇总":results_list, "待入库具体信息": final_list,"company_name":company_name,"file_time":file_time, "source_id":source_id}
#         save_data["_id"] = uuid.uuid1().hex
#         save_data["year"] = year
#         result = MongodbUtil.query_docs_by_condition(CollectionConfig.ANNUAL_REPORT_HISTORY,{"company_name":company_name,"year":year})
#         if len(list(result)) > 0:
#             print("has data")
#             # print(len(list(result)))
#         else:
#             result = MongodbUtil.insert_one(collection_name=CollectionConfig.ANNUAL_REPORT_HISTORY, doc_content=save_data)
#         # result = MongodbUtil.insert_one(collection_name=CollectionConfig.ANNUAL_REPORT_HISTORY, doc_content=save_data)
#
#         # print(result)
#         response = SuccessResponse(data=save_data)
#         return response

@router.post("/annual_report_info_ext", summary="年报相关公司信息提取")
async def annual_report_info_ext(request: AnnualReportEntity) -> SuccessResponse:
    """
    年报相关公司信息提取的接口, 点击 Try it out 进行测试(处理时间约30-50s)
    
    company_name : 表示需要提取的年报文件关键字,请从"中国石油","南钢股份","天奇股份","中国石化"选择,分别代表对应的年报文件
    """
    try:
        # # 暂时实例化一些类
        # milvus_util = MilvusUtil()
        # embutil = TextEmbedService()
        # com_analysis = AnnualReportCompanyAnalysis(milvus_util,embutil)
        # 提取输入的数据
        company_name = request.company_name
        industry_chain = "工业机器人"
        pdf_name_to_filename = {
            "中国石油": "中国石油天然气股份有限公司2023年度报告",
            "南钢股份":"南钢股份-南京钢铁股份有限公司2023年年度报告",
            "天奇股份":"天奇股份-2023年年度报告",
            "中国石化":"中国石化2023年年度报告"
        }
        pdf_filename = pdf_name_to_filename.get(company_name)
        
        if not pdf_filename:
            raise HTTPException(status_code=400, detail="PDF文件名不符合要求")
        
        # 2025 0520 默认为CollectionConfig.ANNUAL_REPORT_MILVUS
        response = AnnualReportService.common_build_ext(company_name=company_name,pdf_filename=pdf_filename,year="2023",industry_chain=industry_chain,collection=CollectionConfig.ANNUAL_REPORT_MILVUS)
        # print(f"results_list:{results_list}")
        # print(f"final_list:{final_list}")
        return SuccessResponse(data=response)
    
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        data = {"error": detail}
        response = FalseResponse(data=data)
        return response


@router.post("/annual_report_info_ext_with_file", summary="年报相关公司信息提取")
async def annual_report_info_ext_with_file(request: AnnualReportFileEntity) -> SuccessResponse:
    """
    年报相关公司信息提取的接口, 点击 Try it out 进行测试(处理时间约30-50s)
    
    company_name : 表示需要提取的年报文件关键字
    pdf_filename : 文件名
    year ： 文件年份，用于入库
    """
    try:    
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
       
        # 提取输入的数据
        company_name = request.company_name
   
        pdf_filename = request.pdf_filename

        year = request.year

        industry_chain = request.industry_chain

        collection = request.milvus_collection

        is_cached = request.is_cached

        mongodb_id = request.mongodb_id
        if mongodb_id != '':
            doc_info = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.NOTICE_ALL, doc_id=mongodb_id)
            if doc_info["data"]["data_type"] != "年报":
                doc_type = doc_info["data"]["data_type"] 
                msg = f"unsupport type from mongoid :{doc_type}"
                LogUtil.error(msg=msg)
                response = FalseResponse(data=msg)
                return response
            else:
                company_name = llm_helper.extract_name(pdf_filename=pdf_filename)
                if isinstance(doc_info["file"]["file_flag"]["company_abb"] , list): 
                    company_name = doc_info["file"]["file_flag"]["company_abb"][0]
                elif isinstance(doc_info["file"]["file_flag"]["company_abb"] , str):
                    company_name = doc_info["file"]["file_flag"]["company_abb"]
                pdf_filename = doc_info["file"]["file_title"] + "." + doc_info["file"]["file_type"]
                # pdf_filename =
                pattern = r"(\d{4})年"
                year = re.findall(pattern, pdf_filename)[0]
                collection = doc_info["milvus"]["milvus_collection_name"]
                # 
        response = AnnualReportService.common_build_ext(company_name=company_name,pdf_filename=pdf_filename,year=year,industry_chain=industry_chain,collection=collection,mongodb_id=mongodb_id,is_cached=is_cached)
        # print(f"results_list:{results_list}")
        # print(f"final_list:{final_list}")
        return SuccessResponse(data=response)
    
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        response = FalseResponse(data=data)
        return response






