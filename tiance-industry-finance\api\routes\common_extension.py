#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :common_extension.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 16:31:48
'''

from fastapi import APIRouter
from entity.request_entity import ExtensionRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig

router = APIRouter()
@router.post("/common_extension", summary="通用扩展")
async def common_extension(request: ExtensionRequest) -> SuccessResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="通用扩展请求", kwargs=dict(request))
        node_list = request.node_name_info.split('|')
        root_node = node_list[0]
        current_node = node_list[-1]
        # 查询条件
        query = {"secondary_industry": root_node, "current_industry": current_node}
        # 返回字段
        projection  = {"stream_type", "industry_information"}
        stream_result = list(MongodbUtil.coll(CollectionConfig.COMMON_EXTENSION).find(query, projection))
        results = []
        for item in stream_result:
            for name in item["industry_information"]:
                results.append({"child_name": name, "child_type": item["stream_type"], "total_source": 0, "source_list": []})
        total = len(results)
        data = {"total": total, "child_list": results}
        # 记录返回日志
        LogUtil.log_json(describe="通用扩展请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
