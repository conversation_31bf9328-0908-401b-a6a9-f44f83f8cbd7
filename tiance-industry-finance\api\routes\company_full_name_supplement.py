#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :company_full_name_supplement.py
@Description  :
<AUTHOR>
@Date         :2025/03/18 11:10:20
'''

import re
from entity.request_entity import CompanyFullNameSupplementRequest
from fastapi import APIRouter,Body,Response
from configs.label_value import label_list,dis_list
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.milvus_util import MilvusUtil
from service.llm_service import Llm_Service
from collections import Counter
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from service.text_embed_service import TextEmbedService
import numpy as np
import math
router = APIRouter()
@router.post("/company_full_name_supplement", summary="补充公司全称")
async def company_full_name_supplement(request: CompanyFullNameSupplementRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        abb_dict_cn = {}
        abb_dict = {}#
        abbs = request.node_companies  # 获取根节点企业简称列表
        messages = []  # 系统提示语
        for abb in abbs:
            if abb not in abb_dict:
                # 查询基本信息集合
                company_basic = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one(
                    {"$or": [{"公司简称": abb}, {"公司名称": abb}, {"曾用名": {"$regex": re.escape(abb + "(")}}]}
                )
                if company_basic:
                    abb_dict[abb] = company_basic.get("公司名称")
                else:
                    # 查询品牌信息集合
                    company_brand = MongodbUtil.coll(CollectionConfig.BRAND_INFO).find_one(
                        {"$or": [{"品牌/产品名称": abb}, {"公司全称": abb}]}
                    )
                    if company_brand:
                        abb_dict[abb] = company_brand.get("公司全称")
        abb_dict_cn["数据库补充公司全称"] = abb_dict

        # 过滤未检索到全称的企业
        filter_companies = [abb for abb in abbs if abb not in abb_dict]
        system_prompts = '''\# 角色 你是一名企业公司品牌或简称转换企业公司全称助手，了解各种海内外企业和公司的中文全称。能够准确把海内外企业公司的品牌或简称转换为企业公司中文全称。 ## 技能 ### 技能1： 企业公司的简称转换为企业公司中文全称。技能2： 企业公司的品牌简称转换为企业公司中文全称。技能3：海外公司\企业所对应的全称为该公司\企业在中国成立的公司\企业中文名称\\
         ## 限制: - 1、输出的企业公司中文全称真实有效。2、如未明确匹配公司品牌或简称对应的中文全称，请给出与品牌或简称近似或关联的企业全称。3、返回格式严格按照{公司简称：xx，公司中文全称:XXX}格式给出。如果存在多个近似或关联公司全称，严格按照{公司简称：xx，公司中文全称1:XXX，公司中文全称2:YYY}格式给出'''
        question = f'请依次给出以下企业公司全称：{filter_companies}'
        messages.append({"role": "system", "content": system_prompts})
        messages.append({"role": "user", "content": question})
        if filter_companies:
            # 调用模型生成全称
            model = request.model
            llm_service = Llm_Service(model)
            answer1 = await llm_service.answer_question(messages, model, max_tokens=4096)
            if 'deepseek' in model.lower():
                # 使用正则表达式匹配</think>标签后的所有内容
                match = re.search(r'</think>\s*(.*)', answer1, re.DOTALL)
                if match:
                    answer = match.group(1).strip()
                    # 移除json、换行符和代码块符号
                    answer = re.sub(r'json|```|\n|\|', '', answer)
                else:
                    raise ValueError("未找到</think>标签")
            else:
                answer = answer1
            abb_dict_cn["模型补充公司全称"] = answer

        # 记录返回日志
        return SuccessResponse(data=abb_dict_cn)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        return FalseResponse(data={"error": detail})


@router.post("/label", summary="标签提取")
def label() -> Response:
    try:
        # 记录日志
        LogUtil.info("1.根据mongodb_id检索得到向量数据库信息(包括向量数据库名,向量文本块字段名,标题)")
        labelmongo_name = ["invoice_label_info","customs_label_info","research_report_label_info","credit_report_label_info"]
        milvus_info_list = []
        file_title_list = []
        label_dic = {}
        value_dics ={}
        for label in labelmongo_name:
            label_info = MongodbUtil.query_all_doc(label)
            for doc in label_info:
                milvus_collection_name = doc.get("milvus_collection_name")
                milvus_info_list.append(milvus_collection_name)
                if doc["file_flag"].get("权威度") is not None:
                    value_dics[doc.get("file_title")] = int(doc["file_flag"].get("权威度"))
        milvus_info_list = list(set(milvus_info_list))
        milvus_info_lists = [item for item in milvus_info_list if item != 'None']
        kb_service = MilvusUtil()
        for input_label in label_list:
            label_info_list =[]
            for milvus_collection_name in milvus_info_lists:
                query_conditions = f"chunk_content_father like '%{input_label}%'"
                kb_docs =kb_service.query_by_scalar(milvus_collection_name, query_conditions)
                for kb_doc in kb_docs:
                    file_title = kb_doc.get("file_title")
                    file_title_list.append(file_title)
            counter = Counter(file_title_list)
            count_list = list(counter.items())

            for item, count in count_list:
                count_dict = {}
                count_dict ["title"]= item
                count_dict["num"] = count
                file_value = value_dics.get(item)
                if file_value:
                    count_dict["value"] = value_dics[item]
                label_info_list.append(count_dict)
            label_dic[input_label] = label_info_list
        label_num_value_info = MongodbUtil.query_all_doc('label_num_value')
        for doc in label_num_value_info:
            id = doc.get("_id")
        MongodbUtil.del_doc_by_id('label_num_value',id)
        MongodbUtil.insert_one("label_num_value", label_dic)
        return SuccessResponse(data=label_dic)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        return FalseResponse(data={"error": detail})



@router.post("/label_replace", summary="标签替换")
async def label_replace(
            input_labels: list = Body([], embed=True, examples=["test"], description="标签列表"),
            style: str = Body("", embed=True, examples=["test"], description="标签划分方式")) -> Response:
    try:
        label_num_value_infos = MongodbUtil.query_all_doc('label_num_value')
        for doc in label_num_value_infos:
            id = doc.get("_id")
        label_num_value_info=MongodbUtil.query_doc_by_id('label_num_value',id)
        del label_num_value_info["_id"]
        end_list = []
        result ={}
        labels = label_num_value_info

        # 计算所有num的总和
        for label in labels.keys():
            total_num = 0
            total_value = 0
            for item in labels[label]:
                value = item.get("value")
                total_num += item["num"]
                if value:
                    total_value += item["num"] * item["value"]
                result[label] = {"num": total_num,"value": total_value}
        re_list =[]
        for input in input_labels:
            input_result = None
            for sublist  in dis_list:
                if input in sublist :
                    input_result = sublist
                    break
            if input_result:
                re = {}
                input_result_list = input_result
                for item in input_result_list:
                    re[item] = result[item]
                max_num = -1

                re_dic = {}
                # 遍历字典，找到num最大的键
                for key, value_dict in re.items():
                    if value_dict["num"] > max_num:
                        max_num = value_dict["num"]
                        max_num_key = key
                        re_dic["max_num"] =max_num_key
                max_num = -1
                for key, value_dict in re.items():
                    if value_dict["value"] > max_num:
                        max_num = value_dict["value"]
                        max_value_key = key
                        re_dic["max_value"] =max_value_key
                re_list.append(re_dic)

            else:
                inputs = {}
                inputs['max_value']=input
                inputs['max_num'] = input
                re_list.append(inputs)
        if style == 'num':
            for item in re_list:
                end_value = item['max_num']
                end_list.append(end_value)
        if style == 'value':
            for item in re_list:
                end_value = item['max_value']
                end_list.append(end_value)
        end_dict = {}
        end_dict["result"] = end_list
        return SuccessResponse(data=end_dict)

    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)

@router.post("/label_similar", summary="标签相似分类")
async def label_similar(
        input_labels: list = Body([], embed=True, examples=["test"], description="工作流id"),) -> SuccessResponse | FalseResponse:
    try:
        def find_knn(vector_a, vectors_b, k=5):
            distances = [np.sqrt(np.sum((np.array(vector_a) - np.array(vector_b)) ** 2)) for vector_b in vectors_b]
            sorted_indices = np.argsort(distances)
            knn_indices = sorted_indices[1:k]
            # knn_vectors = [vectors_b[i] for i in knn_indices]
            return knn_indices
        embed_list = []
        embed_dic ={}
        input_dic ={}
        index = 0
        for doc in input_labels:
            embed_service = TextEmbedService()
            embed_vector= await embed_service.text_embedding([doc])
            embed_dic[index] = embed_vector[0]
            input_dic[index] = doc
            embed_list.append(embed_vector[0])
            index += 1

        index = 0
        knn_dic = {}
        for item in embed_list:
            knn_vectors = find_knn(item, embed_list, k=2)
            knn_dic[index] = list(knn_vectors)
            # knn_dic[f"{index}"] = str(list(knn_vectors))
            index += 1

        result = {}
        for key, value_list in knn_dic.items():
            # 替换值
            new_value_list = [input_dic[value] for value in value_list]
            # 存入结果字典
            result[input_dic[key]] = new_value_list
        abb_dict_cn = {}
        messages = []

        system_prompts = '''\# 角色 你是一名标签分类助手，能够准确对给出的标签进行分类并进行组划分。 ## 技能 ### 技能1： 对标签进行分类，技能2，对分类好的标签进行组划分，技能3、检查划分好的标签组，判断是否合理，如果不合理则对每个标签组按照标签的语义进行二次分组，并保证每个标签只被归类到一个相关的主题下，每个标签组的标签数量不超过5个。技能4，将最后结果进行整理，重新编排好数据返回。\\
         ## 限制: - 1、返回格式严格按照[{"A001":[标签1,标签2,..]},{"A002"[标签3,标签4,...]}]格式给出,A001\A002代表标签组划分，标签1\标签2代表相似标签。2、如未明确匹配标签对应的分类结果，请给出与标签近似或关联的标签分类结果。如果存在多个近似或关联标签，严格按照[{"A001":[标签1,标签2,..]},{"A002",[标签3,标签4,..]}]格式给出。3、多次分组的结果也要严格按照[{"A001":[标签1,标签2,..]},{"A002",[标签3,标签4,..]}]格式给出。'''
        question = f'现在给出标签相似字典，字典的键代表标签A，值代表与该标签A最相似的两个标签，请你参考字典提供的相似信息对所有标签进行分类：{result}'
        messages.append({"role": "system", "content": system_prompts})
        messages.append({"role": "user", "content": question})
        if result:
            # 调用模型生成全称
            model = "qwen2.5-72B"
            llm_service = Llm_Service(model)
            answer1 = await llm_service.answer_question(messages, model, max_tokens=4096)
            if 'deepseek' in model.lower():
                # 使用正则表达式匹配</think>标签后的所有内容
                match = re.search(r'</think>\s*(.*)', answer1, re.DOTALL)
                if match:
                    answer = match.group(1).strip()
                    # 移除json、换行符和代码块符号
                    answer = re.sub(r'json|```|\n|\|', '', answer)
                else:
                    raise ValueError("未找到</think>标签")
            else:
                answer = re.sub(r'json|```|\n|\|', '', answer1)
            abb_dict_cn["标签分类结果"] = answer

        # 记录返回日志
        return SuccessResponse(data=abb_dict_cn)


    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
