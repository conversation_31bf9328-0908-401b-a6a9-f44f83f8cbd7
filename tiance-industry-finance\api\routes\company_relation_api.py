#!/usr/bin/env python
# -*- encoding: utf-8 -*-
"""
@File         :company_relation_api.py
@Description  :该API用于从招股说明书中提取客户，供应商，同行业公司信息。
<AUTHOR>
@Date         :2024/12/25 10:01:57
"""

from fastapi import APIRouter, HTTPException
from utils.text_embed_util import TextEmbedService
from utils.milvus_util import MilvusUtil
from utils.mongodb_util import MongodbUtil
import re
from service.company_relation_service import CompanyAnalysis
from entity.company_relation_entity import ComInfoEntity,ComInfoEntityWithFile,SuccessResponse,FalseResponse
import uuid
from configs.collection_config import CollectionConfig
from service.filter_company_rules_service import common_clean_process

router = APIRouter()
def special_clean_process(company_dict:dict) -> dict:
    for year,companies in company_dict.items():
        if(year == "is_keyword_hit"):
            continue
        # print(year,companies)
        companies = common_clean_process(companies,is_filter=False)
        company_dict[year] = companies
        # count = count + len(companies)
    return company_dict

def common_ext(company_name,pdf_filename,industry_chain):
        # 开始存储数据
        MongodbUtil.connect()
        result = MongodbUtil.query_docs_by_condition(CollectionConfig.COMPANY_RELATION_HISTORY,{"company_name":company_name})
        result = list(result)
        if len(result) > 0:
            print("has data")
            response = SuccessResponse(data=result[0])
            return response
        # 暂时实例化一些类
        milvus_util = MilvusUtil()
        embutil = TextEmbedService()
        com_analysis = CompanyAnalysis(milvus_util,embutil)
    # 供应商分析情况 字典
        supplier_dict = com_analysis.company_supplier(company_name,pdf_filename)
        supplier_dict = special_clean_process(supplier_dict)
        # # 同行业分析情况 字典
        same_industry_list = com_analysis.company_same_industry(company_name,pdf_filename)
        same_industry_list = special_clean_process(same_industry_list)
        # # 客户分析情况 字典
        client_dict = com_analysis.company_client(company_name,pdf_filename)
        client_dict = special_clean_process(client_dict)

        
        # 开始存储数据
        MongodbUtil.connect()
        search_condition = {'source_type': {'$regex': "招股说明书"},'title': {"$regex": ".*" + re.escape(pdf_filename) + ".*", "$options": "i"}}
       
        id_list = []
        doc_list = MongodbUtil.query_docs_by_condition(collection_name='source_notice', search_condition=search_condition)
        for item in doc_list: 
            id_list.append(item["_id"])
        # print(pdf_filename)
        # print(search_condition)
        doc_info = MongodbUtil.query_doc_by_id(collection_name='source_notice', doc_id=id_list[0])

        # 公司名称
        company_name = company_name
        
        # 招股书url
        print("ok")
        file_url = doc_info['source_url']
        print(file_url)
        # 招股书文件发布时间
        document_path = doc_info['document_path']
        parts = document_path.split('/')
        file_time_list = parts[2:5]
        ful_file_time_list = []
        for i in file_time_list:
            if len(i) ==1:
                ful_i = "0" + i
                ful_file_time_list.append(ful_i)
            else:
                ful_file_time_list.append(i)
        file_time = '-'.join(ful_file_time_list)
        
        # 文件mongodbid
        source_id = id_list[0]


        data_list = []
        
        is_hit = supplier_dict["is_keyword_hit"]
        # 供应商
        for year, value_list in supplier_dict.items():
            # 判断是否有关键词命中
            if(year == 'is_keyword_hit'):
                is_hit = True
                continue
            for i,value in enumerate(value_list):
            # for i,supplier in enumerate(supplier_dict):
                result_dict = {
                            "industry_chain":industry_chain,
                            "links":"",
                            "company_name":company_name,
                            "released_time":file_time,
                            "source_id":source_id,
                            "affiliate":value,
                            "relation_type":"供应商",
                            "affiliate_register_address":"",
                            "affiliate_business_address":"",
                            "year":year,
                            "ranking":i+1,
                            "product":"",
                            "notes":"",
                            "is_keyword_hit" :is_hit
                           }
                data_list.append(result_dict)
            
        # 同行业
        # same_industry_list,is_hit = com_analysis.company_same_industry(company_name)
        is_hit = same_industry_list["is_keyword_hit"]
        for i, company in enumerate(same_industry_list["same_industry_list"]):
            result_dict = { 
                            "industry_chain":industry_chain,
                            "links":"",
                            "company_name":company_name,
                            "released_time":file_time,
                            "source_id":source_id,
                            "affiliate":company,
                            "relation_type":"同行业",
                            "affiliate_register_address":"",
                            "affiliate_business_address":"",
                            "year":"",
                            "ranking":"",
                            "product":"",
                            "notes":"",
                            "is_keyword_hit" :is_hit
                           }
            data_list.append(result_dict)
        
        # 客户
        # client_dict，is_hit = com_analysis.company_client(company_name)
        is_hit = client_dict["is_keyword_hit"]
        
        for year, value_list in client_dict.items():
            # 判断是否有关键词命中
            if(year == 'is_keyword_hit'):
                is_hit = True
                continue
            for i,value in enumerate(value_list):
            # for i,supplier in enumerate(supplier_dict):
                result_dict = {
                            "industry_chain":industry_chain,
                            "links":"",
                            "company_name":company_name,
                            "released_time":file_time,
                            "source_id":source_id,
                            "affiliate":value,
                            "relation_type":"客户",
                            "affiliate_register_address":"",
                            "affiliate_business_address":"",
                            "year":year,
                            "ranking":i+1,
                            "product":"",
                            "notes":"",
                            "is_keyword_hit" :is_hit
                           }
                data_list.append(result_dict)
        save_data = {"招股书url":file_url,"公司数量":len(data_list),"供应商汇总":supplier_dict,"source_id":source_id, "同行业公司汇总":same_industry_list,"客户汇总":client_dict,"待入库具体信息": data_list}
        save_data["_id"] = uuid.uuid1().hex
        save_data["company_name"] = company_name
        result = MongodbUtil.query_docs_by_condition(CollectionConfig.COMPANY_RELATION_HISTORY,{"company_name":company_name})
        
        if len(list(result)) > 0:
            print("has data")
            # print(len(list(result)))
        else:
            result = MongodbUtil.insert_one(collection_name=CollectionConfig.COMPANY_RELATION_HISTORY, doc_content=save_data)
        response = SuccessResponse(data=save_data)
        return response

router = APIRouter()
@router.post("/company_relation", summary="招股说明书相关公司信息提取")
async def company_relation(request: ComInfoEntity) -> SuccessResponse:
    """
    招股书关联公司信息提取的接口, 点击 Try it out 进行测试(处理时间约5-10s)
    
    company_name : 表示需要提取的招股书文件关键字,请从"机器人","东方国信","三环集团","晶瑞股份","先导股份"选择,分别代表对应的五份招股书文件
    """
    try:
        # 暂时实例化一些类
        # milvus_util = MilvusUtil()
        # embutil = TextEmbedService()
        # com_analysis = CompanyAnalysis(milvus_util,embutil)
        # 提取输入的数据
        company_name = request.pdf_name
   
      
        
        pdf_name_to_filename = {
            "机器人": "机器人-首次公开发行股票并在创业板上市招股说明书",
            "东方国信":"东方国信-首次公开发行股票并在创业板上市招股说明书",
            "三环集团":"三环集团-首次公开发行股票并在创业板上市招股说明书",
            "晶瑞股份":"晶瑞股份-首次公开发行股票并在创业板上市招股说明书",
            "先导股份":"先导股份-首次公开发行股票并在创业板上市招股说明书"
        }
        pdf_filename = pdf_name_to_filename.get(company_name)
        industry_chain="工业机器人"
        
        if not pdf_filename:
            raise HTTPException(status_code=400, detail="PDF文件名不符合要求")
        
        

        response = common_ext(company_name,pdf_filename,industry_chain)
        return response
    
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        data = {"error": detail}
        response = FalseResponse(data=data)
        return response
@router.post("/company_relation_with_file", summary="招股说明书相关公司信息提取")
async def company_relation_with_file(request: ComInfoEntityWithFile) -> SuccessResponse:
    """
    招股书关联公司信息提取的接口, 点击 Try it out 进行测试(处理时间约5-10s)
    
    company_name : 表示需要提取的招股书文件关键字,请从"机器人","东方国信","三环集团","晶瑞股份","先导股份"选择,分别代表对应的五份招股书文件
    """
    try:

        # 提取输入的数据
        company_name = request.pdf_name

        pdf_filename = request.pdf_filename
        industry_chain = request.industry_chain
        
        
        if not pdf_filename:
            raise HTTPException(status_code=400, detail="PDF文件名不符合要求")
        
        
        response = common_ext(company_name,pdf_filename,industry_chain)
        return response
    
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        data = {"error": detail}
        response = FalseResponse(data=data)
        return response







