#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :research_report_extension.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:55:32
'''

from fastapi import APIRouter
from entity.request_entity import CompanySummaryRequest, SearchRelatedCompaniesRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from service.llm_service import Llm_Service
from configs.model_config import ModelConfig
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from api.routes.search_related_companies import search_related_companies
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from configs.run_config import RunConfig
from configs.prompt_config import PromptConfig
from utils.uuid_util import UuidUtil

router = APIRouter()
@router.post("/company_summary", summary="客群总结")
async def company_summary(request: CompanySummaryRequest) -> SuccessResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        industry = request.industry
        company_type = request.company_type
        prompt = request.prompt
        summary_info = MongodbUtil.coll(CollectionConfig.COMPANY_SUMMARY).find_one({"industry": industry, "company_type": company_type, "prompt": prompt})
        if summary_info:
            LogUtil.info("从数据库中返回数据")
            data = summary_info.get("data")
        else:
            LogUtil.info("1.查询产业链中心客群是否已生成")
            key_result = MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).find_one({"industry": industry})
            if key_result:
                key_companies = key_result.get("key_companies") # 获取中心客群列表
                if request.company_type == "中心客群":
                    companies = key_companies
                elif request.company_type == "关联企业":
                    related_info = await search_related_companies(SearchRelatedCompaniesRequest(key_companies=key_companies))
                    if related_info.code == 200:
                        companies = related_info.data.get("result")
                    else:
                        data = {"summary": ""}
                        # 记录返回日志
                        LogUtil.log_json(describe="客群总结请求返回结果", kwargs=data)
                        return SuccessResponse(data=data)
                else:
                    data = {"summary": ""}
                    # 记录返回日志
                    LogUtil.log_json(describe="客群总结请求返回结果", kwargs=data)
                    return SuccessResponse(data=data)

            companies = [item["abb"] for item in (companies if len(companies) < 10 else companies[:10])]

            LogUtil.info("2.组装提示词")
            messages = []
            system_prompt = request.prompt
            if system_prompt == "":
                system_prompt = PromptConfig.COMPANY_SUMMARY_SYSTEM_PROMPT
            messages.append(SystemMessage(system_prompt))
            user_prompt = f"产业链名称：{industry}\n\n企业名单：{'，'.join(companies)}"
            LogUtil.info("用户提示词：" + user_prompt)
            messages.append(UserMessage(user_prompt))
            messages = MessageConverter.convert_messages(messages)

            LogUtil.info("3.调用大模型生成回答")
            model = RunConfig.MAX_LLM_MODEL_NAME
            llm_service = Llm_Service(model)
            answer = await llm_service.answer_question(messages, ModelConfig.MAX_LLM_MODEL_NAME, max_tokens=8192)
            data = {"summary": answer}
            
            # 入库
            if RunConfig.IS_SAVE_DATABASE:
                params = dict(request)
                save_data = CompanySummaryRequest(**params)
                save_data = dict(save_data)
                save_data["_id"] = UuidUtil.get_uuid()
                save_data["data"] = data
                MongodbUtil.insert_one(collection_name=CollectionConfig.COMPANY_SUMMARY, doc_content=save_data)

        # 记录返回日志
        LogUtil.log_json(describe="客群总结请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
