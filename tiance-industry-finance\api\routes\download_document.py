#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :download_document.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 18:06:48
'''
import os
import time
import uuid

import requests
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil

LOCAL_PDF_SAVE_PATH = "/tmp/local_pdf_file"
UPLOAD_PDF_ROOT_PATH = "/tmp/pdf_temp_save_dir"
MINIO_END_POINT = "***********:9000"
MINIO_ACCESS_KEY = "minioadmin"
MINIO_SECRET_KEY = "minioadmin"
MINIO_BUCKET_NAME = "temp-file-bucket"

MONGODB_HOST = "***********"
MONGODB_PORT = 27017
MONGODB_USERNAME = "tansun-finance"
MONGODB_PASSWORD = "<EMAIL>"
MONGODB_DATABASE = "tiance_industry_finance"
MONGODB_AUTH_MECHANISM = "SCRAM-SHA-256"
MONGODB_TEMP_PDF_INFO_COLLECTION = "temp_file_coll"
MONGODB_COMPANY_INFO_COLL = "company_info_coll"

router = APIRouter()


# file_url: str = Body(..., examples=["http://test.com/test.pdf"], description="文件链接"),
# file_name: str = Body(..., examples=["test.pdf"], description="文件名")
class DownloadDocumentRequestBody(BaseModel):
    file_url: str
    file_name: str


class DownloadDocumentResponseBody(BaseModel):
    code: int
    message: str


def download_pdf(url, filename, max_retries=3):
    file_path = os.path.join(LOCAL_PDF_SAVE_PATH, filename)
    attempts = 0

    while attempts < max_retries:
        try:
            response = requests.get(url)
            response.raise_for_status()

            with open(file_path, 'wb') as file:
                file.write(response.content)

            print(f"File downloaded successfully and saved as {file_path}")
            return file_path
        except requests.exceptions.RequestException as e:
            attempts += 1
            print(f"Attempt {attempts} failed: {e}")
            if attempts < max_retries:
                time.sleep(2)  # Wait before retrying

    print("Failed to download the file after multiple attempts.")
    return None


def upload_pdf(pdf_path, doc_id):
    try:
        file_name = doc_id + ".pdf"
        r_path = "{}/{}".format(UPLOAD_PDF_ROOT_PATH, file_name)
        print("r_path", r_path)
        print("pdf_path", pdf_path)

        MinIoUtil.upload_file(MINIO_BUCKET_NAME, r_path, pdf_path)
        return r_path

    except (Exception, RuntimeError) as e:
        print("Failed to upload file to minio.")
        print(e)
        return None


def save_data(doc_id, mongo_collection, file_name, file_url, minio_remote_path):
    try:
        doc_json = {
            "_id": doc_id,
            "source_name": "",
            "source_code": "",
            "source_type": "",
            "org_name": "",
            "industry_name": "",
            "title": file_name,
            "source_url": file_url,
            "publish_date": "",
            "crawling_time": time.time(),
            "document_path": minio_remote_path,
            "bucket_name": mongo_collection
        }
        # 存储mongodb
        MongodbUtil.insert_one(MONGODB_TEMP_PDF_INFO_COLLECTION, doc_json)
        return True
    except (Exception, RuntimeError) as e:
        print("Failed to save file info to mongodb.")
        print(e)
        return False


@router.post("/download_document", summary="文档下载")
async def download_document(body: DownloadDocumentRequestBody) -> DownloadDocumentResponseBody:
    file_url = body.file_url
    file_name = body.file_name

    doc_id = uuid.uuid1().hex

    file_path = download_pdf(file_url, file_name)
    if not file_path:
        raise HTTPException(status_code=500, detail="File download failed after 3 retries.")
    minio_remote_path = upload_pdf(file_path, str(doc_id))
    if not minio_remote_path:
        raise HTTPException(status_code=500, detail="Upload file content to file bucket failed.")
    if not save_data(doc_id, MONGODB_TEMP_PDF_INFO_COLLECTION, file_name, file_url, minio_remote_path):
        raise HTTPException(status_code=500, detail="Save file info to database failed.")
    return DownloadDocumentResponseBody(code=200, message="success")
