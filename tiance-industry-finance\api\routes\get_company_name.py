
# !/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/6/12 下午6:16
# <AUTHOR> <PERSON><PERSON>yuanYi
# @Email   : <EMAIL>
# @File    : get_company_name.py
# @Project : tiance-industry-finance
"""
from entity.request_entity import GetCompnayName
from service.get_company_name import CompanyExtractor, check_parsed_status, _extract_from_text
import json
from fastapi import APIRouter
from entity.request_entity import ExtensionRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
import asyncio
import os

router = APIRouter()


@router.post("/extract-companies", response_model=SuccessResponse)
async def extract_companies(request: GetCompnayName) -> SuccessResponse:
    try:
        # 初始化日志
        LogUtil.init(process_name='company_extractor_api')

        # 创建提取器实例
        extractor = CompanyExtractor(
            mongodb_collection=request.mongodb_collection_name,
            mongodb_id=request.mongodb_id
        )

        # 步骤1: 获取MongoDB文档
        doc = extractor.get_mongodb_document()
        LogUtil.info(f"成功获取MongoDB文档: {request.mongodb_id}")

        # 步骤2: 检查解析状态
        is_parsed, milvus_collection, file_title = check_parsed_status(doc)
        if not is_parsed:
            LogUtil.info("文档未解析，无法获取Milvus文本块")
        LogUtil.info(f"Milvus集合名称: {milvus_collection}")

        # 步骤3: 获取Milvus文本内容
        text_content = extractor.get_milvus_chunks(milvus_collection)
        text_chunks = text_content.splitlines()
        LogUtil.info(f"获取到 {len(text_chunks)} 个文本块，总长度: {len(text_content)}字符")

        # 步骤4: 提取公司名称
        LogUtil.info("开始使用大模型提取公司名称...")
        result = await extractor.extract_companies(text_content)
        companies = result.get("companies", [])

        # 如果大模型返回空结果，尝试使用正则提取
        if not companies:
            LogUtil.warn("大模型未提取到公司名称，尝试使用正则表达式提取")
            companies = _extract_from_text(text_content)

        LogUtil.info(f"成功提取到 {len(companies)} 个公司名称")

        # 步骤5: 保存结果
        output_dir = "./file/company_name_json/"
        os.makedirs(output_dir, exist_ok=True)

        # 生成文件名
        filename = os.path.join(output_dir, f"{request.mongodb_id}_{file_title}.json")
        extractor.save_to_json({"companies": companies}, filename)
        LogUtil.info(f"提取完成! 结果保存至: {filename}")
        LogUtil.info(f"提取的公司列表: {companies}")

        # 返回成功响应
        return SuccessResponse(
            data={
                "filename": filename,
                "company_count": len(companies),
                "companies": companies,
                "milvus_collection": milvus_collection,
                "text_length": len(text_content)
            }
        )

    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
