#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :get_fullname_companies.py
@Description  :
<AUTHOR>
@Date         :2025/03/02 09:28:20
'''

import re
from fastapi import APIRouter
from entity.request_entity import GetFullnameCompaniesRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.cache_utils import CacheUtils
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig

router = APIRouter()


@router.post("/get_fullname_companies", summary="获取中心客群全称")
async def get_fullname_companies(
    request: GetFullnameCompaniesRequest,
    is_use_cache: bool = False  # TODO 对request中的node_companies排序，不然顺序乱了就匹配不上
) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request, is_use_cache=is_use_cache))

        # 判断是否启用缓存
        if is_use_cache:
            # 启用缓存
            cached_data = CacheUtils.get_cache(
                CollectionConfig.CACHE_GET_FULLNAME_COMPANIES,
                key=request.model_dump_json()
            )
            if cached_data is None:
                is_use_cache = False
                LogUtil.info("未命中缓存，将逐个公司查询")
            else:
                LogUtil.info("命中缓存，立即返回结果")
                data = cached_data
        if not is_use_cache:  # 再次判断
            abb_dict = {}  # 简称映射全称
            abbs = request.node_companies.get(request.industry)  # 获取根节点企业简称列表，根节点存储了所有简称列表
            listed_companies = []  # 上市企业列表
            # TODO 等待建立标签库
            special_companies = []  # 专精特新企业列表
            high_tech_companies = []  # 高新技术企业列表
            for abb in abbs:
                company_basic = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one(
                    {"$or": [{"公司简称": abb}, {"公司名称": abb}, {"曾用名": {"$regex": re.escape(abb)}}]})
                if company_basic:
                    abb_dict[abb] = company_basic.get("公司名称")
                    # 上市企业
                    if company_basic.get("是否上市") == "是":
                        listed_companies.append(abb_dict[abb])
                else:
                    company_brand = MongodbUtil.coll(CollectionConfig.BRAND_INFO).find_one(
                        {"$or": [{"品牌/产品名称": abb}, {"公司全称": abb}]})
                    if company_brand:
                        abb_dict[abb] = company_brand.get("公司全称")

            new_node_companies = {}
            filter_companies = []  # 未检索到全称的企业过滤
            for node, abbs in request.node_companies.items():
                new_node_companies[node] = []
                for abb in abbs:
                    if abb in abb_dict.keys():
                        company_info = {}
                        company_info["abb"] = abb
                        fullname = abb_dict[abb]
                        company_info["name"] = fullname
                        company_info["is_listed"] = "是" if fullname in listed_companies else "否"
                        company_info["is_special"] = "是" if fullname in special_companies else "否"
                        company_info["is_high_tech"] = "是" if fullname in high_tech_companies else "否"
                        basic_info = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one({"公司名称": fullname})
                        company_info["stock_code"] = "-"
                        company_info["province"] = "-"
                        company_info["city"] = "-"
                        if basic_info:
                            company_info["stock_code"] = basic_info.get("股票代码") if isinstance(
                                basic_info.get("股票代码"), str) else "-"
                            company_info["province"] = basic_info.get("所属省") if isinstance(basic_info.get("所属省"),
                                                                                              str) else "-"
                            company_info["city"] = basic_info.get("所属市") if isinstance(basic_info.get("所属市"),
                                                                                          str) else "-"
                        industry_position_info = MongodbUtil.coll(CollectionConfig.INDUSTRY_POSITION_INFO).find_one(
                            {"企业名称": fullname})
                        company_info["industry_position"] = ""
                        if industry_position_info:
                            company_info["industry_position"] = industry_position_info.get("产业地位")
                        new_node_companies[node].append(company_info)
                    else:
                        if abb not in filter_companies:
                            filter_companies.append(abb)

            abb_list = []
            all_key_companies = []
            for node, companies in new_node_companies.items():
                if len(node.split("|")) > 2:  # 产业链和上中下游不查询相关企业
                    for company in companies:
                        if company["abb"] not in abb_list:
                            abb_list.append(company["abb"])
                            company["stream_type"] = node.split("|")[1]
                            all_key_companies.append(company)

            data = {"all_key_companies": all_key_companies, "filter_companies": filter_companies,
                    "result": new_node_companies}

            # 缓存
            CacheUtils.save_cache(
                CollectionConfig.CACHE_GET_FULLNAME_COMPANIES,
                key=request.model_dump_json(),
                value=data
            )

        # 记录返回日志
        LogUtil.log_json(describe="获取中心客群全称请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
