#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :get_key_companies.py
@Description  :
<AUTHOR>
@Date         :2025/03/05 17:20:08
'''

from fastapi import APIRouter
from entity.request_entity import (
    GetKeyCompaniesRequest,
    SearchKeyCompaniesComposeRequest,
    GetFullnameCompaniesRequest)
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from api.routes.search_key_companies_compose import search_key_companies_compose
from api.routes.get_fullname_companies import get_fullname_companies
from configs.run_config import RunConfig
from configs.collection_config import CollectionConfig
from utils.uuid_util import UuidUtil
from utils.mongodb_util import MongodbUtil

router = APIRouter()
@router.post("/get_key_companies", summary="获取中心客群")
async def get_key_companies(request: GetKeyCompaniesRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        model = RunConfig.MAX_LLM_MODEL_NAME
        industry = request.industry
        LogUtil.info("1.查询产业链中心客群是否已生成")
        key_result = MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).find_one({"industry": industry})
        if not key_result:
            LogUtil.info("2.查询产业链图谱是否已生成")
            chain_result = MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).find_one({"industry": industry})
            if chain_result:
                LogUtil.info("3.获取中心客群名单")
                mongodb_ids = chain_result.get("mongodb_ids")
                chain_structure = chain_result.get("chain_structure")
                node_info = await search_key_companies_compose(SearchKeyCompaniesComposeRequest(
                    model=model,
                    k=5,
                    mongodb_ids=mongodb_ids,
                    industry=industry,
                    chain_structure=chain_structure,
                    epochs=1
                ))

                if node_info.code == 200:
                    node_companies = node_info.data.get("result")

                    # 溯源
                    mongdb_id_companies = node_info.data.get("mongdb_id_companies")

                    LogUtil.info("4.获取中心客群全称")
                    key_info = await get_fullname_companies(GetFullnameCompaniesRequest(
                        industry=industry,
                        node_companies=node_companies
                    ))

                    if key_info.code == 200:
                        LogUtil.info("5.中心客群入库")
                        new_node_companies = key_info.data.get("result") # 有产业链结构的中心客群
                        key_companies = key_info.data.get("all_key_companies") # 无产业链结构的中心客群列表
                        filter_companies = key_info.data.get("filter_companies") # 被过滤的中心客群
                        save_data = {"_id": UuidUtil.get_uuid(), "industry": industry, "node_companies": new_node_companies,
                                     "key_companies": key_companies, "filter_companies": filter_companies, "mongdb_id_companies": mongdb_id_companies}
                        MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).insert_one(save_data)
        
        data = {"result": "获取中心客群成功"}
        # 记录返回日志
        LogUtil.log_json(describe="获取中心客群请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
