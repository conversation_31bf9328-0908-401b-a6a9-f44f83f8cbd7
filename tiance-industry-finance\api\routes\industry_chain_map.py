#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :industry_chain_map.py
@Description  :
<AUTHOR>
@Date         :2025/03/05 13:45:17
'''

from fastapi import APIRouter
from entity.request_entity import IndustryChainMapRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig

router = APIRouter()
@router.post("/industry_chain_map", summary="产业链图谱生成")
async def industry_chain_map(request: IndustryChainMapRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        stream_type = request.stream_type
        node_list = request.node_name_info.split("|")
        industry = node_list[0]
        LogUtil.info("1.查询是否已在产业链库中")
        result = MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).find_one({"industry": industry})
        if result:
            chain_structure = result.get("chain_structure")
            node_list.insert(1, stream_type) # 在列表的位置1插入上中下游类型
            
            LogUtil.info("2.获取到当前节点")
            current_node = chain_structure
            for node in node_list:
                current_node = current_node[node]
            
            child_list = []
            for child in current_node.keys():
                child_list.append({"child_name": child, "stream_type": stream_type})
            
            data = {"child_list": child_list}
            # 记录返回日志
            LogUtil.log_json(describe="获取中心客群请求返回结果", kwargs=data)
            return SuccessResponse(data=data)
        else:
            detail = f"失败详情：该产业链图谱未在库中"
            LogUtil.error(msg=detail)
            data = {"error": detail}
            return FalseResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
