#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :industry_evaluation.py
@Description  :
<AUTHOR>
@Date         :2025/03/06 09:26:01
'''

from fastapi import APIRouter
from entity.request_entity import IndustryEvaluationRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from service.kb_service import KbService
from service.llm_service import Llm_Service
from service.rerank_service import Rerank_Service
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from configs.run_config import RunConfig
from utils.uuid_util import UuidUtil

router = APIRouter()
@router.post("/industry_evaluation", summary="产业评价")
async def industry_evaluation(request: IndustryEvaluationRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        industry = request.industry
        prompt = request.prompt
        evaluation_info = MongodbUtil.coll(CollectionConfig.INDUSTRY_EVALUATION).find_one({"industry": industry, "prompt": prompt})
        if evaluation_info:
            LogUtil.info("从数据库中返回数据")
            data = evaluation_info.get("data")
        else:
            LogUtil.info("根据产业链名称检索得到mongodb_ids")
            mongodb_ids = []
            chain_result = MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).find_one({"industry": industry})
            if chain_result:
                mongodb_ids = chain_result.get("mongodb_ids")
            LogUtil.info("1.根据mongodb_id检索得到向量数据库信息(包括向量数据库名,向量文本块字段名,标题)")
            research_report_list = MongodbUtil.coll(CollectionConfig.RESEARCH_REPORT_LABEL_INFO).find({"_id": {"$in": mongodb_ids}})
            title_list = []
            milvus_collection_name = ""
            milvus_field_name = ""
            for report in research_report_list:
                title_list.append(report.get("title"))
                milvus_collection_name = report.get("milvus_collection_name") # milvus集合名
                milvus_field_name = report.get("milvus_field_name")
            k = 5

            rerank_content = []
            if milvus_collection_name:
                LogUtil.info("2.检索向量数据库")
                kb_service = KbService()
                expr = f"file_title in {title_list}"
                # 构造检索语句
                retrieval_sentence = request.industry + "的概况"
                doc_list = await kb_service.search_knowledge_by_question(collection_name=milvus_collection_name, question=retrieval_sentence,
                                                                        limit_top_k=k, expr=expr)
                
                LogUtil.info("3.拼接检索内容")
                content_list = []
                for item in doc_list:
                    if item[milvus_field_name] not in content_list:
                        content_list.append(item[milvus_field_name])
                
                if content_list:
                    rerank_service = Rerank_Service()
                    rerank_result = await rerank_service.rerank(retrieval_sentence, content_list)
                    for item in rerank_result["results"]:
                        if item["relevance_score"] >= 0.5:
                            rerank_content.append(content_list[item["index"]])

            LogUtil.info("4.组装提示词")
            messages = []
            prompt = request.prompt
            messages.append(SystemMessage(prompt))
            if rerank_content:
                user_prompt = "\n\n\n\n".join(rerank_content) + "\n\n"
            else:
                user_prompt = "请给出" + request.industry + "的概况"
            messages.append(UserMessage(user_prompt))
            messages = MessageConverter.convert_messages(messages)

            LogUtil.info("5.调用大模型生成回答")
            model = RunConfig.MAX_LLM_MODEL_NAME
            llm_service = Llm_Service(model)
            answer = await llm_service.answer_question(messages, model, max_tokens=20480)   
            data = {"evaluation": answer}

            # 入库
            if RunConfig.IS_SAVE_DATABASE:
                params = dict(request)
                save_data = IndustryEvaluationRequest(**params)
                save_data = dict(save_data)
                save_data["_id"] = UuidUtil.get_uuid()
                save_data["data"] = data
                MongodbUtil.insert_one(collection_name=CollectionConfig.INDUSTRY_EVALUATION, doc_content=save_data)

        # 记录返回日志
        LogUtil.log_json(describe="产业评价请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
