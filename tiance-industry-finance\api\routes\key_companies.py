#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :research_report_extension.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:55:32
'''

from fastapi import APIRouter
from entity.request_entity import KeyCompaniesRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.time_util import TimeUtil
from configs.collection_config import CollectionConfig

router = APIRouter()
@router.post("/key_companies", summary="中心客群详情")
async def key_companies(request: KeyCompaniesRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        industry = request.industry
        LogUtil.info("1.查询产业链中心客群是否已生成")
        key_result = MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).find_one({"industry": industry})
        update_time = TimeUtil.get_current_format_time()
        company_list = []
        if key_result:
            key_companies = key_result.get("key_companies") # 获取中心客群列表
            # 溯源
            mongdb_id_companies = key_result.get("mongdb_id_companies")
            for company_info in key_companies:
                company_info["credit_code"] = ""
                company_info["registered_capital"] = ""
                company_info["main_products"] = ""
                company_info["company_web"] = ""
                company_info["company_attributes"] = ""
                company_info["company_profile"] = ""
                company_info["business_scope"] = ""
                company_info["registered_address"] = ""
                # TODO 等待获取
                company_info["is_special"] = ""
                company_info["is_high_tech"] = ""
                company_info["is_sub_special"] = ""
                company_info["update_time"] = update_time
                if company_info["is_listed"] == "是":
                    listed_companies = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one({"公司中文名称": company_info["name"]})
                    if listed_companies:
                        company_info["credit_code"] = listed_companies["统一社会信用代码"]
                        company_info["registered_capital"] = listed_companies["注册资本\n单位：万元"]
                        company_info["main_products"] = "" # TODO 等待获取
                        company_info["company_web"] = listed_companies["企业网址"]
                        company_info["company_attributes"] = "" # TODO 等待获取
                        company_info["company_profile"] = "" # TODO 等待获取
                        company_info["business_scope"] = listed_companies["经营范围"]
                        company_info["registered_address"] = listed_companies["注册地址"]
                
                # 溯源
                company_info["source_list"] = []
                for _id, abbs in mongdb_id_companies.items():
                    if company_info["abb"] in abbs:
                        report_info = MongodbUtil.coll(CollectionConfig.RESEARCH_REPORT_LABEL_INFO).find_one({"_id": _id})
                        source_info = {"source_title": report_info["title"], "source_name": report_info["file_source"], "source_type": "研报", "source_url": report_info["source_url"]}
                        company_info["source_list"].append(source_info)
                
                company_list.append(company_info) 
        
        data = {"company_list": company_list}
        # 记录返回日志
        LogUtil.log_json(describe="中心客群详情请求返回结果", kwargs=data)  
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
