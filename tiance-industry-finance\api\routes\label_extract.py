#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/4/14 10:43
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : label_extract.py
# @Project : tiance-industry-finance
"""

import asyncio
import json
import pickle
import re
from typing import List

from fastapi import APIRouter

from api.routes.get_key_companies import get_key_companies
from entity.request_entity import SearchKeyCompaniesComposeRequest, LabelExtractRequest, GetKeyCompaniesRequest
from entity.request_example import CHAIN_STRUCTURE
from entity.response_entity import SuccessResponse, FalseResponse
from service.label_to_perform_adapter import Label2PerformAdapter
from service.label_extract_service import *
from utils.log_util import LogUtil
from api.routes.search_key_companies import search_key_companies
from entity.request_entity import SearchKeyCompaniesRequest
from configs.prompt_config import PromptConfig
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from utils.tree_utils import TreeUtils
from utils.text_utils import TextUtil
import ast

from utils.uuid_util import UuidUtil

router = APIRouter()


@router.post("/label_extract_perform", summary="基于文档解析的结果进行“产业标签/公司标签”等抽取展示")
async def label_extract_perform(request: LabelExtractRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))

        LogUtil.info("正在从用户提示词中获取产业链名称列表")
        industry_list = extract_industries_from_prompt(request.system_prompt)
        if industry_list is None:
            return FalseResponse(data={
                "error": '未能从提示词中解析出产业链列表，请注意格式。提示词示例：你是一个专业的$["工业机器人","新能源汽车","新材料","低空经济"]$产业链抽取助手...'
            })

        # LogUtil.info("正在验证产业链名称列表")
        # if valid_industries(industry_list) is False:
        #     return FalseResponse(data={"error": f"产业链名称列表验证失败。industry_list: {industry_list}"})

        # TODO用户提示词除了获取产业链名称列表外暂时别无他用，不会被用作大模型提示词

        LogUtil.info("正在将doc_id_and_type中的doc_type的值替换为MongoDB中对应表名")
        collection_names_and_ids_dict = convert_docIdAndType_to_collectionNameAndIds(request.doc_id_and_type)

        # TODO debug
        LogUtil.info("正在遍历产业链列表，并抽取数据")
        raw_data_multi_industries = []
        for industry in industry_list:
            LogUtil.info(f"正在抽取产业链标签，industry: {industry}")
            raw_data_single_industry = await asyncio.gather(extract_raw_label_data_single_industry(
                industry=industry,
                collection_names=collection_names_and_ids_dict["collection_names"],
                mongodb_ids=collection_names_and_ids_dict["mongodb_ids"],
                model=request.model,
                k=request.k,
                is_use_cache=request.is_cached
            ))
            raw_data_single_industry = raw_data_single_industry[0]
            if raw_data_single_industry:
                raw_data_multi_industries.append(raw_data_single_industry)
        LogUtil.info(f"多行业数据提取成功。raw_data_multi_industries: {raw_data_multi_industries}")

        # with open('raw_data_multi_industries.pickle', 'wb') as f:
        #     pickle.dump(raw_data_multi_industries, f)
        #
        # # TODO debug
        # with open('raw_data_multi_industries.pickle', 'rb') as f:
        #     raw_data_multi_industries = pickle.load(f)

        # raw_data_multi_industries 中多条产业链合并
        merged_multi_industries_raw_data = merge_multi_industries_raw_data(raw_data_multi_industries)

        # 根据文档mongo_id获取文档摘要信息
        mongodb_id_info = get_mongodb_id_info(collection_names_and_ids_dict)
        merged_multi_industries_raw_data["mongodb_id_info"] = mongodb_id_info

        # 整理不同文档mongo_id下的产业链环节
        mongodb_id_node = get_mongodb_id_node(merged_multi_industries_raw_data)
        merged_multi_industries_raw_data["mongodb_id_node"] = mongodb_id_node

        # 获取不同产业链环节关联的产品，及该关联的信息来源依据
        node_products = get_node_products(merged_multi_industries_raw_data)
        merged_multi_industries_raw_data["node_products"] = node_products

        #  根据 'key_companies' 字段中的那些公司，
        #  去 “发票、海关、授信报告中公司名对应的产品” MongoDB表中查询出对应的产品，
        products = get_products(merged_multi_industries_raw_data)
        merged_multi_industries_raw_data["products"] = products

        # merged_multi_industries_raw_data 存一份到数据库
        merged_multi_industries_raw_data["_id"] = UuidUtil.get_uuid()
        MongodbUtil.insert_one(CollectionConfig.LABEL_EXTRACT, merged_multi_industries_raw_data)

        # 将原始数据转换为前端展示的数据视图
        perform_data = label_rawData_to_performData(merged_multi_industries_raw_data)
        # perform_data 存一份到数据库
        perform_data["_id"] = UuidUtil.get_uuid()
        MongodbUtil.insert_one(CollectionConfig.LABEL_EXTRACT_PERFORM, perform_data)

        LogUtil.info(f"标签提取接口数据提取成功，正在返回数据给前端。perform_data: {perform_data}")

        return SuccessResponse(data=perform_data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)


if __name__ == '__main__':
    LogUtil.init('Test label_extract.py')
    MongodbUtil.connect()
    MinIoUtil.connect()

    # request_res = asyncio.run(label_extract_perform(
    #     LabelExtractRequest(
    #         model="qwen2.5-72B",
    #         k=5,
    #         doc_id_and_type=[
    #             {"doc_type": "research_report", "mongodb_id": "3ad4695c6cdc44e9ade70bf84d9c5049"},
    #             {"doc_type": "research_report", "mongodb_id": "2e25bd437c0d4cfb9a1b61d15a76868b"},
    #             {"doc_type": "research_report", "mongodb_id": "f4d1f9dcb6ca49b9882a5ff3d7b6e75a"},
    #             {"doc_type": "research_report", "mongodb_id": "bbfab0b018334212b284930737cd8e3d"},
    #             {"doc_type": "research_report", "mongodb_id": "98d353b0ccca4a0b97a0310f688e0876"},
    #             {"doc_type": "invoice", "mongodb_id": "b9396aac89a74c84878d40f27b8be245"},
    #             {"doc_type": "invoice", "mongodb_id": "6847a1f20e5f4a6b966ff9d1d91c0000"},
    #             {"doc_type": "invoice", "mongodb_id": "ff546123621d4528a9c460e99a56f8ca"},
    #             {"doc_type": "customs", "mongodb_id": "68c57da82f084a8e823da804df4e7638"},
    #             {"doc_type": "customs", "mongodb_id": "422001e159c7463a89cba8f082d0a836"},
    #
    #         ],
    #         system_prompt="""你是一个专业的$["工业机器人","新材料"]$产业链抽取助手..."""
    #     )
    # ))
    s_type = "低空经济"
    request_res = asyncio.run(label_extract_perform(
        LabelExtractRequest(
            model="DeepSeek-R1-Distill-Qwen-32B",
            k=5,
            is_cached=False,
            doc_id_and_type=[
                {"doc_type": "research_report", "mongodb_id": "fb344c3ee7134dafbf4cbc23e23ee404"},
                # {"doc_type": "research_report", "mongodb_id": "eb236fe307b54eb6b380c3b9158a53c8"},
                # {"doc_type": "research_report", "mongodb_id": "2f76821f2abd4390a65d9597778e5967"},
                # {"doc_type": "research_report", "mongodb_id": "352836eaa9cd4ca19af00e6b74361634"},
                # {"doc_type": "research_report", "mongodb_id": "4be493ebdc5d4c18bd09f79f9f0fb361"},
                # {"doc_type": "research_report", "mongodb_id": "ea80d887412443ff9b8be4f9e1c1746d"},
                # {"doc_type": "research_report", "mongodb_id": "d9d207eb214544a8aad90456b6721f06"},
                # {"doc_type": "research_report", "mongodb_id": "ae20f9070af94799939630517ad1dc82"},
                # {"doc_type": "research_report", "mongodb_id": "76e97997af6e4129b240782fe2e1dedd"},
                # {"doc_type": "research_report", "mongodb_id": "e5d8831395b647cda592a28bbd918f79"},
                #
                # {"doc_type": "research_report", "mongodb_id": "ae20f9070af94799939630517ad1dc82"},
                # {"doc_type": "research_report", "mongodb_id": "76e97997af6e4129b240782fe2e1dedd"},
                # #
                # {"doc_type": "research_report", "mongodb_id": "0378816e84e842f0be51dc900e86ec7a"},
                # #
                # {"doc_type": "research_report", "mongodb_id": "7871aa6f26534c20aa9a430d53278478"},
                # {"doc_type": "research_report", "mongodb_id": "01eba2954b34433e945688bc407c15a1"},
                # {"doc_type": "research_report", "mongodb_id": "e2d22e70a4184321bdb5b4ca990cc44c"},
                # {"doc_type": "research_report", "mongodb_id": "76e97997af6e4129b240782fe2e1dedd"},
            ],
            system_prompt=f"""你是一个专业的$["{s_type}"]$产业链抽取助手...""",
        )
    ))

    # request_res = asyncio.run(label_extract_perform(
    #     LabelExtractRequest(
    #         model="qwen2.5-72B",
    #         k=5,
    #         doc_id_and_type=[
    #             {"doc_type": "research_report", "mongodb_id": "76e97997af6e4129b240782fe2e1dedd"}
    #         ],
    #         system_prompt="""你是一个专业的$["evtol","低空经济"]$产业链抽取助手...""",
    #         is_use_cache=True
    #     )
    # ))

    perform_data = request_res.data
    print(perform_data['_id'])
    # save_perform_data_to_excel(perform_data,s_type)

    # json_res=TreeUtils.restore_tree(perform_data["chain_structure"])
    # print(json_res)

    pass
