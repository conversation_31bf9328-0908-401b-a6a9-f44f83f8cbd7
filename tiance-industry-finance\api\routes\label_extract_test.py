#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/4/17 17:01
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : label_extract_test.py.py
# @Project : tiance-industry-finance
"""

import asyncio
import json
import pickle
import re
from typing import List

from fastapi import APIRouter

from api.routes.get_key_companies import get_key_companies
from entity.request_entity import SearchKeyCompaniesComposeRequest, LabelExtractRequest, GetKeyCompaniesRequest
from entity.request_example import CHAIN_STRUCTURE
from entity.response_entity import SuccessResponse, FalseResponse
from service.label_extract_service import *
from utils.log_util import LogUtil
from api.routes.search_key_companies import search_key_companies
from entity.request_entity import SearchKeyCompaniesRequest
from configs.prompt_config import PromptConfig
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from utils.tree_utils import TreeUtils
from utils.text_utils import TextUtil
import ast

from utils.uuid_util import UuidUtil

router = APIRouter()

test_input_data = LabelExtractRequest(
    model="qwen2.5-72B",
    k=5,
    doc_id_and_type=[
        {"doc_type": "research_report", "mongodb_id": "3ad4695c6cdc44e9ade70bf84d9c5049"},
        {"doc_type": "research_report", "mongodb_id": "2e25bd437c0d4cfb9a1b61d15a76868b"},
        {"doc_type": "research_report", "mongodb_id": "f4d1f9dcb6ca49b9882a5ff3d7b6e75a"},
        {"doc_type": "research_report", "mongodb_id": "bbfab0b018334212b284930737cd8e3d"},
        {"doc_type": "research_report", "mongodb_id": "98d353b0ccca4a0b97a0310f688e0876"},
        {"doc_type": "invoice", "mongodb_id": "b9396aac89a74c84878d40f27b8be245"},
        {"doc_type": "invoice", "mongodb_id": "6847a1f20e5f4a6b966ff9d1d91c0000"},
        {"doc_type": "invoice", "mongodb_id": "ff546123621d4528a9c460e99a56f8ca"},
        {"doc_type": "customs", "mongodb_id": "68c57da82f084a8e823da804df4e7638"},
        {"doc_type": "customs", "mongodb_id": "422001e159c7463a89cba8f082d0a836"},

    ],
    system_prompt="""你是一个专业的$["工业机器人"]$产业链抽取助手..."""
)


@router.post("/label_extract_perform_test", summary="基于文档解析的结果进行“产业标签/公司标签”等抽取展示")
async def label_extract_perform_test(request: LabelExtractRequest) -> SuccessResponse | FalseResponse:
    try:
        request = test_input_data  # TODO debug

        # 记录日志
        LogUtil.log_json(describe="【测试】请求参数", kwargs=dict(request))

        LogUtil.info("【测试】正在从用户提示词中获取产业链名称列表")
        industry_list = extract_industries_from_prompt(request.system_prompt)
        if industry_list is None:
            return FalseResponse(data={
                "error": '未能从提示词中解析出产业链列表，请注意格式。提示词示例：你是一个专业的$["工业机器人","新能源汽车","新材料","低空经济"]$产业链抽取助手...'
            })

        LogUtil.info("【测试】正在验证产业链名称列表")
        if valid_industries(industry_list) is False:
            return FalseResponse(data={"error": f"产业链名称列表验证失败。industry_list: {industry_list}"})

        # TODO用户提示词除了获取产业链名称列表外暂时别无他用，不会被用作大模型提示词

        LogUtil.info("【测试】正在将doc_id_and_type中的doc_type的值替换为MongoDB中对应表名")
        collection_names_and_ids_dict = convert_docIdAndType_to_collectionNameAndIds(request.doc_id_and_type)

        # # TODO debug
        # LogUtil.info("正在遍历产业链列表，并抽取数据")
        # raw_data_multi_industries = []
        # for industry in industry_list:
        #     LogUtil.info(f"正在抽取产业链标签，industry: {industry}")
        #     raw_data_single_industry = await asyncio.gather(extract_raw_label_data_single_industry(
        #         industry=industry,
        #         collection_names=collection_names_and_ids_dict["collection_names"],
        #         mongodb_ids=collection_names_and_ids_dict["mongodb_ids"],
        #         model=request.model,
        #         k=request.k,
        #     ))
        #     raw_data_multi_industries.append(raw_data_single_industry[0])
        #
        # LogUtil.info(f"多行业数据提取成功。raw_data_multi_industries: {raw_data_multi_industries}")

        # with open('raw_data_multi_industries.pickle', 'wb') as f:
        #     pickle.dump(raw_data_multi_industries, f)

        # TODO debug
        with open(
            '/home/<USER>/projects/tiance-industry-finance/api/routes/raw_data_multi_industries__bak3_10doc_fix.pickle',
            'rb') as f:
            raw_data_multi_industries = pickle.load(f)

        # raw_data_multi_industries 中多条产业链合并
        merged_multi_industries_raw_data = merge_multi_industries_raw_data(raw_data_multi_industries)

        # 根据文档mongo_id获取文档摘要信息
        mongodb_id_info = get_mongodb_id_info(collection_names_and_ids_dict)
        merged_multi_industries_raw_data["mongodb_id_info"] = mongodb_id_info

        # 整理不同文档mongo_id下的产业链环节
        mongodb_id_node = get_mongodb_id_node(merged_multi_industries_raw_data)
        merged_multi_industries_raw_data["mongodb_id_node"] = mongodb_id_node

        # 获取不同产业链环节关联的产品，及该关联的信息来源依据
        node_products = get_node_products(merged_multi_industries_raw_data)
        merged_multi_industries_raw_data["node_products"] = node_products

        #  根据 'key_companies' 字段中的那些公司，
        #  去 “发票、海关、授信报告中公司名对应的产品” MongoDB表中查询出对应的产品，
        products = get_products(merged_multi_industries_raw_data)
        merged_multi_industries_raw_data["products"] = products

        # merged_multi_industries_raw_data 存一份到数据库
        merged_multi_industries_raw_data["_id"] = UuidUtil.get_uuid()
        MongodbUtil.insert_one(CollectionConfig.LABEL_EXTRACT, merged_multi_industries_raw_data)

        # 将原始数据转换为前端展示的数据视图
        perform_data = label_rawData_to_performData(merged_multi_industries_raw_data)
        # perform_data 存一份到数据库
        perform_data["_id"] = UuidUtil.get_uuid()
        MongodbUtil.insert_one(CollectionConfig.LABEL_EXTRACT_PERFORM, perform_data)

        LogUtil.info(f"【测试】标签提取接口数据提取成功，正在返回数据给前端。perform_data: {perform_data}")

        return SuccessResponse(data=perform_data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)


if __name__ == '__main__':
    LogUtil.init('Test label_extract_test.py')
    MongodbUtil.connect()
    MinIoUtil.connect()

    asyncio.run(
        label_extract_perform_test(test_input_data)
    )

    # with open('raw_data_multi_industries.pickle', 'rb') as f:
    #     raw_data_multi_industries = pickle.load(f)
    #
    # merged_multi_industries_raw_data = merge_multi_industries_raw_data(raw_data_multi_industries)
    # # mongo_ids_of_company_abb = get_company_abb_to_mongo_ids(merged_multi_industries_raw_data)
    #
    # mongodb_id_node = get_mongodb_id_node(merged_multi_industries_raw_data)
    # merged_multi_industries_raw_data["mongodb_id_node"] = mongodb_id_node
    #
    # node_products = get_node_products(merged_multi_industries_raw_data)
    # merged_multi_industries_raw_data["node_products"] = node_products
    #
    # products = get_products(merged_multi_industries_raw_data)
    # merged_multi_industries_raw_data["products"] = products

    # MongodbUtil.insert_one(CollectionConfig.LABEL_EXTRACT,merged_multi_industries_raw_data)

    pass

    # extract_industries_from_prompt(
    #     """
    #     示例：你是一个专业的$["工业机器人","新能源汽车","新材料","低空经济"]$产业链抽取助手...
    #     """
    # )

    # print(valid_industries(["低空经济"]))

    # res = convert_docIdAndType_to_collectionNameAndIds(
    #     [
    #         {
    #             "doc_type": "research_report",
    #             "mongodb_id": "b6abd927a86d40b891f10ade0fd130c0"
    #         },
    #         {
    #             "doc_type": "research_report",
    #             "mongodb_id": "fa1c26252598433b8ddbc157f03edbdb"
    #         },
    #         {
    #             "doc_type": "invoice",
    #             "mongodb_id": "asdasd"
    #         }
    #     ]
    # )
    # print(res)

    # mongodb_ids = ["b6abd927a86d40b891f10ade0fd130c0", "fa1c26252598433b8ddbc157f03edbdb",
    #                "bc141372c98e433db2ff273e56c8dde7", "8f1d1a1089464a55898931f1d862938c",
    #                "5cd87797d8fb41f78bb40285ee6ddf18"],
