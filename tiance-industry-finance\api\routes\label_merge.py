﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/04/28 16:14:49
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   label_merge.py
@Project    :   tiance-industry-finance
'''
import asyncio
from fastapi import APIRouter
from entity.request_entity import LabelMergeRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.time_util import TimeUtil
from configs.collection_config import CollectionConfig
from service.label_merge_service import LabelMergeService
from utils.uuid_util import UuidUtil
from configs.run_config import RunConfig
import copy
from utils.label_extract_utils import fill_complete_chain_structure
from service.label_to_perform_adapter import merge_and_deduplicate,replace_mongodb_source,find_label_in_source,map_mongodb_id2info,Label2PerformAdapter,deduplicate_dicts
from utils.tree_utils import TreeUtils
import copy






async def common_merge_helper(label_extract_info,merge_func):
# 1. 产业链进行合并
    # chain_structure = [key for key in label_extract_info["node_companies"]]
    if not callable(merge_func):
        raise TypeError("merge_func must be a callable function.")
    chain_structure = []
    company_abb_labels = set()
    node_structure_mapper = {}
    for key,value in label_extract_info["node_companies"].items():
        chain_structure.append(key)
        for company_abb in value:
            company_abb_labels.add(company_abb["abb"])
            node_structure_mapper[company_abb["abb"]] = company_abb
    
    LogUtil.info(f"开始进行大模型产业链合并")
    result = await merge_func(label_array=chain_structure,is_industry=True)
    LogUtil.info(f"大模型产业链合并结果，result：{result}")
    # 处理合并数据
    t_value_to_remove = []
    merge_filter_industry = {}
    # 新增的产业链
    for key in result["result"]:
        if key not in label_extract_info["node_companies"]:
            
            if key in result:
                label_extract_info["node_companies"][key] = []
                value = label_extract_info["node_companies"][key]
                merge_filter_industry[key] = {}
                for t_value in result[key]:
                    # 产业链数据处理
                    # print(str(merge_filter_industry[key]))
                    # print(str(label_extract_info["node_companies"]))
                    merge_filter_industry[key][t_value] = label_extract_info["node_companies"][t_value]
                    value = label_extract_info["node_companies"][key]
                    value = merge_and_deduplicate(value,label_extract_info["node_companies"][t_value],"abb")
                    label_extract_info["node_companies"][key]=value
                    if t_value in label_extract_info["node_companies"]:
                        t_value_to_remove.append(t_value)
            else:
                LogUtil.error(f"unmatched label in result label:{key}")
    # remove nodes
    for key,value in label_extract_info["node_companies"].items():
        
        if key in result:
            merge_filter_industry[key] = {}
            for t_value in result[key]:
                # 产业链数据处理
                merge_filter_industry[key][t_value] = label_extract_info["node_companies"][t_value]
                value = merge_and_deduplicate(value,label_extract_info["node_companies"][t_value],"abb")
                if t_value in label_extract_info["node_companies"]:
                    t_value_to_remove.append(t_value)
    for t_value in t_value_to_remove:
        if t_value in label_extract_info["node_companies"]:
            label_extract_info["node_companies"].pop(t_value)
    
    # mongodb_id_node update 
    # for key,value in result.items():
    #     if key == "result":
    #         continue
    #     for t_value in value:
    #         for monogodb_id,source_array in label_extract_info["mongodb_id_node"].items():
    #             if t_value in source_array:
    #                 LogUtil.debug(f"t_value replace: {t_value}")
    #                 source_array.remove(t_value)
    #                 if key not in source_array:
    #                     LogUtil.debug(f"source_array add: {key}")
    #                     source_array.append(key)
    # remove mongodb id for nodes
    label_extract_info["mongodb_id_node"] = replace_mongodb_source(result=result,mongodb_dict=label_extract_info["mongodb_id_node"])
            
    LogUtil.debug(f"node_companies process_after:"+ str(label_extract_info["node_companies"]))   
    LogUtil.debug(f"mongodb_id_node process_after:"+ str(label_extract_info["mongodb_id_node"]))      
    LogUtil.debug(f"merge_filter_industry:{merge_filter_industry}")
    # update merge_filter_industry
    if "merge_filter_industry" not in label_extract_info:
        label_extract_info["merge_filter_industry"] = {}
    label_extract_info["merge_filter_industry"].update(merge_filter_industry) 
    LogUtil.debug(f"merge_filter_industry add to result: "+ str(label_extract_info["merge_filter_industry"]))

    # 2. 公司进行合并
    merge_filter_company = {}
    LogUtil.debug(f"key_companies process_company_merge_before:"+ str(label_extract_info["key_companies"]))
    for value in label_extract_info["key_companies"]:
        company_abb_labels.add(value["abb"])
    company_abb_labels = list(company_abb_labels)

    LogUtil.debug(f"company_abb_labels input_dict {company_abb_labels}")
    results = await merge_func(label_array=company_abb_labels)
    LogUtil.debug(f"company_abb_labels merge_after {results}")

    company_mapper_dict = {}
    for key,value in results.items():
        if key == "result":
            continue
        for replace_abb in value:
            company_mapper_dict[replace_abb] = key
    LogUtil.debug(f"company_mapper_dict:{company_mapper_dict}")
    # replace node companies
    for key,value in label_extract_info["node_companies"].items():
        company_to_append_set = set()
        t_value_to_remove = []
        for company in value:
            if company["abb"] in company_mapper_dict:
                t_value_to_remove.append(company)
                is_in_dict = False
                for t_company in value:
                    if company_mapper_dict[company["abb"]] == t_company["abb"]:
                        is_in_dict = True
                if is_in_dict == False:
                    LogUtil.debug("replace "+ company["abb"] + " for " +company_mapper_dict[company["abb"]] + " value "+ str(node_structure_mapper[company_mapper_dict[company["abb"]]]))
                    value.append(node_structure_mapper[company_mapper_dict[company["abb"]]])
        
        for t_value in t_value_to_remove:
            value.remove(t_value)
    # remove key_companies
    company_to_remove = []
    for company in label_extract_info["key_companies"]:
        if company["abb"] in company_mapper_dict:
            company_to_remove.append(company)
            if company_mapper_dict[company["abb"]] not in merge_filter_company:
                merge_filter_company[company_mapper_dict[company["abb"]]] = []
            merge_filter_company[company_mapper_dict[company["abb"]]].append(company) 
    for company in company_to_remove:
        label_extract_info["key_companies"].remove(company)
    # remove mongodb_id
    LogUtil.debug("before mongodb_id_companies" + str(label_extract_info["mongodb_id_companies"]))
    label_extract_info["mongodb_id_companies"] = replace_mongodb_source(result=results,mongodb_dict=label_extract_info["mongodb_id_companies"])
    LogUtil.debug("after mongodb_id_companies" + str(label_extract_info["mongodb_id_companies"]))
    
    # remove from product
    product_company_merge_set = set()
    LogUtil.debug("before company merge label_extract_info[\"product\"]" + str(label_extract_info["product"]))
    for product_company in label_extract_info["product"]:
        if product_company["company_abb"] in company_mapper_dict:
            product_company_merge_set.add((company_mapper_dict[product_company["company_abb"]],product_company["product_abb"]))
        else:
            product_company_merge_set.add((product_company["company_abb"],product_company["product_abb"]))
    label_extract_info["product"] = []
    for company_abb,product_abb in product_company_merge_set:
        label_extract_info["product"].append({"company_abb":company_abb,"product_abb":product_abb})
    LogUtil.debug("after company merge label_extract_info[\"product\"]" + str(label_extract_info["product"]))
    
    LogUtil.debug(f"node_companies process_company_merge_after:"+ str(label_extract_info["node_companies"])) 
    LogUtil.debug(f"key_companies process_company_merge_after:"+ str(label_extract_info["key_companies"]))
    if "merge_filter_company" not in label_extract_info:
        label_extract_info["merge_filter_company"] = {}
    label_extract_info["merge_filter_company"].update(merge_filter_company) 
    LogUtil.debug(f"merge_filter_company process_company_merge_after:"+ str(label_extract_info["merge_filter_company"]))
    

    # 需要更新
    # 3.产品合并
    merge_filter_product = {}
    product_label = [item["product_abb"] for item in label_extract_info["product"]]
    LogUtil.debug(f"product_label input_dict {product_label}")
    results = await merge_func(label_array=product_label)
    product_mapper_dict = {}
    for key,value in results.items():
        if key == "result":
            continue
        for replace_abb in value:
            product_mapper_dict[replace_abb] = key
    LogUtil.debug(f"product_label merge_after {results}")
    LogUtil.debug(f"product_mapper_dict merge_after {product_mapper_dict}")

    # remove from product
    product_company_merge_set = set()
    LogUtil.debug("before company merge label_extract_info[\"product\"]" + str(label_extract_info["product"]))
    for product_company in label_extract_info["product"]:
        if product_company["product_abb"] in product_mapper_dict:
            product_company_merge_set.add((product_company["company_abb"],product_mapper_dict[product_company["product_abb"]]))
            if product_mapper_dict[product_company["product_abb"]] not in merge_filter_product:
                merge_filter_product[product_mapper_dict[product_company["product_abb"]]] = []    
            merge_filter_product[product_mapper_dict[product_company["product_abb"]]].append(product_company)
        else:
            product_company_merge_set.add((product_company["company_abb"],product_company["product_abb"]))
    label_extract_info["product"] = []
    for company_abb,product_abb in product_company_merge_set:
        label_extract_info["product"].append({"company_abb":company_abb,"product_abb":product_abb})
    LogUtil.debug("after company merge label_extract_info[\"product\"]" + str(label_extract_info["product"]))
    
    # remove from mongdb product
    LogUtil.debug("before company merge mongodb_id_product" + str(label_extract_info["mongodb_id_product"]))
    label_extract_info["mongodb_id_product"] = replace_mongodb_source(result=results,mongodb_dict=label_extract_info["mongodb_id_product"])
    LogUtil.debug("after company merge mongodb_id_product" + str(label_extract_info["mongodb_id_product"]))
    
    if "merge_filter_product" not in label_extract_info:
        label_extract_info["merge_filter_product"] = {}
    label_extract_info["merge_filter_product"].update(merge_filter_product) 
    

    # LogUtil.debug(label_extract_info)
    return label_extract_info

router = APIRouter()



@router.post("/label_merge", summary="标签融合")
async def label_merge(request: LabelMergeRequest) -> SuccessResponse | FalseResponse:
    try:
        data = {}
        
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        merge_service = LabelMergeService(model=request.model,prompt=request.prompt,rule_type=request.merge_type)
        # industry = request.industry
        LogUtil.info("获取所有的产业链名称")
        label_extract_info = MongodbUtil.coll(CollectionConfig.LABEL_EXTRACT).find_one({"_id": request.mongodb_id})
        # print(f"label_extract_info:{label_extract_info}")
        LogUtil.log_json(describe="label_extract_info", kwargs=dict(label_extract_info))
        if request.is_ai_extend == True and  label_extract_info["node_companies"] == {} :
            generate_dict,mongodb_id_node,mongodb_id_info = await merge_service.ai_extend(label_extract_info=label_extract_info)
            LogUtil.info(str(generate_dict))
            label_extract_info["node_companies"] = generate_dict
            label_extract_info["mongodb_id_node"].update(mongodb_id_node)
            label_extract_info["mongodb_id_info"].update(mongodb_id_info)
            # print(f"mongodb_id_info:"+str(label_extract_info["mongodb_id_info"]))
            # print(f"mongodb_id_node:"+str(label_extract_info["mongodb_id_node"]))
        label_merge_info = await common_merge_helper(label_extract_info=label_extract_info,merge_func=merge_service.llm_merge)
        LogUtil.log_json(describe="回复", kwargs=dict(label_merge_info))
        if request.merge_type == "Frequency":
            LogUtil.debug("Frequency")
            label_merge_info = await common_merge_helper(label_extract_info=label_merge_info,merge_func=merge_service.rule_merge)
        elif request.merge_type == "Source":
            label_merge_info = await common_merge_helper(label_extract_info=label_merge_info,merge_func=merge_service.rule_merge)
            LogUtil.debug("Source")


        label_merge_info["_id"] = UuidUtil.get_uuid()

        MongodbUtil.insert_one(collection_name=CollectionConfig.LABEL_MERGE, doc_content=label_merge_info)

        LogUtil.log_json(describe="标签融合请求返回结果", kwargs=label_merge_info)
        return SuccessResponse(data=label_merge_info)

    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)


async def merge_task_multirunner_helper(merge_type:str,merge_array,merge_func):
    is_industry = False
    if merge_type == "node_json" or merge_type =="node":
        is_industry = True
    result = await merge_func(label_array=merge_array,is_industry=is_industry)
    return (merge_type,result)

def chain_structure_2_json_mapper(input_dict,mapper_dict:dict,industry):
    if type(input_dict) != dict:
        return {}

    for key,value in input_dict.items():
        if key == "data" :
            continue
        if type(value) == dict:
            input_dict[key] = chain_structure_2_json_mapper(input_dict=value,mapper_dict=mapper_dict,industry=industry)
            # for key1,value1 in value.items():
                
            #     if key1 == "data":
            #         continue
            #     else:
            #         if key1 =="协作机器人":
            #             print("test4",value[key1])
            #         value[key1] = chain_structure_2_json_mapper(input_dict=value1,mapper_dict=mapper_dict,industry=industry)
            #         if key1 =="协作机器人":
            #             print("test",value[key1])
            #     if key1 =="协作机器人":
            #         print("test2",value[key1])
            #         print("test3",mapper_dict)

        else:
            LogUtil.error(f"unexpect format{value}")
        
        if(industry,key) in mapper_dict:  
            if(type(input_dict[key]) != dict):
                input_dict[key] = {}
            input_dict[key]["data"] =  mapper_dict[(industry,key)]
    return input_dict

def get_all_company_list_from_company(chain_structure:dict):
    all_company = []
    for key,value in chain_structure.items():
        all_company.extend(value["company"])
    return list(set(all_company))


        

    

async def perform_common_merge_helper(label_extract_info,merge_func,merge_process:str):
# 1. 产业链进行合并
    # chain_structure = [key for key in label_extract_info["node_companies"]]
    if not callable(merge_func):
        raise TypeError("merge_func must be a callable function.")
    chain_structure = []
    company_abb_labels = set()
    node_structure_mapper = {}
    # print(label_extract_info["chain_structure"])
    if merge_process == "LLM":
        if "chain_structure_json" in label_extract_info:
            chain_structure = label_extract_info["chain_structure_json"]
    elif label_extract_info["chain_structure"] is not {}:
        for key,value in label_extract_info["chain_structure"].items():
                chain_structure.append(key)

    
    for value in label_extract_info["company"]:
        # 
        # print(company_abb_labels)
        company_abb_labels.add(value["abb"])
    company_abb_labels = list(company_abb_labels)
    
    product_label = [item["product_abb"] for item in label_extract_info["product"]]

    input_label = [("node_json" if merge_process=="LLM" else "node",chain_structure),("company",company_abb_labels),("product",product_label)]
    tasks = [merge_task_multirunner_helper(merge_type=labels[0],merge_array=labels[1],merge_func=merge_func) for labels in input_label]
    tasks_results = await asyncio.gather(*tasks)
    result_node = {}
    result_company = {}
    result_product = {}
    for task_result in tasks_results:
        if task_result[0] == "node" or task_result[0] == "node_json":
            result_node = task_result[1]
        elif task_result[0] == "company":
            result_company = task_result[1]
        elif task_result[0] == "product":
            result_product = task_result[1]


    if label_extract_info["chain_structure"] is not {}:
        # 0428 RULE 走原先的逻辑，LLM走新逻辑
        if merge_process == "LLM":
            if "merge_filter_industry" not in label_extract_info:
                label_extract_info["merge_filter_industry"] = {}
            if "chain_structure_json" in label_extract_info:
                # 更新json，对每个产业链上面的每个节点构建dict
                node_value_mapper = {}
                for key,value in label_extract_info["chain_structure"].items():
                    nodes = key.split("|")
                    industry = nodes[0]
                    node = nodes[-1]
                    if (industry,node) in node_value_mapper:
                        node_value_mapper[(industry,node)]["product"] = merge_and_deduplicate(node_value_mapper[(industry,node)]["product"],value["product"],"product_abb")
                        # 0429 变更为全称
                        node_value_mapper[(industry,node)]["company"].extend(value["company"])
                        node_value_mapper[(industry,node)]["company"] = list(set(node_value_mapper[(industry,node)]["company"]))
                        # merge_and_deduplicate(node_value_mapper[(industry,node)]["company"],value["company"],"abb")
                        node_value_mapper[(industry,node)]["source_list"] = merge_and_deduplicate(node_value_mapper[(industry,node)]["source_list"],value["source_list"],"_id")
                    else:
                        # rule 部分的去重可以兜底，理论上这里可以去除
                        value["product"] = deduplicate_dicts(value["product"],"product_abb")
                        # value["company"] = merge_and_deduplicate(value["company"],label_extract_info["chain_structure"][t_value]["company"],"abb")
                        # 0429 变更为全称
                        value["company"] = list(set(value["company"]))
                        value["source_list"] = deduplicate_dicts(value["source_list"],"_id")
                        node_value_mapper[(industry,node)] = value
                LogUtil.info(f"node_value_mapper: {node_value_mapper}")
                LogUtil.info(f"generate: {result_node}")
                for industry_chain in result_node:
                    industry_name = list(industry_chain.keys())
                    if len(industry_name) == 0:
                        continue
                    industry_chain = chain_structure_2_json_mapper(input_dict=industry_chain,mapper_dict=node_value_mapper,industry=industry_name[0])
                    # print("industry_chain",industry_chain)
                    # for key,value in industry_chain.items():
                    #     print("key value",(key,value))
                    #     value = chain_structure_2_json_mapper(input_dict=value,mapper_dict=node_value_mapper,industry=key)
                LogUtil.info(f"final_merge_structre{result_node}")
                merge_json = {}
                for j in result_node:
                    merge_json.update(j)
                
                label_extract_info["chain_structure"] = TreeUtils.nodes2listmap(merge_json)
                
                LogUtil.info(f"final_export_structure"+str(label_extract_info["chain_structure"]))
            else:
                LogUtil.error(f"chain_structure_json is none")
        elif merge_process == "RULE":
            
            for key,value in label_extract_info["chain_structure"].items():
                    chain_structure.append(key)
            
            LogUtil.info(f"开始进行大模型产业链合并")
            # result_node = await merge_func(label_array=chain_structure,is_industry=True)
            LogUtil.info(f"大模型产业链合并结果，result：{result_node}")
            # 处理合并数据
            t_value_to_remove = []
            merge_filter_industry = {}
            # 新增的产业链
            for key in result_node["result"]:
                if key not in label_extract_info["chain_structure"]:
                    if key in result_node:
                        label_extract_info["chain_structure"][key] = {}
                        label_extract_info["chain_structure"][key]["product"] = []
                        label_extract_info["chain_structure"][key]["company"] = []
                        label_extract_info["chain_structure"][key]["source_list"] = []
                        # value = label_extract_info["chain_structure"][key]
                        merge_filter_industry[key] = {}
                        for t_value in result_node[key]:
                            # 产业链数据处理
                            # print(str(merge_filter_industry[key]))
                            # print(str(label_extract_info["node_companies"]))
                            if t_value not in label_extract_info["chain_structure"]:
                                LogUtil.error(f"unknown label in origin list :{t_value}")
                                continue
                            merge_filter_industry[key][t_value] = label_extract_info["chain_structure"][t_value]
                            value = label_extract_info["chain_structure"][key]
                            value["product"] = merge_and_deduplicate(value["product"],label_extract_info["chain_structure"][t_value]["product"],"product_abb")
                            # value["company"] = merge_and_deduplicate(value["company"],label_extract_info["chain_structure"][t_value]["company"],"abb")
                            # 0429 变更为全称
                            value["company"].extend(label_extract_info["chain_structure"][t_value]["company"])
                            value["company"] = list(set(value["company"]))

                            value["source_list"] = merge_and_deduplicate(value["source_list"],label_extract_info["chain_structure"][t_value]["source_list"],"_id")

                            label_extract_info["chain_structure"][key]=value
                            if t_value in label_extract_info["chain_structure"]:
                                t_value_to_remove.append(t_value)
                    else:
                        LogUtil.error(f"unmatched label in result_node label:{key}")
            # remove merge nodes
            for key,value in label_extract_info["chain_structure"].items():
                if key in result_node:
                    merge_filter_industry[key] = {}
                    for t_value in result_node[key]:
                        # 产业链数据处理
                        if t_value not in label_extract_info["chain_structure"]:
                            LogUtil.error(f"unknown label in origin list :{t_value}")
                            continue
                        merge_filter_industry[key][t_value] = label_extract_info["chain_structure"][t_value]
                        # value = merge_and_deduplicate(value,label_extract_info["node_companies"][t_value],"abb")
                        value["product"] = merge_and_deduplicate(value["product"],label_extract_info["chain_structure"][t_value]["product"],"product_abb")
                        # value["company"] = merge_and_deduplicate(value["company"],label_extract_info["chain_structure"][t_value]["company"],"abb")
                        # 0429 变更为全称
                        value["company"].extend(label_extract_info["chain_structure"][t_value]["company"])
                        value["company"] = list(set(value["company"]))

                        value["source_list"] = merge_and_deduplicate(value["source_list"],label_extract_info["chain_structure"][t_value]["source_list"],"_id")
                        if t_value in label_extract_info["chain_structure"]:
                            t_value_to_remove.append(t_value)
                else:
                    # t_value_to_remove.append(key)
                    value["product"] = deduplicate_dicts(value["product"],"product_abb")
                    # value["company"] = merge_and_deduplicate(value["company"],label_extract_info["chain_structure"][t_value]["company"],"abb")
                    # 0429 变更为全称
                    value["company"] = list(set(value["company"]))
                    value["source_list"] = deduplicate_dicts(value["source_list"],"_id")
            LogUtil.debug(f"t_value_to_remove:{t_value_to_remove}")          
            for t_value in t_value_to_remove:
                
                if t_value in label_extract_info["chain_structure"]:
                    LogUtil.debug(f"t_value:{t_value}")
                    label_extract_info["chain_structure"].pop(t_value)
            LogUtil.debug(f"node_companies process_after::"+ str(label_extract_info["chain_structure"]))         
            LogUtil.debug(f"merge_filter_industry:{merge_filter_industry}")
            # update merge_filter_industry
            if "merge_filter_industry" not in label_extract_info:
                label_extract_info["merge_filter_industry"] = {}
            label_extract_info["merge_filter_industry"].update(merge_filter_industry) 
            LogUtil.debug(f"merge_filter_industry add to result_node: "+ str(label_extract_info["merge_filter_industry"]))
        else:
            if "merge_filter_industry" not in label_extract_info:
                label_extract_info["merge_filter_industry"] = {}
            LogUtil.error(f"unknown merge_process{merge_process}")
    else:
        if "merge_filter_industry" not in label_extract_info:
            label_extract_info["merge_filter_industry"] = {}
        LogUtil.debug(f"chain_structure is none:")



    


    # 2. 公司进行合并
    merge_filter_company = {}
    LogUtil.debug(f"company process_company_merge_before:"+ str(label_extract_info["company"]))
    # print(label_extract_info["company"])
    # for value in label_extract_info["company"]:
    #     # 
    #     # print(company_abb_labels)
    #     company_abb_labels.add(value["abb"])
    # company_abb_labels = list(company_abb_labels)

    LogUtil.debug(f"company_abb_labels input_dict {company_abb_labels}")
    # result_company = await merge_func(label_array=company_abb_labels)
    LogUtil.debug(f"company_abb_labels merge_after {result_company}")

    company_mapper_dict = {}
    for key,value in result_company.items():
        if key == "result":
            continue
        for replace_abb in value:
            company_mapper_dict[replace_abb] = key
    LogUtil.debug(f"company_mapper_dict:{company_mapper_dict}")
    
    # remove key_companies
    company_to_remove = []
    for company in label_extract_info["company"]:
        if company["abb"] in company_mapper_dict:
            company_to_remove.append(company)
            if company_mapper_dict[company["abb"]] not in merge_filter_company:
                merge_filter_company[company_mapper_dict[company["abb"]]] = []
            merge_filter_company[company_mapper_dict[company["abb"]]].append(company)
    for company in company_to_remove:
        label_extract_info["company"].remove(company)
    # company 去重复 依照abb
    label_extract_info["company"] = deduplicate_dicts(label_extract_info["company"],"abb")
    for company in label_extract_info["company"]:
        value = company['abb']
        if "abb_list" not in company:
            company['abb_list'] = []
        company['abb_list'].append(value)
        if value in merge_filter_company:
            for filter_company in merge_filter_company[value]:
                company["source_list"] = merge_and_deduplicate(company["source_list"],filter_company["source_list"],"_id")
                company['abb_list'].append(filter_company["abb"])
        company['abb_list'] = list(set(company['abb_list']))

     
    LogUtil.debug(f"company process_company_merge_after:"+ str(label_extract_info["company"]))
    if "merge_filter_company" not in label_extract_info:
        label_extract_info["merge_filter_company"] = {}
    label_extract_info["merge_filter_company"].update(merge_filter_company) 
    LogUtil.debug(f"merge_filter_company process_company_merge_after:"+ str(label_extract_info["merge_filter_company"]))
    
    # 3.产品合并
    merge_filter_product = {}
    product_label = [item["product_abb"] for item in label_extract_info["product"]]
    LogUtil.debug(f"product_label input_dict {product_label}")
    # result_product = await merge_func(label_array=product_label)
    LogUtil.debug(f"product_label merge after input_dict {result_product}")
    product_mapper_dict = {}
    for key,value in result_product.items():
        if key == "result":
            continue
        for replace_abb in value:
            product_mapper_dict[replace_abb] = key
    LogUtil.debug(f"product_label merge_after {result_product}")
    LogUtil.debug(f"product_mapper_dict merge_after {product_mapper_dict}")

    # build set_map 
    product_company_mapper_dict = {}
    for product_company in label_extract_info["product"]:
        product_company_mapper_dict[(product_company["product_abb"],product_company["company_abb"])] = product_company
    # remove from product
    # product_company_merge_set = set()
    product_company_to_remove = []
    product_company_to_append = []
    LogUtil.debug("before company merge label_extract_info[\"product\"]" + str(label_extract_info["product"]))
    for product_company in label_extract_info["product"]:
        if product_company["product_abb"] in product_mapper_dict:
            if (product_mapper_dict[product_company["product_abb"]],product_company["company_abb"]) in product_company_mapper_dict:
                product_company_to_remove.append(product_company)
            else :
                t_product_company = copy.deepcopy(product_company)
                t_product_company["product_abb"] = product_mapper_dict[product_company["product_abb"]]
                product_company_to_append.append(t_product_company)
                product_company_to_remove.append(product_company)
            # product_company_merge_set.add((product_company["company_abb"],product_mapper_dict[product_company["product_abb"]]))
            mapper_key = product_mapper_dict[product_company["product_abb"]]+'_'+product_company["company_abb"]
            if mapper_key not in merge_filter_product:
                merge_filter_product[mapper_key] = []
            merge_filter_product[mapper_key].append(product_company)
        else:
            LogUtil.debug("not replace " + str((product_company["company_abb"],product_company["product_abb"])))
            # product_company_merge_set.add((product_company["company_abb"],product_company["product_abb"]))
    for filter_company in product_company_to_remove:
        label_extract_info["product"].remove(filter_company)
    for add_company in product_company_to_append:
        label_extract_info["product"].append(add_company)
    
    # product 去重复 product_abb
    # label_extract_info["product"] = deduplicate_dicts(label_extract_info["product"],"product_abb")

    for product_company in label_extract_info["product"]:
        value = product_company["product_abb"]
        if "product_abb_list" not in product_company:
            product_company["product_abb_list"] = []
        product_company["product_abb_list"].append(value)

        merge_list = []
        mapper_key = product_company["product_abb"]+'_'+product_company["company_abb"]
        if mapper_key in merge_filter_product:
            merge_ori_list = merge_filter_product[mapper_key]
            for item in merge_ori_list:
                merge_list.append(item["product_abb"])
                product_company["source_list"] = merge_and_deduplicate(product_company["source_list"],item["source_list"],"_id")  
        product_company["product_abb_list"].extend(merge_list)
        product_company["product_abb_list"] = list(set(product_company["product_abb_list"]))
    LogUtil.debug("after company merge label_extract_info[\"product\"]" + str(label_extract_info["product"]))
    
    
    if "merge_filter_product" not in label_extract_info:
        label_extract_info["merge_filter_product"] = {}
    label_extract_info["merge_filter_product"].update(merge_filter_product) 


    merge_filter_fullname_company = {}
    full_name_company_map_company_map = {}
    full_name_process_to_remove = []
    # merge product by company full name
    for company in label_extract_info["company"]:
        if company["name"] not in full_name_company_map_company_map:
            full_name_company_map_company_map[company["name"]] = company
        else:
            keep_name = full_name_company_map_company_map[company["name"]] 
            merge_filter_fullname_company[keep_name["name"]] = company
            full_name_process_to_remove.append(company)
            keep_name["abb_list"].append(company["abb"])
            keep_name["source_list"] = merge_and_deduplicate(keep_name["source_list"],company["source_list"],"_id")
            full_name_company_map_company_map[company["name"]]  = keep_name
        
    for filter_company in full_name_process_to_remove :
        label_extract_info["company"].remove(filter_company)
    for company in label_extract_info["company"]:
        company = full_name_company_map_company_map[company["name"]]
    
    if "merge_filter_company" not in label_extract_info:
        label_extract_info["merge_filter_company"] = {}
    label_extract_info["merge_filter_company"].update(merge_filter_fullname_company) 

    

    # LogUtil.debug(label_extract_info)
    return label_extract_info


@router.post("/label_merge_perform", summary="标签融合")
async def label_merge_perform(request: LabelMergeRequest) -> SuccessResponse | FalseResponse:
    try:
        # cached
        # label_merge_info["request_param"] = {
            #     "model":request.model,
            #     "prompt":request.prompt,
            #     "merge_type":request.merge_type,
            #     "is_ai_extend":request.is_ai_extend,
            #     "is_cached":request.is_cached,
            # }
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        cached_data = MongodbUtil.coll(CollectionConfig.LABEL_MERGE_PERFORM).find_one(
            {"raw_label_merge_data_id": request.mongodb_id,
            "model":request.model,
            "prompt":request.prompt,
            "merge_type":request.merge_type,
            "is_ai_extend":request.is_ai_extend,
            "is_cached":request.is_cached
            },sort=[('$natural', -1)])
        # print(cached_data)
        if cached_data is not None and request.is_cached == True:
            LogUtil.info(f"{request}:use cached")
             # print(label_merge_info["company"])
            
            
            # 2025 05 08 增加重复执行二次融合
            # merge_service = LabelMergeService(model=request.model,prompt=request.prompt,rule_type=request.merge_type)
            
            # if request.merge_type == "Frequency":
            #     LogUtil.debug("Frequency")
            #     cached_data = await perform_common_merge_helper(label_extract_info=cached_data,merge_func=merge_service.rule_merge,merge_process="RULE")
            # elif request.merge_type == "Source":
            #     cached_data = await perform_common_merge_helper(label_extract_info=cached_data,merge_func=merge_service.rule_merge,merge_process="RULE")
            #     LogUtil.debug("Source")
            LogUtil.log_json(describe="标签融合展示请求返回结果", kwargs=cached_data)
            return SuccessResponse(data=cached_data)

        history = MongodbUtil.coll(CollectionConfig.LABEL_EXTRACT_PERFORM).find_one({"_id": request.mongodb_id})
        if history is None:
            return FalseResponse(data={"input error":f"can not find _id:{request.mongodb_id} in database"})
        # 旧方式，Demo不走这个逻辑，这个是基于原有的数据库结构处理
        if RunConfig.IS_MERGE_ORIGIN_LABEL == True:
            LogUtil.debug(f"go to merge origin label:{result.code}")
            request.mongodb_id = history["raw_label_extrac_data_id"]
            # request.
            result = await label_merge(request)
            LogUtil.debug(f"call label fault response:{result.code}")
            if result.code == 200:
                
                label_merge_info = result.data
                result_data = Label2PerformAdapter.label_to_perform(label_merge_info)
                result_data["raw_label_merge_data_id"] = label_merge_info["_id"]
                result_data["_id"] = UuidUtil.get_uuid()
                MongodbUtil.insert_one(collection_name=CollectionConfig.LABEL_MERGE_PERFORM, doc_content=result_data)

                LogUtil.log_json(describe="标签融合展示请求返回结果", kwargs=result_data)
                
                return SuccessResponse(data=result_data)
            else:
                return FalseResponse(data=result.data)
        # 新方式，所有的表结构没有联合更新，三张表是独立的表
        else:
            LogUtil.debug(f"go to not to use merge origin label")
            merge_service = LabelMergeService(model=request.model,prompt=request.prompt,rule_type=request.merge_type)
            # print("test")
            if request.is_ai_extend == True and  history["chain_structure"] == {} :
                generate_dict,mongodb_id_node,mongodb_id_info = await merge_service.ai_extend_perform(label_extract_info=history)
                # LogUtil.info(str(generate_dict))
                # print(generate_dict)
                history["chain_structure"] = generate_dict
                # print(TreeUtils.restore_tree(generate_dict))
                history["chain_structure_json"] = TreeUtils.restore_tree(generate_dict)

                # history["chain_structure"] = generate_dict
            # 合并产业链节点表
            label_merge_info = await perform_common_merge_helper(label_extract_info=history,merge_func=merge_service.llm_merge,merge_process="LLM")
            label_merge_info["chain_structure_llm_merge"] = copy.deepcopy(label_merge_info["chain_structure"])
            if request.merge_type == "Frequency":
                LogUtil.debug("Frequency")
                label_merge_info = await perform_common_merge_helper(label_extract_info=label_merge_info,merge_func=merge_service.rule_merge,merge_process="RULE")
            elif request.merge_type == "Source":
                label_merge_info = await perform_common_merge_helper(label_extract_info=label_merge_info,merge_func=merge_service.rule_merge,merge_process="RULE")
                LogUtil.debug("Source")

            label_merge_info["raw_label_merge_data_id"] = label_merge_info["_id"]
            label_merge_info["_id"] = UuidUtil.get_uuid()
            
            
            # print(label_merge_info["company"])
            for company in label_merge_info["company"]:
                company["abb"] = ",".join(company["abb_list"])
            for product in label_merge_info["product"]:
                product["product_abb"] = ",".join(product["product_abb_list"])
            
            # fill_complete_chain_structure(
            #     label_merge_info["chain_structure"],
                # {
                #     "company": [],
                #     "product": [],
                #     "source_list": []
                # }
            # )
            label_merge_info["chain_structure"] = TreeUtils.expand_dict(original=label_merge_info["chain_structure"])
            all_company = get_all_company_list_from_company(label_merge_info["chain_structure"])
            LogUtil.info(f"all_company{all_company}")

            # 遍历并删除符合条件的元素
            # label_merge_info["company"] = [item for item in label_merge_info["company"] if item.get("name") in all_company]
            # label_merge_info["request_param"] = {
            #     "model":request.model,
            #     "prompt":request.prompt,
            #     "merge_type":request.merge_type,
            #     "is_ai_extend":request.is_ai_extend,
            #     "is_cached":request.is_cached,
            # }
            label_merge_info["model"] = request.model
            label_merge_info["prompt"] = request.prompt
            label_merge_info["merge_type"] = request.merge_type
            label_merge_info["is_ai_extend"] = request.is_ai_extend
            label_merge_info["is_cached"] = request.is_cached
            MongodbUtil.insert_one(collection_name=CollectionConfig.LABEL_MERGE_PERFORM, doc_content=label_merge_info)
            # com_to_remove = []
            # for com in label_merge_info["company"]:
            #     if com["name"] not in all_company:
            #         com_to_remove.append(com)
            # for com in com_to_remove:
            #     label_merge_info["company"].pop(com)
            # print(label_merge_info["company"])
            LogUtil.log_json(describe="标签融合展示请求返回结果", kwargs=label_merge_info)
            
            return SuccessResponse(data=label_merge_info)
            
        


    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)

if __name__ == "__main__":


    from utils.mongodb_util import MongodbUtil
    from utils.minio_util import MinIoUtil
    LogUtil.init(process_name="label_merge")
    # 初始化数据库连接
    MongodbUtil.connect()
    MinIoUtil.connect()
    # request_entity = LabelMergeRequest(mongodb_id="338ea781843e45bd892593587724f63a")
    # result = asyncio.run(label_merge_perform(LabelMergeRequest(
    #                 mongodb_id="bc141372c98e433db2ff273e56c8dde7"
    #                 )))
    result = asyncio.run(label_merge_perform(LabelMergeRequest(
                    mongodb_id="cb24fb65ad5b40fd950a700f085f3769",
                    model="DeepSeek-R1-Distill-Qwen-32B",
                    prompt="""
                    你是一个专业的产业链合并助手，能够准确地整合不同来源的产业链信息，给出最完整、最准确、最客观的产业链结构。请返回最精简的结果
                    """,
                    is_cached=False,
                    is_ai_extend=True,
                    merge_type="Source")
                    ))
    print(result)
    # result = asyncio.run(label_merge_perform(LabelMergeRequest(
    #                 mongodb_id="test1",is_ai_extend=True,model="DeepSeek-R1-Distill-Qwen-32B"
    #                 ,is_cached=False)))
    
    # result_node=[{'工业机器人': {'上游': {'核心零部件': {'减速器': {'品牌': ['Nabtesco', 'Harmonica']}, '伺服': {'品牌': ['Yaskawa', 'Mitsubishi']}, '控制器': {'品牌': ['ABB', 'Fanuc']}}}, '中游': {'本体': {'传统机器人': {'品牌': ['ABB', 'Fanuc', 'Kuka', 'Yaskawa']}, '协作机器人': {'品牌': ['Universal Robots', 'Franka Emika', 'ABB YuMi']}}}, '下游': {'系统集成商': {'汽车': {'企业': ['Kuka Systems', 'ABB Robotics']}, '3C电子': {'企业': ['Fanuc', 'Yaskawa Electric']}, '新能源': {'企业': ['ABB Robotics', 'Kuka Systems']}, '其他': {'企业': ['Fanuc', 'Yaskawa Electric', 'Kuka Systems']}, '金属': {'企业': ['ABB Robotics', 'Fanuc']}}}}}]
    # node_value_mapper = {('3ad4695c6cdc44e9ade70bf84d9c5049', '3ad4695c6cdc44e9ade70bf84d9c5049'): {'工业机器人': {'上游': {'核心零部件': {'减速器': {'谐波减速器': {}, 'RV减速器': {}}, '伺服系统': {'伺服驱动器': {}, '伺服电机': {}}, '控制器': {'关节控制器': {}, '处理器': {}}}}, '中游': {'本体制造': {'机器人本体': {'多关节型': {}, '平面多关节型': {}, '协作型': {}, '并联型': {}, '直角坐标型': {}}}}, '下游': {'应用': {'系统集成': {'焊接': {}, '装卸': {}, '装配': {}, '喷涂': {}, '上下料': {}}, '应用领域': {'汽车行业': {}, '电子工业': {}, '金属加工': {}, '化学制品': {}, '食品制造': {}}}}}}, ('2e25bd437c0d4cfb9a1b61d15a76868b', '2e25bd437c0d4cfb9a1b61d15a76868b'): {'工业机器人': {'上游': {'核心零部件': {'减速器': {}, '伺服': {}, '控制器': {}}}, '中游': {'本体': {'传统机器人': {}, '协作机器人': {}}}, '下游': {'系统集成商': {'汽车': {}, '3C电子': {}, '新能源': {}, '其他': {}, '金属': {}}}}}, ('f4d1f9dcb6ca49b9882a5ff3d7b6e75a', 'f4d1f9dcb6ca49b9882a5ff3d7b6e75a'): {'工业机器人': {'上游': {'核心零部件': {'减速器': {}, '伺服': {}, '控制器': {}}}, '中游': {'本体': {'传统机器人': {}, '协作机器人': {}}}, '下游': {'系统集成商': {'汽车': {}, '3C电子': {}, '新能源': {}, '其他': {}, '金属': {}}}}}}
    # for industry_chain in result_node:
    #     for key,value in industry_chain.items():
    #         value = chain_structure_2_json_mapper(input_dict=value,mapper_dict=node_value_mapper,industry=key)
    # print(f"final_structre{result_node}")

    # print(result.json())
    # print(result.code)
