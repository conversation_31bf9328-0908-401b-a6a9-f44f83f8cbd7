#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :node_analysis.py
@Description  :
<AUTHOR>
@Date         :2025/03/06 11:19:58
'''

from fastapi import APIRouter
from entity.request_entity import NodeAnalysisRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from service.kb_service import KbService
from datetime import datetime
from configs.prompt_config import PromptConfig
from service.llm_service import Llm_Service
from configs.model_config import ModelConfig
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from configs.run_config import RunConfig
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from utils.uuid_util import UuidUtil

router = APIRouter()

@router.post("/node_analysis", summary="节点分析")
async def node_analysis(request: NodeAnalysisRequest) -> SuccessResponse:
    try:
        params = dict(request)
         # 记录日志
        LogUtil.log_json(describe="节点分析请求", kwargs=params)

        collection_name = CollectionConfig.NODE_ANALYSIS
        find_result = MongodbUtil.coll(collection_name).find_one(params)
        # 查看库是否已有数据
        if find_result:
            LogUtil.info("从数据库中返回数据")
            data = find_result.get("data")
        else:
            LogUtil.info("大模型生成返回数据")
            node_list = request.node_name_info.split('|')
            root_node = node_list[0]
            current_node = node_list[-1]

            LogUtil.info("根据产业链名称检索得到mongodb_ids")
            mongodb_ids = []
            chain_result = MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).find_one({"industry": root_node})
            if chain_result:
                mongodb_ids = chain_result.get("mongodb_ids")

            LogUtil.log_json(describe="请求参数", kwargs=dict(request))
            # 1.根据mongodb_id检索得到向量数据库信息(包括向量数据库名,向量文本块字段名,标题)
            research_report_list = MongodbUtil.coll(CollectionConfig.RESEARCH_REPORT_LABEL_INFO).find({"_id": {"$in": mongodb_ids}})
            title_list = []
            milvus_collection_name = ""
            milvus_field_name = ""
            for report in research_report_list:
                title_list.append(report.get("title"))
                milvus_collection_name = report.get("milvus_collection_name") # milvus集合名
                milvus_field_name = report.get("milvus_field_name")


            expr = f"file_title in {title_list}"

            kb_service = KbService()
            # 知识库检索
            doc_list = await kb_service.search_knowledge_by_question(collection_name=milvus_collection_name, question=current_node,
                                                                     limit_top_k=10, expr=expr)
            
            # 3.拼接检索内容
            content_list = []
            source_list = []
            title_list =[]
            for item in doc_list:
                if item[milvus_field_name] not in content_list:
                    if item["file_time"] and item["file_time"] != "None":
                        item['file_time'] = datetime.strptime(item['file_time'], '%Y/%m/%d').strftime('%Y-%m-%d')
                    content_list.append(item[milvus_field_name])
                    if item["file_title"] not in title_list:
                        source_list.append({"source_name": item["file_source"], "source_file_title": item["file_title"], "source_file_url": item["file_url"], 
                                        "source_content": item[milvus_field_name], "source_file_time": item["file_time"]})
                        title_list.append(item["file_title"])
                
            # 知识库信息
            knowledge = "\n\n\n\n".join(content_list)
            
            if len(node_list) == 1:
                question = f"请阐述{current_node}。"
            else:
                question = f"请阐述{root_node}中的{current_node}。"
                
            # 组织消息
            messages = []
            messages.append(SystemMessage(PromptConfig.NODE_ANALYSIS_SYSTEM_PROMPT.format(knowledge=knowledge)))
            messages.append(UserMessage(question))
            messages = MessageConverter.convert_messages(messages)
            
            # 调用大模型回答问题
            model = RunConfig.MAX_LLM_MODEL_NAME
            llm_service = Llm_Service(model)
            answer = await llm_service.answer_question(messages, ModelConfig.MAX_LLM_MODEL_NAME)
            
            total = len(source_list)
            data = {"total": total, "summary": answer, "source_list": source_list}
                 
            # 入库
            if RunConfig.IS_SAVE_DATABASE:
                save_data = NodeAnalysisRequest(**params)
                save_data = dict(save_data)
                save_data["_id"] = UuidUtil.get_uuid()
                save_data["data"] = data
                MongodbUtil.insert_one(collection_name=CollectionConfig.NODE_ANALYSIS, doc_content=save_data)
       
        # 记录返回日志
        LogUtil.log_json(describe="节点分析请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)

