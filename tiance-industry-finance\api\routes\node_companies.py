#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :node_companies.py
@Description  :
<AUTHOR>
@Date         :2025/03/05 17:16:39
'''

from fastapi import APIRouter
from entity.request_entity import IndustryChainMapRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig

router = APIRouter()
@router.post("/node_companies", summary="节点企业")
async def node_companies(request: IndustryChainMapRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        stream_type = request.stream_type
        node_list = request.node_name_info.split("|")
        industry = node_list[0]
        LogUtil.info("1.查询产业链中心客群是否已生成")
        key_result = MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).find_one({"industry": industry})
        if key_result:
            if len(node_list) == 1:
                node_name = industry
            else:
                node_list.insert(1, stream_type) # 在列表的位置1插入上中下游类型
                node_name = "|".join(node_list)
            node_companies = key_result.get("node_companies")
            companies = node_companies.get(node_name)
            company_list = []
            for company_info in companies:
                company_name = company_info.get("name")
                tag_list = []
                if company_info.get("is_listed") == "是":
                    tag_list.append("上市企业")
                if company_info.get("is_special") == "是":
                    tag_list.append("专精特新企业")
                if company_info.get("is_high_tech") == "是":
                    tag_list.append("高新技术企业")
                company_list.append({"company_name": company_name, "tag_list": tag_list})

            data = {"total": len(company_list), "company_list": company_list}
            # 记录返回日志
            LogUtil.log_json(describe="节点分析请求返回结果", kwargs=data)
            return SuccessResponse(data=data)
        else:
            detail = f"失败详情：该产业链中心客群未在库中"
            LogUtil.error(msg=detail)
            data = {"error": detail}
            return FalseResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
