#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :product_extension.py
@Description  :
<AUTHOR>
@Date         :2024/11/16 13:51:38
'''

from fastapi import APIRouter
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from entity.request_entity import ResearchExtensionRequest
from service.kb_service import KbService
from datetime import datetime
from configs.prompt_config import PromptConfig
from service.llm_service import Llm_Service
from configs.model_config import ModelConfig
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from configs.run_config import RunConfig
from entity.data_entity import ExtensionData
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from utils.uuid_util import UuidUtil

router = APIRouter()

@router.post("/product_extension", summary="产品扩展")
async def product_extensionasync(request: ResearchExtensionRequest) -> SuccessResponse:
    try:
        params = dict(request)
         # 记录日志
        LogUtil.log_json(describe="->产品扩展请求", kwargs=params)
        # find_result = MongodbUtil.coll(collection_name = CollectionConfig.PRODUCT_EXTENSION).find_one(params)
        find_result=[]
        
        # 查看库是否已有数据
        if find_result:
            LogUtil.info("从数据库中返回数据")
            data = find_result.get("data")
        else:
            LogUtil.info("大模型生成返回数据")
            node_list = request.node_name_info.split('|')
            root_node = node_list[0]
            current_node = node_list[-1]
        
            kb_service = KbService()
            # 知识库检索
            expr = f"mongodb_id in {request.document_list}"
            # 知识库检索
            if request.document_list==[]:
                doc_list = await kb_service.search_knowledge_by_question(collection_name=CollectionConfig.KNOWLEDGE, question=current_node)
            else:
                doc_list = await kb_service.search_knowledge_by_question(collection_name=CollectionConfig.KNOWLEDGE, question=current_node, expr=expr)
            # 知识库检索相关内容
            content_list = []
            source_list = []
            for item in doc_list:
                # 时间格式转换
                if item.get("file_time")=="tmp_file":
                    item['file_time']=""
                if item.get("file_time",""):
                    item['file_time'] = datetime.strptime(item['file_time'], '%Y/%m/%d').strftime('%Y-%m-%d')
                content_list.append(item["chunk_content"])
               
                # 可以添加"source_content": item["chunk_content"]返回源文本段用来测评
                source_list.append({"source_name": item["file_source"], 
                                    "source_file_title": item["file_title"], 
                                    "source_file_url": item["file_url"], 
                                    "source_content": item["chunk_content"],
                                    "source_file_time": item["file_time"]
                                     })
                              
            # 知识库信息
            knowledge = "\n\n".join(content_list)
            
            if len(node_list) == 1:
                question = f"{current_node}。"
            else:
                question = f"{root_node}中的{current_node}。"
                
            # 组织消息
            messages = []
            messages.append(SystemMessage(PromptConfig.PRODUCT_EXTENSION_SYSTEM_PROMPT.format(knowledge=knowledge)))
            messages.append(UserMessage(question))
            messages = MessageConverter.convert_messages(messages)
            
            # 调用大模型回答问题
            model = RunConfig.MAX_LLM_MODEL_NAME
            llm_service = Llm_Service(model)
            answer = await llm_service.answer_question(messages, ModelConfig.MAX_LLM_MODEL_NAME)
            
            
            totalSource = len(source_list)
            child_list = []
            split_answer = answer.split('|')
            split_list = [item for item in split_answer if len(item) >= 3]
            for splits in split_list:
                child_list.append({"child_name":splits,"child_type": request.stream_type,"total_source":totalSource,"source_list": source_list})

            data = {"total":len(child_list), "child_list":child_list}
        
        # 入库
        if RunConfig.IS_SAVE_DATABASE:
            save_data = ExtensionData(**params, data=data)
            save_data = dict(save_data)
            save_data["_id"] = UuidUtil.get_uuid()
            MongodbUtil.insert_one(collection_name=CollectionConfig.PRODUCT_EXTENSION, doc_content=save_data)
        
        # 记录返回日志
        LogUtil.log_json(describe="产品扩展请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        response = FalseResponse(data=data)
        return response

