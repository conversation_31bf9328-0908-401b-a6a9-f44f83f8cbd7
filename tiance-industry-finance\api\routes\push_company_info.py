#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :push_company_info.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:58:18
'''
from typing import List
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from utils.mongodb_util import MongodbUtil

router = APIRouter()

MONGODB_HOST = "***********"
MONGODB_PORT = 27017
MONGODB_USERNAME = "tansun-finance"
MONGODB_PASSWORD = "<EMAIL>"
MONGODB_DATABASE = "tiance_industry_finance"
MONGODB_AUTH_MECHANISM = "SCRAM-SHA-256"
MONGODB_TEMP_PDF_INFO_COLLECTION = "temp_file_coll"
MONGODB_COMPANY_INFO_COLL = "company_info_coll"


class PushCompanyInfo(BaseModel):
    cust_no: str
    cust_name: str
    uscd: str
    industry_environment: str
    idst_cd: str
    idst_cd_desc: str
    main_product: str


class PushCompanyInfoRequestBody(BaseModel):
    list: List[PushCompanyInfo]


class PushCompanyInfoResponseBody(BaseModel):
    code: int
    message: str

@router.post("/push_company_info", summary="推送企业信息")
async def push_company_info(body: PushCompanyInfoRequestBody) -> PushCompanyInfoResponseBody:
    try:
        success_list = []
        already_exist_list = []
        for company in body.list:
            company_already_exists = MongodbUtil.coll(MONGODB_COMPANY_INFO_COLL).find_one({"cust_no": company.cust_no})
            if company_already_exists is None:
                MongodbUtil.coll(MONGODB_COMPANY_INFO_COLL).insert_one(company.dict())
                success_list.append(str(company.cust_no))
            else:
                already_exist_list.append(str(company.cust_no))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"An error occurred when pushing company info: {e}")
    return PushCompanyInfoResponseBody(code=200, message="success")
