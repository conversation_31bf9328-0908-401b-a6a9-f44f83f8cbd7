#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :related_companies.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:55:32
'''

from fastapi import APIRouter
from api.routes.search_related_companies import search_related_companies
from entity.request_entity import RelatedCompaniesRequest, SearchRelatedCompaniesRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig

router = APIRouter()
@router.post("/related_companies", summary="关联客群详情")
async def related_companies(request: RelatedCompaniesRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        industry = request.industry
        LogUtil.info("1.查询产业链中心客群是否已生成")
        key_result = MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).find_one({"industry": industry})

        if key_result:
            key_companies = key_result.get("key_companies") # 获取中心客群列表
            LogUtil.info("2.获取关联客群")
            related_info = await search_related_companies(SearchRelatedCompaniesRequest(key_companies=key_companies))
            if related_info.code == 200:
                related_companies = related_info.data.get("result")
                
                data = {"company_list": related_companies}
                # 记录返回日志
                LogUtil.log_json(describe="中心客群详情请求返回结果", kwargs=data)
                return SuccessResponse(data=data)
            else:
                detail = f"失败详情：{related_info.data.get('error')}"
                LogUtil.error(msg=detail)
                data = {"error": detail}
                return FalseResponse(data=data)
        else:
            
            detail = f"失败详情：前置条件该产业链中心客群未在库中,库{CollectionConfig.KEY_COMPANIES},产业{request.industry}"
            LogUtil.error(msg=detail)
            data = {"error": detail}
            return FalseResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
    