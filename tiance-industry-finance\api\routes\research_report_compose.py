#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :research_report_compose.py
@Description  :
<AUTHOR>
@Date         :2024/11/13 17:55:32
'''

import asyncio
import time

from fastapi import APIRouter
from entity.request_entity import ResearchReportComposeRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from service.llm_service import Llm_Service
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from api.routes.research_report_structure import research_report_structure
from entity.request_entity import ResearchReportStructureRequest
from configs.prompt_config import PromptConfig
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from utils.text_utils import TextUtil
from configs.model_config import ModelConfig

router = APIRouter()


@router.post("/research_report_compose", summary="多研报产业链结构组合结果")
async def research_report_compose(request: ResearchReportComposeRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        LogUtil.info("1.根据mongodb_id列表调用research_report_structure接口得到多研报结果")
        multi_report_result = []
        structure_system_prompt = PromptConfig.RESEACH_REPORT_STRUCTURE_SYSTEM_PROMPT.format(industry=request.industry)
        # 创建并发执行的任务列表
        tasks = [research_report_structure(ResearchReportStructureRequest(
            mongodb_id=mongodb_id,
            model=request.model,
            k=request.k,
            industry=request.industry,
            system_prompt=structure_system_prompt
        )) for mongodb_id in request.mongodb_ids]

        for i in range(0, len(tasks), 16):
            end = min(i + 16, len(tasks))
            # 并发执行任务
            results = await asyncio.gather(*tasks[i:end])
            # 处理结果
            for single_report_info in results:
                if single_report_info.code == 200:
                    single_report_result = TextUtil.remove_think(single_report_info.data.get("result", ""))
                    multi_report_result.append(single_report_result)

        LogUtil.info("2.多研报结果拼接后调用大模型得到组合结果")
        multi_report_content = "产业链结构\n"
        multi_report_content += "\n\n\n\n产业链结构\n".join(multi_report_result)
        messages = []
        compose_system_prompt = request.system_prompt.format(industry=request.industry)
        messages.append(SystemMessage(compose_system_prompt))
        user_prompt = multi_report_content
        messages.append(UserMessage(user_prompt))
        messages = MessageConverter.convert_messages(messages)
        model = request.model
        llm_service = Llm_Service(model)
        answer = await llm_service.answer_question(messages, model, max_tokens=4096)
        data = {"mid_result": multi_report_content, "result": answer}
        # 记录返回日志
        LogUtil.log_json(describe="多研报产业链结构组合结果请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
