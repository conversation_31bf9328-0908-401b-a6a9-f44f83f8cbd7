#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :get_industry_chain.py
@Description  :
<AUTHOR>
@Date         :2025/03/03 10:47:25
'''

import json
from fastapi import APIRouter
from entity.request_entity import ResearchReportComposeRequest, ResearchReportExtensionReuquest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from api.routes.research_report_compose import research_report_compose
from configs.prompt_config import PromptConfig
from utils.text_utils import TextUtil
from utils.mongodb_util import MongodbUtil
from configs.run_config import RunConfig
from utils.uuid_util import UuidUtil
from configs.collection_config import CollectionConfig

router = APIRouter()
@router.post("/research_report_extension", summary="研报扩展获取产业链图谱")
async def research_report_extension(request: ResearchReportExtensionReuquest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request))
        model = RunConfig.MAX_LLM_MODEL_NAME
        mongodb_ids = sorted(request.mongodb_ids) # 重排序，便于存储后查询
        industry = request.industry

        LogUtil.info("1.查询关于mongodb_ids的研报文档是否已在产业链库中")
        result = MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).find_one({"industry":industry, "mongodb_ids": mongodb_ids})
        if not result:
            # 查询产业链库中是否已有其他研报的结果，有就删除
            if MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).find_one({"industry":industry}):
                MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).delete_one({"industry":industry})
            LogUtil.info("2.获取产业链结构")
            chain_info = await research_report_compose(ResearchReportComposeRequest(
                    model=model,
                    k=5,
                    mongodb_ids=mongodb_ids,
                    industry=industry,
                    system_prompt=PromptConfig.RESEACH_REPORT_COMPOSE_SYSTEM_PROMPT
                    ))

            LogUtil.info("3.解析并存储产业链结构")
            if chain_info.code == 200:
                chain_structure = TextUtil.remove_think(chain_info.data.get("result"))
                chain_structure = TextUtil.get_json_from_text(chain_structure)
                if chain_structure:
                    try:
                        chain_structure = json.loads(chain_structure.group())
                        save_data = {"_id": UuidUtil.get_uuid(), "industry": industry, "mongodb_ids": mongodb_ids, "chain_structure": chain_structure}
                        MongodbUtil.coll(CollectionConfig.CHAIN_STRUCTURE).insert_one(save_data)

                    except Exception as e:
                        LogUtil.error("chain_structure的json解析异常：" + str(e))
                        raise
        
        data = {"result": "获取产业链图谱成功"}
        # 记录返回日志
        LogUtil.log_json(describe="研报扩展获取产业链图谱请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)

