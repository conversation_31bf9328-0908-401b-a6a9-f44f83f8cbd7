#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :search_document.py
@Description  :
<AUTHOR>
@Date         :2025/03/07 15:07:45
'''

from fastapi import APIRouter
from entity.response_entity import SuccessResponse, FalseResponse
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig
from entity.request_entity import SearchDocumentRequest

router = APIRouter()
@router.post("/search_document", summary="文档检索")
async def search_document(request: SearchDocumentRequest) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="文档检索请求", kwargs=dict(request))
        
        # 查询条件
        query = {"$or": [{"title": {"$regex": request.doc_keyword}}, {"file_flag": {"$regex": request.doc_keyword}}]}
        # 返回字段
        projection = {"_id", "title", "source_url"}
        # 研报查询
        research_result = MongodbUtil.coll(CollectionConfig.RESEARCH_REPORT_LABEL_INFO).find(query, projection)
        # # 临时表查询
        # temp_result = MongodbUtil.coll("temp_file_coll").find(query, projection)
        # results = list(research_result) + list(temp_result)
        results = list(research_result)
        # 整合返回数据
        total = len(results)
        data = {"total": total, "document_list": results}
        # 记录返回日志
        LogUtil.log_json(describe="文档检索请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)
