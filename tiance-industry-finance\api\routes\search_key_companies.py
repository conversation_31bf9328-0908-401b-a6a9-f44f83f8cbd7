#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :search_key_companies.py
@Description  :
<AUTHOR>
@Date         :2025/02/28 14:43:01
'''
import asyncio
from datetime import datetime

from fastapi import APIRouter

from configs.milvus_config import MilvusConfig
from configs.model_config import ModelConfig
from configs.prompt_config import PromptConfig
from entity.request_entity import SearchKeyCompaniesRequest
from entity.response_entity import SuccessResponse, FalseResponse
from utils.cache_utils import CacheUtils
from utils.log_util import LogUtil
from service.kb_service import KbService
from service.llm_service import Llm_Service
from service.rerank_service import Rerank_Service
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil
from configs.collection_config import CollectionConfig

router = APIRouter()


@router.post("/search_key_companies", summary="查询单环节中心客群")
async def   search_key_companies(
    request: SearchKeyCompaniesRequest,
    is_use_cache: bool = False
) -> SuccessResponse | FalseResponse:
    try:
        # 记录日志
        LogUtil.log_json(describe="请求参数", kwargs=dict(request, is_use_cache=is_use_cache))

        # 初始化返回数据
        data = {"retrival_content": [], "result": ""}

        # 判断是否启用缓存
        if is_use_cache:
            # 启用缓存
            cached_data = CacheUtils.get_cache(
                CollectionConfig.CACHE_SEARCH_KEY_COMPANIES,
                key=request.model_dump_json()
            )
            if cached_data is None:
                is_use_cache = False
                LogUtil.info("未命中缓存，将调用大模型生成结果")
            else:
                LogUtil.info("命中缓存，立即返回结果")
                data = cached_data
        if not is_use_cache:  # 再次判断
            # 未启用缓存
            LogUtil.info("1.根据mongodb_id检索得到向量数据库信息(包括向量数据库名,向量文本块字段名,标题)")
            milvus_info = MongodbUtil.query_doc_by_id(request.collection_name, request.mongodb_id)
            milvus_collection_name = milvus_info.get("milvus_collection_name")  # milvus集合名
            title = milvus_info.get("file_title")
            file_source = milvus_info.get("data_source")
            milvus_field_name = MilvusConfig.MILVUS_DOC_CONTENT_FIELD_NAME

            LogUtil.info("2.检索向量数据库")
            kb_service = KbService()
            # 构造检索语句
            retrieval_sentence = request.industry.replace("产业链", "") + "的" + request.link

            # 发票、海关文档体量比较小，不需要 like 搜索，不然可能有些产品搜不到。例如去文档中有“行星减速器”，去找like “精密行星减速器” 会找不到。
            expr = f"file_title=='{title}' and file_source=='{file_source}'"
            if request.collection_name not in [CollectionConfig.INVOICE_LABEL_INFO,
                                               CollectionConfig.CUSTOMS_LABEL_INFO]:
                expr += f" and {milvus_field_name} like '%{request.link}%'"

            doc_list = await kb_service.search_knowledge_by_question(collection_name=milvus_collection_name,
                                                                     question=request.link,
                                                                     limit_top_k=request.k, expr=expr)

            LogUtil.info("3.拼接检索内容")
            content_list = []
            for item in doc_list:
                if item[milvus_field_name] not in content_list:
                    content_list.append(item[milvus_field_name])

            if not content_list:
                LogUtil.info("未检索到相关内容, 返回空")
                # 缓存
                CacheUtils.save_cache(
                    CollectionConfig.CACHE_SEARCH_KEY_COMPANIES,
                    key=request.model_dump_json(),
                    value=data
                )
                # 记录返回日志
                LogUtil.log_json(describe="查询单环节中心客群请求返回结果", kwargs=data)
                return SuccessResponse(data=data)

            if request.collection_name in [CollectionConfig.INVOICE_LABEL_INFO,
                                           CollectionConfig.CUSTOMS_LABEL_INFO]:
                # 发票、海关文档基本为markdown格式的表格，会使重排模型丢弃这些数据造成错误，故不重排
                rerank_content = content_list
            else:
                rerank_service = Rerank_Service()
                rerank_result = await rerank_service.rerank(retrieval_sentence, content_list)

                rerank_content = []
                for item in rerank_result["results"]:
                    if item["relevance_score"] >= 0.5:
                        rerank_content.append(content_list[item["index"]])

            if not rerank_content:
                LogUtil.info("重排后未得到到相关内容, 返回空")
                # 缓存
                CacheUtils.save_cache(
                    CollectionConfig.CACHE_SEARCH_KEY_COMPANIES,
                    key=request.model_dump_json(),
                    value=data
                )
                # 记录返回日志
                LogUtil.log_json(describe="查询单环节中心客群请求返回结果", kwargs=data)
                return SuccessResponse(data=data)

            LogUtil.info("4.组装提示词")
            messages = []
            messages.append(SystemMessage(request.system_prompt))
            user_prompt = "\n\n\n\n".join(rerank_content)
            messages.append(UserMessage(user_prompt))
            messages = MessageConverter.convert_messages(messages)

            LogUtil.info("5.调用大模型生成回答")
            model = request.model
            llm_service = Llm_Service(model)

            answer = await llm_service.answer_question(messages, model, max_tokens=4096)
            data = {"retrival_content": rerank_content, "result": answer}

            # 缓存
            CacheUtils.save_cache(
                CollectionConfig.CACHE_SEARCH_KEY_COMPANIES,
                key=request.model_dump_json(),
                value=data
            )

        # 记录返回日志
        LogUtil.log_json(describe="查询单环节中心客群请求返回结果", kwargs=data)
        return SuccessResponse(data=data)
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        data = {"error": detail}
        return FalseResponse(data=data)


if __name__ == "__main__":
    LogUtil.init('Test search_key_companies_compose.py')
    MongodbUtil.connect()
    MinIoUtil.connect()

    asyncio.run(search_key_companies(
        SearchKeyCompaniesRequest(
            model=ModelConfig.MAX_LLM_MODEL_NAME,
            collection_name=CollectionConfig.RESEARCH_REPORT_LABEL_INFO,
            mongodb_id="3ad4695c6cdc44e9ade70bf84d9c5049",
            k=5,
            industry="工业机器人",
            system_prompt=PromptConfig.SEARCH_KEY_COMPNIES_SYSTEM_PROMPT,
            link="减速器"
        ),
        is_use_cache=True
    ))

    pass
