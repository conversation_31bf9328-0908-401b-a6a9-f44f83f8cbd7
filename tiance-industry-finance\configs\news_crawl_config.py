#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:13
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : news_crawl_config.py
# @Description  : 资讯类数据爬取相关配置信息
"""

class NewsCrawlConfig(object):
    """
    资讯数据爬取的相关配置信息
    """

    MONGODB_COLLECTION = "news_label_info"  ## mongodb的collection
    REMOTE_PATH = "news/"   ## minio的存储 路径
    BUCKET_NAME = "tiance-industry-finance"  ##minio 的bucket名称
    LOCAL_PDF_PATH = 'file/news_pdf/'
    LOCAL_IMAGES_PATH = 'file/news_images/'

    CHORME_DRIVER_PATH = 'C:\Program Files\Google\Chrome\Application\chromedriver.exe' ##谷歌浏览器驱动的位置

    ## 百家号网页配置信息
    BAIJIAHAO = {
        'title_xpath':'/html/body/div[1]/div/div/div[2]/div[1]/div[1]/div[1]/div/div[1]',
        'source_path' : '//*[@id="header"]/div[2]/div/div/a/span',
        'release_time_xpath': '//*[@id="header"]/div[2]/div/div/span[1]',
        'distince_xpath':'//*[@id="header"]/div[2]/div/div/span[2]',
        'content_list_xpath': '//*[@id="ssr-content"]/div[2]/div[1]/div[1]/div[3]/div[1]',
        'list_type' : "div",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M",
        'crawl_method':'driver',
    }

    ## 搜狐网页配置信息
    SOHU = {
        'title_xpath': '//*[@id="article-container"]/div[2]/div[1]/div/div[1]/h1',
        'source_path': '//*[@id="user-info"]/h4/a',
        'release_time_xpath': '//*[@id="news-time"]',
        'distince_xpath' : '//*[@id="article-container"]/div[2]/div[1]/div/div[1]/div/div/span[2]',
        'content_list_xpath': '//*[@id="mp-editor"]',
        'list_type' : "p",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M",
        'crawl_method':'driver',
    }

    ## 雪球网页配置信息
    XUEQIU = {
        'title_xpath': '//*[@id="app"]/div[2]/article/h1',
        'source_path': '//*[@id="app"]/div[2]/div[1]/div[1]/a',
        'release_time_xpath': '//*[@id="app"]/div[2]/div[1]/div[2]/a/time',
        'distince_xpath' : '//*[@id="header"]/div[2]/div/div/span[2]',
        'content_list_xpath': '//*[@id="app"]/div[2]/article/div',
        'list_type' : "p",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M",
        'crawl_method':'request',
    }

    ## 36氪网页配置信息
    KR36 = {
        'title_xpath': '//*[@id="app"]/div/div[1]/div/div[2]/div[3]/div/div/div/div[1]/div/div[1]/div[1]/div/div/div[1]/div/h1',
        'source_path': '//*[@id="app"]/div/div[1]/div/div[2]/div[3]/div/div/div/div[1]/div/div[1]/div[1]/div/div/div[1]/div/div[1]/a',
        'release_time_xpath': '//*[@id="app"]/div/div[1]/div/div[2]/div[3]/div/div/div/div[1]/div/div[1]/div[1]/div/div/div[1]/div/div[1]/span',
        'distince_xpath' : '//*[@id="header"]/div[2]/div/div/span[2]',
        'content_list_xpath': '//*[@id="app"]/div/div[1]/div/div[2]/div[3]/div/div/div/div[1]/div/div[1]/div[1]/div/div/div[2]/div',
        'list_type' : "p",
        'img_url_header' : "",
        'date_format' : "%Y年%m月%d日 %H:%M",
        'crawl_method':'driver',
    }

    ## 中国日报网 天下专栏
    ChinaDaily_tx = {
        'title_xpath': '//*[@id="all"]/div[6]/div[1]/div[2]/div[1]',
        'source_path': '//*[@id="all"]/div[6]/div[1]/div[2]/div[2]/div[1]',
        'release_time_xpath': '//*[@id="all"]/div[6]/div[1]/div[2]/div[2]/div[2]',
        'distince_xpath' : '//*[@id="header"]/div[2]/div/div/span[2]',  ## 无
        'content_list_xpath': '//*[@id="Content"]',
        'list_type' : "p",
        'img_url_header' : "https:",
        'date_format' : "%Y年%m月%d日",
        'crawl_method':'driver',
    }

    ## 智研咨询 智研观点
    ZHIYAN_opinion = {
        'title_xpath': '/html/body/main/section/div/div[1]/article/h1',
        'source_path': '/html/body/main/section/div/div[1]/article/div[1]/div/span[2]',
        'release_time_xpath': '/html/body/main/section/div/div[1]/article/div[1]/div/span[1]',
        'distince_xpath' : '//*[@id="header"]/div[2]/div/div/span[2]',  ## 无
        'content_list_xpath': '/html/body/main/section/div/div[1]/article/div[2]/div[1]',
        'list_type' : "p",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M",
        'crawl_method':'driver',
    }

    ## 中国报告大厅
    Chinabgao = {
        'title_xpath': '/html/body/div[8]/div[1]/div/div[1]/h1',
        'source_path': '/html/body/div[8]/div[1]/div/div[1]/div/span[2]',
        'release_time_xpath': '/html/body/div[8]/div[1]/div/div[1]/div/span[1]',
        'distince_xpath' : '//*[@id="header"]/div[2]/div/div/span[2]',  ## 无
        'content_list_xpath': '/html/body/div[8]/div[1]/div/div[2]',
        'list_type' : "p",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M:%S",
        'crawl_method':'driver',
    }


    ## 腾讯网
    News_qq = {
        'title_xpath': '//*[@id="dc-normal-body"]/div[3]/div[1]/div[1]/div[2]/h1',
        'source_path': '//*[@id="article-author"]/div[1]/div[2]/a/p',
        'release_time_xpath': '//*[@id="article-author"]/div[1]/div[2]/p/span[1]',
        'distince_xpath' : '//*[@id="article-author"]/div[1]/div[2]/p/span[2]',  
        'content_list_xpath': '//*[@id="article-content"]/div[2]/div/section[2]/section/section',
        'list_type' : "section",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M:%S",
        'crawl_method':'driver',
    }

    ## 亿欧
    Yiou = {
        'title_xpath': '//*[@id="eo-app"]/div[1]/div/div[2]/div/article/h1',
        'source_path': '//*[@id="eo-app"]/div[1]/div/div[2]/div/article/div[1]/div/span',
        'release_time_xpath': '//*[@id="eo-app"]/div[1]/div/div[2]/div/article/div[1]/span[2]',
        'distince_xpath' : '//*[@id="article-author"]/div[1]/div[2]/p/span[2]',  ##无
        'content_list_xpath': '//*[@id="eo-app"]/div[1]/div/div[2]/div/article/article',
        'list_type' : "./p | ./h3",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M",
        'crawl_method':'driver',
    }


    ## 新华网
    Xinhua_news = {
        'title_xpath': '/html/body/div[10]/div[2]/div[3]/h1/span[1]',
        'source_path': '/html/body/div[10]/div[2]/div[2]',
        'release_time_xpath': '//*[@id="eo-app"]/div[1]/div/div[2]/div/article/div[1]/span[2]',
        'distince_xpath' : '//*[@id="article-author"]/div[1]/div[2]/p/span[2]',  ##无
        'content_list_xpath': '//*[@id="detailContent"]',
        'list_type' : "./p | ./div",
        'img_url_header' : "",
        'date_format' : "%Y-%m-%d %H:%M",
        'crawl_method':'driver',
    }