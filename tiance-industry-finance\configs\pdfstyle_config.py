from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:17
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : pdfstyle_config.py
# @Description  : pdf样式配置文件
"""

# 注册字体
pdfmetrics.registerFont(TTFont('SimSun', 'file/fonts/simsun.ttf'))

# 获取样式表
styles = getSampleStyleSheet()

# 添加自定义样式
## 正文格式
styles.add(ParagraphStyle(
    name='ChineseStyle',
    fontName='SimSun',  # 使用注册的中文字体
    fontSize=12,
    leading=14,
    spaceAfter=12
))

## 标题格式
styles.add(ParagraphStyle(
    name='titleStyle', 
    fontName='SimSun',  # 使用注册的中文字体
    fontSize=16,
    leading=18,
    spaceAfter=20,
    alignment=1, 
))
