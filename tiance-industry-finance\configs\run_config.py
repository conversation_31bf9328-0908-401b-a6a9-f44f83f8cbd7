#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :run_config.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 15:24:46
'''

class RunConfig(object):
    """
    系统运行配置
    """
    # ************1、模型相关配置************ #
    # OPENAI规范模型接口
    LLM_MODEL_CHOICES = {"qwen2.5-72B": "http://***********:9997/v1",
                         "DeepSeek-R1-Distill-Qwen-32B": "http://***********:9997/v1",
                         "DeepSeek-R1-671B": "http://***********:11998/v1",
                         "Qwen3-32B": "http://***********:9997/v1"
                         }
    OPENAI_API_KEY = 'CHAIN_EXTENSION'
    OPENAI_API_BASE = "http://***********:9997/v1"

    MAX_LLM_MODEL_NAME = "qwen2.5-72B"

    # 使用的向量嵌入模型
    EMBED_MODEL_NAME = "bge-large-zh-v1.5"
    
    # 重排模型url
    RERANK_MODEL_URL = "http://***********:9997/v1/rerank"
    # 使用重排的打分模型
    RERANK_MODEL_NAME = "bge-reranker-large"
    
    # 是否入库
    IS_SAVE_DATABASE = True
    
    # 历史会话对长度
    HISTORY_LEN = 3

    # 是否使用原始数据做标签融合
    IS_MERGE_ORIGIN_LABEL = False

    # 历史会话对长度
    HISTORY_LEN = 3
    # Xinference接口
    XINFERENCE_API = "http://***********:9997"
