# 公司数据总量查询接口文档

## 概述

本接口提供公司数据的统计查询功能，包括公司数据总量、上市公司总数、当日更新总数等统计信息。

## 接口列表

### 1. 公司数据总量查询（POST）

**接口地址：** `POST /company_data_stats`

**功能说明：**
- 查询公司数据总量（status=1的记录总数）
- 查询上市公司总数（StockAbbr不为空的记录总数）
- 查询当日更新总数（update_time大于当日0点的记录总数）

**请求参数：**
```json
{}
```
*注：当前版本无需传入参数*

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total_count": 15000,
        "listed_count": 3500,
        "today_updated_count": 120,
        "query_date": "2025-01-23"
    }
}
```

**响应字段说明：**
- `total_count`: 公司数据总量
- `listed_count`: 上市公司总数
- `today_updated_count`: 当日更新总数
- `query_date`: 查询日期

### 2. 公司数据总量查询（GET）

**接口地址：** `GET /company_data_stats_simple`

**功能说明：**
与POST接口功能相同，但使用GET方式，无需传入请求体。

**请求参数：** 无

**响应示例：** 同POST接口

### 3. 公司数据详细统计查询

**接口地址：** `POST /company_data_stats_detailed`

**功能说明：**
获取更详细的公司数据统计信息，包含基础统计和扩展统计。

**请求参数：**
```json
{}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total_count": 15000,
        "listed_count": 3500,
        "today_updated_count": 120,
        "query_date": "2025-01-23",
        "statistics_type": "detailed",
        "description": "公司数据详细统计信息"
    }
}
```

## 错误响应

当接口调用失败时，返回格式如下：

```json
{
    "code": 500,
    "message": "false",
    "data": {
        "error": "具体错误信息",
        "error_type": "company_data_stats_error"
    }
}
```

## 统计规则说明

### 1. 公司数据总量
- 统计条件：`status = 1`
- 说明：只统计状态为正常的公司记录

### 2. 上市公司总数
- 统计条件：`status = 1 AND StockAbbr IS NOT NULL AND StockAbbr != '' AND StockAbbr != 'NULL'`
- 说明：统计有股票简称的公司记录

### 3. 当日更新总数
- 统计条件：`status = 1 AND update_time >= 当日0点`
- 说明：统计当天有更新的公司记录

## 使用示例

### Python 请求示例

```python
import requests
import json

# POST 方式
url = "http://your-server:port/company_data_stats"
response = requests.post(url, json={})
result = response.json()
print(json.dumps(result, indent=2, ensure_ascii=False))

# GET 方式
url = "http://your-server:port/company_data_stats_simple"
response = requests.get(url)
result = response.json()
print(json.dumps(result, indent=2, ensure_ascii=False))
```

### curl 请求示例

```bash
# POST 方式
curl -X POST "http://your-server:port/company_data_stats" \
     -H "Content-Type: application/json" \
     -d '{}'

# GET 方式
curl -X GET "http://your-server:port/company_data_stats_simple"
```

## 技术实现

### 架构设计
- **API层**: `api/routes/company_data_stats.py` - 处理HTTP请求和响应
- **Service层**: `service/company_data_stats_service.py` - 业务逻辑处理
- **Model层**: `entity/mysql_entity.py` - 数据模型定义
- **工具层**: `utils/sql_util.py` - 数据库操作工具

### 数据库表结构
使用 `CompanyMain` 表，主要字段：
- `CompanyCode`: 企业编号（主键）
- `ChiName`: 中文名称
- `StockAbbr`: 股票简称
- `create_time`: 创建时间
- `update_time`: 更新时间
- `status`: 状态（0-删除，1-正常）

## 测试

运行测试脚本：
```bash
python script/test_company_data_stats.py
```

## 注意事项

1. 确保数据库连接配置正确
2. 确保 `CompanyMain` 表存在且包含必要字段
3. 接口返回的统计数据基于查询时的实时数据
4. 当日更新统计基于服务器当前时区的日期
