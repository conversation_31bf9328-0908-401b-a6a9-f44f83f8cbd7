# 公司数据总量查询接口实现说明

## 项目概述

按照 tiance-base 项目的架构风格，在 tiance-industry-finance 项目中实现了公司数据总量查询接口，采用 API、Service、Model 分离的设计模式。

## 功能特性

### 核心功能
1. **公司数据总量查询** - 统计 status=1 的公司记录总数
2. **上市公司总数查询** - 统计 StockAbbr 不为空的公司记录总数  
3. **当日更新总数查询** - 统计 update_time 大于当日0点的公司记录总数

### 接口设计
- 提供 POST 和 GET 两种请求方式
- 支持基础统计和详细统计
- 统一的响应格式和错误处理

## 文件结构

```
tiance-industry-finance/
├── entity/
│   ├── mysql_entity.py          # 更新了 CompanyMain 模型
│   └── request_entity.py        # 新增 CompanyDataStatsRequest
├── service/
│   └── company_data_stats_service.py  # 新增服务层
├── api/routes/
│   └── company_data_stats.py    # 新增 API 路由
├── utils/
│   └── sql_util.py             # 新增带条件的 count 方法
├── script/
│   ├── test_company_data_stats.py      # 服务层测试脚本
│   └── test_api_company_data_stats.py  # API 测试脚本
├── examples/
│   └── company_data_stats_example.py   # 使用示例
├── docs/
│   ├── company_data_stats_api.md       # API 文档
│   └── company_data_stats_implementation.md  # 实现说明
└── main.py                     # 更新了路由注册
```

## 技术实现

### 1. Model 层 (entity/mysql_entity.py)

更新了 `CompanyMain` 模型，添加了缺失的字段：

```python
class CompanyMain(Base):
    # ... 原有字段 ...
    create_time = Column(DateTime, nullable=False, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='更新时间')
    status = Column(TINYINT(1), nullable=False, default=1, comment='状态：0-删除，1-正常')
```

### 2. Service 层 (service/company_data_stats_service.py)

实现了业务逻辑：

```python
class CompanyDataStatsService:
    @staticmethod
    def get_company_data_statistics():
        # 1. 查询公司数据总量
        total_count = SQLUtil.count_records_with_condition(CompanyMain, CompanyMain.status == 1)
        
        # 2. 查询上市公司总数
        listed_count = SQLUtil.count_records_with_condition(CompanyMain, and_(...))
        
        # 3. 查询当日更新总数
        today_updated_count = SQLUtil.count_records_with_condition(CompanyMain, and_(...))
```

### 3. API 层 (api/routes/company_data_stats.py)

提供了三个接口：

```python
@router.post("/company_data_stats")           # POST 方式基础统计
@router.get("/company_data_stats_simple")     # GET 方式基础统计  
@router.post("/company_data_stats_detailed")  # POST 方式详细统计
```

### 4. 工具层 (utils/sql_util.py)

新增了带条件的计数方法：

```python
@staticmethod
def count_records_with_condition(model, condition):
    """根据条件查询表中的记录数"""
    count = SQLUtil.__session.query(model).filter(condition).count()
    return count
```

## 统计规则

### 1. 公司数据总量
- **条件**: `status = 1`
- **说明**: 只统计状态为正常的公司记录

### 2. 上市公司总数
- **条件**: `status = 1 AND StockAbbr IS NOT NULL AND StockAbbr != '' AND StockAbbr != 'NULL'`
- **说明**: 统计有股票简称的公司记录

### 3. 当日更新总数
- **条件**: `status = 1 AND update_time >= 当日0点`
- **说明**: 统计当天有更新的公司记录

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success", 
    "data": {
        "total_count": 15000,
        "listed_count": 3500,
        "today_updated_count": 120,
        "query_date": "2025-01-23"
    }
}
```

### 错误响应
```json
{
    "code": 500,
    "message": "false",
    "data": {
        "error": "具体错误信息",
        "error_type": "company_data_stats_error"
    }
}
```

## 测试方法

### 1. 服务层测试
```bash
python script/test_company_data_stats.py
```

### 2. API 测试
```bash
python script/test_api_company_data_stats.py
```

### 3. 使用示例
```bash
python examples/company_data_stats_example.py
```

## 部署说明

### 1. 启动服务
```bash
python main.py
```

### 2. 访问接口
- API 文档: http://localhost:8000/docs
- 基础统计: http://localhost:8000/company_data_stats_simple

### 3. 配置检查
- 确保数据库连接配置正确 (`configs/mysql_config.py`)
- 确保 `CompanyMain` 表存在且包含必要字段
- 确保服务端口配置正确 (`configs/api_config.py`)

## 扩展建议

### 1. 功能扩展
- 添加按行业分类统计
- 添加按地区分类统计
- 添加时间范围查询
- 添加数据趋势分析

### 2. 性能优化
- 添加缓存机制
- 优化数据库查询
- 添加分页支持
- 添加异步处理

### 3. 监控告警
- 添加接口调用监控
- 添加数据异常告警
- 添加性能指标监控

## 注意事项

1. **数据库连接**: 确保数据库连接池配置合理
2. **错误处理**: 所有数据库操作都有异常处理
3. **日志记录**: 关键操作都有日志记录
4. **资源管理**: 确保数据库连接正确关闭
5. **时区处理**: 当日更新统计基于服务器时区

## 版本信息

- **创建时间**: 2025-01-23
- **版本**: v1.0.0
- **兼容性**: 基于 tiance-base 架构风格
- **依赖**: SQLAlchemy, FastAPI, Pydantic
