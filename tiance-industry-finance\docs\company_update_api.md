# 公司数据修改接口文档

## 概述

本接口提供公司数据的修改功能，支持修改公司的中文名称、企业别称、英文全称等信息。接口具有完善的数据验证和曾用名管理功能。

## 接口列表

### 1. 公司数据修改

**接口地址：** `POST /company_update`

**功能说明：**
- 修改公司的中文名称、企业别称、英文全称
- ChiName（中文名称）为必填项，不能为空
- ChiName修改后自动将原有值补充到PreName中
- PreName中的数据以逗号分割，自动去重
- 自动更新update_time
- 修改ChiName时会进行查重验证

**请求参数：**
```json
{
    "company_code": "COMP001",
    "chi_name": "新公司名称",
    "chi_name_abbr": "新公司简称",
    "eng_name": "New Company Name"
}
```

**参数说明：**
- `company_code`: 企业编号（必填）
- `chi_name`: 中文名称（必填）
- `chi_name_abbr`: 企业别称（可选）
- `eng_name`: 英文全称（可选）

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "company_code": "COMP001",
        "old_chi_name": "原公司名称",
        "new_chi_name": "新公司名称",
        "chi_name_abbr": "新公司简称",
        "eng_name": "New Company Name",
        "pre_name": "原公司名称,其他曾用名",
        "update_time": "2025-01-23 11:30:00",
        "message": "公司信息修改成功"
    }
}
```

### 2. 获取公司信息

**接口地址：** `GET /company_info/{company_code}`

**功能说明：**
根据企业编号获取公司的详细信息

**请求参数：**
- `company_code`: 企业编号（路径参数）

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "company_code": "COMP001",
        "chi_name": "公司名称",
        "chi_name_abbr": "公司简称",
        "pre_name": "曾用名1,曾用名2",
        "eng_name": "Company Name",
        "eng_name_abbr": "Company",
        "stock_abbr": "STOCK",
        "brand_name": "品牌名称",
        "large_model_abbr": "大模型简称",
        "other_abbr": "其他简称",
        "credit_code": "91110000000000000X",
        "create_time": "2025-01-01 00:00:00",
        "update_time": "2025-01-23 11:30:00",
        "status": 1
    }
}
```

### 3. 检查公司名称重复

**接口地址：** `POST /company_name_check`

**功能说明：**
检查指定的中文名称是否已被其他公司使用

**请求参数：**
- `chi_name`: 要检查的中文名称（查询参数）
- `exclude_company_code`: 排除的企业编号（可选，查询参数）

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "is_duplicate": false,
        "chi_name": "测试公司名称",
        "exclude_company_code": "COMP001",
        "message": "名称 '测试公司名称' 可以使用"
    }
}
```

## 错误响应

当接口调用失败时，返回格式如下：

```json
{
    "code": 500,
    "message": "false",
    "data": {
        "error": "具体错误信息",
        "error_type": "validation_error",
        "company_code": "COMP001"
    }
}
```

**错误类型说明：**
- `validation_error`: 参数验证错误
- `company_update_error`: 公司修改错误
- `company_query_error`: 公司查询错误
- `name_check_error`: 名称检查错误

## 业务规则

### 1. 数据验证规则
- **企业编号**: 不能为空
- **中文名称**: 不能为空，修改时需要查重
- **企业别称**: 可选字段
- **英文全称**: 可选字段

### 2. 曾用名管理规则
- 当中文名称发生变化时，原名称自动添加到曾用名中
- 曾用名以逗号分割存储
- 自动去重，避免重复的曾用名
- 新名称不会出现在曾用名中

### 3. 查重规则
- 中文名称在同一状态（status=1）下必须唯一
- 查重时排除当前公司自身
- 只对正常状态的公司进行查重

### 4. 更新规则
- 自动更新 update_time 字段
- 只更新传入的字段，未传入的字段保持不变
- 使用事务确保数据一致性

## 使用示例

### Python 请求示例

```python
import requests
import json

base_url = "http://your-server:port"

# 1. 获取公司信息
company_code = "COMP001"
response = requests.get(f"{base_url}/company_info/{company_code}")
print(json.dumps(response.json(), indent=2, ensure_ascii=False))

# 2. 检查名称重复
params = {
    "chi_name": "新公司名称",
    "exclude_company_code": "COMP001"
}
response = requests.post(f"{base_url}/company_name_check", params=params)
print(json.dumps(response.json(), indent=2, ensure_ascii=False))

# 3. 修改公司信息
data = {
    "company_code": "COMP001",
    "chi_name": "新公司名称",
    "chi_name_abbr": "新简称",
    "eng_name": "New Company Name"
}
response = requests.post(f"{base_url}/company_update", json=data)
print(json.dumps(response.json(), indent=2, ensure_ascii=False))
```

### curl 请求示例

```bash
# 1. 获取公司信息
curl -X GET "http://your-server:port/company_info/COMP001"

# 2. 检查名称重复
curl -X POST "http://your-server:port/company_name_check?chi_name=新公司名称&exclude_company_code=COMP001"

# 3. 修改公司信息
curl -X POST "http://your-server:port/company_update" \
     -H "Content-Type: application/json" \
     -d '{
         "company_code": "COMP001",
         "chi_name": "新公司名称",
         "chi_name_abbr": "新简称",
         "eng_name": "New Company Name"
     }'
```

## 技术实现

### 架构设计
- **API层**: `api/routes/company_update.py` - 处理HTTP请求和响应
- **Service层**: `service/company_update_service.py` - 业务逻辑处理
- **Model层**: `entity/mysql_entity.py` - 数据模型定义
- **工具层**: `utils/sql_util.py` - 数据库操作工具

### 数据库操作
- 使用 `SQLUtil.query_by_column` 进行条件查询
- 使用 `SQLUtil.bulk_update` 进行批量更新
- 支持事务回滚和错误处理

### 曾用名处理算法
```python
def _update_pre_name(old_pre_name, old_chi_name, new_chi_name):
    # 如果名称未变化，不更新曾用名
    if old_chi_name == new_chi_name:
        return old_pre_name
    
    # 解析现有曾用名
    pre_names = [name.strip() for name in old_pre_name.split(',') if name.strip()]
    
    # 添加原名称到曾用名
    if old_chi_name and old_chi_name not in pre_names:
        pre_names.append(old_chi_name)
    
    # 去重并排除新名称
    unique_names = [name for name in pre_names if name != new_chi_name]
    
    return ','.join(unique_names) if unique_names else None
```

## 测试方法

### 1. 服务层测试
```bash
python script/test_company_update.py
```

### 2. API 测试
```bash
python script/test_api_company_update.py
```

### 3. 使用示例
```bash
python examples/company_update_example.py
```

## 注意事项

1. **数据备份**: 修改前建议备份重要数据
2. **并发控制**: 高并发环境下注意数据一致性
3. **权限控制**: 生产环境需要添加适当的权限验证
4. **日志记录**: 所有修改操作都有详细的日志记录
5. **错误处理**: 完善的异常处理和错误提示

## 版本信息

- **创建时间**: 2025-01-23
- **版本**: v1.0.0
- **兼容性**: 基于 tiance-base 架构风格
- **依赖**: SQLAlchemy, FastAPI, Pydantic
