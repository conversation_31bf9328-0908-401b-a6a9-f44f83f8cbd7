#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :company_relation_entity.py
@Description  :存放招股说明书解析的相关实体类
<AUTHOR>
@Date         :2024/12/31 09:01:57
'''

from pydantic import BaseModel, Field

# 实体类，用于定义招股说明书解析接口的请求参数
class ComInfoEntity(BaseModel):
    """
    实体类，用于招股书关联公司信息提取接口的参数验证和解析。
    """
    pdf_name: str = Field(default="机器人", example="机器人", description="招股书文件关键字")

class ComInfoEntityWithFile(BaseModel):
    """
    实体类，用于招股书关联公司信息提取接口的参数验证和解析。
    """
    pdf_name: str = Field(default="机器人", example="机器人", description="招股书文件关键字")
    pdf_filename :str = Field(..., embed=True, example="2023", description="依据年报")
    industry_chain :str = Field(..., embed=True, example="工业机器人", description="产业链:上一流水线传递")
   


class SuccessResponse(BaseModel):
    """
    成功响应的实体类。
    """
    code: int = Field(200, example=200, description="响应码")
    message: str = Field("success", example="success", description="响应信息")
    data: dict = Field(..., example={}, description="响应数据")

class FalseResponse(BaseModel):
    """
    失败响应的实体类。
    """
    code: int = Field(500, example=500, description="响应码")
    message: str = Field("false", example="success", description="响应信息")
    data: dict = Field(..., example={"error": "detail"}, description="响应数据")
    
    