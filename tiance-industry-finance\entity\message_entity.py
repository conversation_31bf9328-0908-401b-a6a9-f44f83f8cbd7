#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :message_entity.py
@Description  :大模型模型对话消息实体
<AUTHOR>
@Date         :2024/11/15 20:51:30
'''


# 基础消息类
class Message:
    def __init__(self, role: str, content: str):
        self.role = role
        self.content = content

    def to_dict(self):
        return {
            "role": self.role,
            "content": self.content
        }


# 系统消息类
class SystemMessage(Message):
    def __init__(self, content: str):
        super().__init__(role="system", content=content)


# AI消息类
class AssistantMessage(Message):
    def __init__(self, content: str):
        super().__init__(role="assistant", content=content)


# 用户消息类
class UserMessage(Message):
    def __init__(self, content: str):
        super().__init__(role="user", content=content)


# 消息转换器类
class MessageConverter:

    # 将类实例列表转换为字典列表
    @staticmethod
    def convert_messages(messages) -> list[dict]:
        return [message.to_dict() for message in messages]
