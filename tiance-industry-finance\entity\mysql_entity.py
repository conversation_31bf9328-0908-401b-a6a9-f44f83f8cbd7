#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/30 9:51
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : mysql_entity.py
# @Description  : sqlalchemy所需要的ORM模型实体，与数据库表对应
"""

from sqlalchemy import CHAR, BigInteger, Column, DateTime, Integer, String, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func


# 创建 SQLAlchemy 的基类
Base = declarative_base()
class CompanyMain(Base):
    """企业主信息表 ORM 模型"""
    __tablename__ = 'CompanyMain'
    __table_args__ = {
        'comment': '企业主信息表',
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }
    CompanyCode = Column(String(100),primary_key=True,nullable=False,comment='企业编号（业务唯一标识）')
    ChiName = Column(String(100),nullable=False,comment='中文名称')
    ChiNameAbbr = Column(String(100),nullable=True,comment='企业别称')
    PreName = Column(Text,nullable=True,comment='曾用名')
    EngName = Column(String(200),nullable=True,comment='英文全称')
    EngNameAbbr = Column(String(100),nullable=True,comment='英文简称')
    StockAbbr = Column(String(100),nullable=True,comment='股票简称') 
    BrandName = Column(String(500),nullable=True,comment='产品名称')
    LargeModelAbbr = Column(String(100),nullable=True,comment='大模型简称')
    OtherAbbr = Column(String(100),nullable=True,comment='其他简称')
    CreditCode = Column(String(100),nullable=True,comment='统一社会信用代码')
    create_time = Column(DateTime, nullable=False, default=func.now(), comment='创建时间')
    update_time = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now(), comment='更新时间')
    status = Column(Integer, nullable=False, default=1, comment='状态：0-删除，1-正常')

class Order(Base):
    """订单表 ORM 模型"""
    __tablename__ = 'Order'
    __table_args__ = {
        'comment': '订单表',
        'mysql_engine': 'InnoDB',
        'mysql_charset': 'utf8mb4',
        'mysql_collate': 'utf8mb4_unicode_ci'
    }
    OrderCode = Column(String(100),primary_key=True,nullable=False,comment='订单编号（业务唯一标识）')
    CompanyCode = Column(String(100),nullable=False,comment='企业编号')

class EP_CompanyMain(Base):
    __tablename__ = 'EP_CompanyMain'
    
    ID = Column(BigInteger, primary_key=True, comment='ID')
    EnterpriseCode = Column(String(12), nullable=False, unique=True, comment='企业编码')
    CompanyCode = Column(Integer, nullable=False, comment='企业编号')
    ChiName = Column(String(100), nullable=True, comment='中文名称')
    ChiNameAbbr = Column(String(100), nullable=True, comment='中文简称')
    EngName = Column(String(100), nullable=True, comment='英文名称')
    EngNameAbbr = Column(String(100), nullable=True, comment='英文简称')
    EstablishmentDate = Column(DateTime, nullable=True, comment='成立日期')
    CreditCode = Column(String(18), nullable=True, comment='统一社会信用代码')
    RegNumber = Column(String(25), nullable=True, comment='注册号')
    InsertTime = Column(DateTime, nullable=False, comment='发布时间')
    UpdateTime = Column(DateTime, nullable=False, comment='更新时间')
    JSID = Column(BigInteger, nullable=False, unique=True, comment='JSID')
    DataJson = Column(Text, nullable=True, comment='DataJson')


class EP_PatentInfo(Base):
    __tablename__ = 'EP_PatentInfo'

    ID = Column(BigInteger, primary_key=True, comment='ID')
    PatentName = Column(String(1000, collation='utf8mb4_general_ci'), nullable=True, comment='专利名称')
    RequestNumOri = Column(String(100, collation='utf8mb4_general_ci'), nullable=True, comment='申请号(原始)')
    RequestNum = Column(String(100, collation='utf8mb4_general_ci'), nullable=False, comment='申请号(标准)')
    RequestDate = Column(DateTime, nullable=True, comment='申请日')
    OuthorNum = Column(String(300, collation='utf8mb4_general_ci'), nullable=True, comment='公布公告号')
    OuthorDate = Column(DateTime, nullable=True, comment='公布公告日')
    AuthorizeNum = Column(String(300, collation='utf8mb4_general_ci'), nullable=True, comment='授权公告号')
    AuthorizeDate = Column(DateTime, nullable=True, comment='授权公告日')
    OuthorChangeDate = Column(DateTime, nullable=True, comment='发明公布更正文献出版日')
    OuthorChangeNum = Column(String(300, collation='utf8mb4_general_ci'), nullable=True, comment='发明公布更正公布号')
    AuthorizeChangeDate = Column(DateTime, nullable=True, comment='授权更正文献出版日')
    AuthorizeChangeNum = Column(String(300, collation='utf8mb4_general_ci'), nullable=True, comment='授权更正公布号')
    IPCNum = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='IPC分类号')
    PatentOwner = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='当前申请(专利权)人')
    PatentOwnerOri = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='原始专利权人')
    Designer = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='发明人')
    Priority = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='优先权')
    Address = Column(String(1000, collation='utf8mb4_general_ci'), nullable=True, comment='专利权人地址')
    Zipcode = Column(String(100, collation='utf8mb4_general_ci'), nullable=True, comment='专利权人邮编')
    Content = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='专利摘要')
    LastStatus = Column(String(600, collation='utf8mb4_general_ci'), nullable=True, comment='最新专利状态')
    LastStatusDate = Column(DateTime, nullable=True, comment='最新专利状态发生日期')
    PatentType = Column(String(200, collation='utf8mb4_general_ci'), nullable=True, comment='专利类型')
    PatentTypeCode = Column(BigInteger, nullable=True, comment='专利类型代码')  # 注意: 原表定义为int，但SQLAlchemy建议使用BigInteger
    IfAuthorize = Column(BigInteger, nullable=True, comment='是否已授权')  # 注意: 原表定义为int，但SQLAlchemy建议使用BigInteger
    PCTPubContent = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='PCT公布数据')
    PCTDate = Column(DateTime, nullable=True, comment='PCT进入国家阶段日')
    PCTReqContent = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='PCT申请数据')
    Agent = Column(String(1000, collation='utf8mb4_general_ci'), nullable=True, comment='专业代理机构')
    AgentID = Column(String(12, collation='utf8mb4_general_ci'), nullable=True, comment='专业代理机构ID')
    AgentPeople = Column(String(1000, collation='utf8mb4_general_ci'), nullable=True, comment='代理人')
    ContentPic = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='专利摘要图片')
    PicURL = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='专利图片')
    ApplyStateCode = Column(String(600, collation='utf8mb4_general_ci'), nullable=True, comment='申请国家')
    IfHistory = Column(BigInteger, nullable=True, comment='是否历史')  # 注意: 原表定义为int，但SQLAlchemy建议使用BigInteger
    InsertTime = Column(DateTime, nullable=False, comment='发布时间')
    UpdateTime = Column(DateTime, nullable=False, comment='更新时间')
    JSID = Column(BigInteger, nullable=False, comment='JSID')
    DataJson = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='DataJson')


class EP_PatentInfoRelated(Base):
    __tablename__ = 'EP_PatentInfoRelated'
    
    ID = Column(BigInteger, primary_key=True, comment='ID')
    RequestNum = Column(String(100, collation='utf8mb4_general_ci'), nullable=False, comment='标准化后的申请号')
    RelatedKind = Column(BigInteger, nullable=True, comment='当事人种类')  # 注意: 原表定义为int，但SQLAlchemy建议使用BigInteger
    RelatedName = Column(String(500, collation='utf8mb4_general_ci'), nullable=False, comment='当事人姓名')
    RelatedCode = Column(String(12, collation='utf8mb4_general_ci'), nullable=True, comment='当事人ID')
    RelatedRole = Column(String(100, collation='utf8mb4_general_ci'), nullable=True, comment='当事人角色')
    InsertTime = Column(DateTime, nullable=False, comment='发布时间')
    UpdateTime = Column(DateTime, nullable=False, comment='更新时间')
    JSID = Column(BigInteger, nullable=False, comment='JSID')
    DataJson = Column(Text(collation='utf8mb4_general_ci'), nullable=True, comment='DataJson')
