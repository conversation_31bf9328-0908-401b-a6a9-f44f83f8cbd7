#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 10:00:00
# <AUTHOR> Assistant
# @File         : company_data_stats_example.py
# @Description  : 公司数据总量查询接口使用示例
"""

import requests
import json
from datetime import datetime


class CompanyDataStatsClient:
    """公司数据统计客户端"""
    
    def __init__(self, base_url="http://localhost:9029"):
        """
        初始化客户端
        :param base_url: 服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
    
    def get_basic_stats(self):
        """
        获取基础统计数据（POST方式）
        :return: 统计数据字典
        """
        url = f"{self.base_url}/company_data_stats"
        try:
            response = requests.post(url, json={}, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    
    def print_stats(self, stats_data, title="统计数据"):
        """
        格式化打印统计数据
        :param stats_data: 统计数据
        :param title: 标题
        """
        print(f"\n{title}")
        print("=" * 50)
        
        if "error" in stats_data:
            print(f"错误: {stats_data['error']}")
            return
        
        if stats_data.get("code") == 200:
            data = stats_data.get("data", {})
            print(f"查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"查询日期: {data.get('query_date', 'N/A')}")
            print(f"公司数据总量: {data.get('total_count', 0):,}")
            print(f"上市公司总数: {data.get('listed_count', 0):,}")
            print(f"当日更新总数: {data.get('today_updated_count', 0):,}")
            
            # 计算上市公司占比
            total = data.get('total_count', 0)
            listed = data.get('listed_count', 0)
            if total > 0:
                ratio = (listed / total) * 100
                print(f"上市公司占比: {ratio:.2f}%")
            
            # 显示其他字段
            for key, value in data.items():
                if key not in ['total_count', 'listed_count', 'today_updated_count', 'query_date']:
                    print(f"{key}: {value}")
        else:
            print(f"请求失败: {stats_data.get('message', 'Unknown error')}")
            if 'data' in stats_data and 'error' in stats_data['data']:
                print(f"错误详情: {stats_data['data']['error']}")


def main():
    """主函数 - 演示如何使用公司数据统计接口"""
    
    print("公司数据总量查询接口使用示例")
    print("=" * 60)
    
    # 创建客户端（请根据实际服务器地址修改）
    client = CompanyDataStatsClient("http://localhost:9029")
    
    # 1. 获取基础统计数据（POST方式）
    print("\n1. 获取基础统计数据（POST方式）")
    basic_stats = client.get_basic_stats()
    client.print_stats(basic_stats, "基础统计数据（POST）")
    
    # 2. 获取基础统计数据（GET方式）
    print("\n2. 获取基础统计数据（GET方式）")
    simple_stats = client.get_basic_stats_simple()
    client.print_stats(simple_stats, "基础统计数据（GET）")
    
    # 3. 获取详细统计数据
    print("\n3. 获取详细统计数据")
    detailed_stats = client.get_detailed_stats()
    client.print_stats(detailed_stats, "详细统计数据")
    
    # 4. 数据分析示例
    print("\n4. 数据分析示例")
    print("=" * 50)
    
    if basic_stats.get("code") == 200:
        data = basic_stats.get("data", {})
        
        total = data.get('total_count', 0)
        listed = data.get('listed_count', 0)
        updated_today = data.get('today_updated_count', 0)
        
        print(f"数据质量分析:")
        print(f"  - 总公司数量: {total:,}")
        print(f"  - 上市公司数量: {listed:,}")
        print(f"  - 非上市公司数量: {total - listed:,}")
        
        if total > 0:
            listed_ratio = (listed / total) * 100
            updated_ratio = (updated_today / total) * 100
            print(f"  - 上市公司占比: {listed_ratio:.2f}%")
            print(f"  - 当日更新占比: {updated_ratio:.2f}%")
        
        print(f"\n数据活跃度:")
        if updated_today > 0:
            print(f"  - 今日有 {updated_today:,} 家公司数据更新")
            print(f"  - 数据更新活跃度: {'高' if updated_ratio > 1 else '中' if updated_ratio > 0.1 else '低'}")
        else:
            print(f"  - 今日暂无公司数据更新")


def demo_error_handling():
    """演示错误处理"""
    print("\n" + "=" * 60)
    print("错误处理演示")
    print("=" * 60)
    
    # 使用错误的URL演示错误处理
    client = CompanyDataStatsClient("http://localhost:9999")  # 错误端口
    
    print("\n尝试连接到错误的服务器地址...")
    stats = client.get_basic_stats()
    client.print_stats(stats, "错误处理示例")


if __name__ == "__main__":
    # 运行主示例
    main()
    
    # 演示错误处理
    demo_error_handling()
    
    print("\n" + "=" * 60)
    print("示例运行完成")
    print("=" * 60)
    
    print("\n使用说明:")
    print("1. 确保服务器已启动: python main.py")
    print("2. 修改客户端中的服务器地址")
    print("3. 运行此示例: python examples/company_data_stats_example.py")
