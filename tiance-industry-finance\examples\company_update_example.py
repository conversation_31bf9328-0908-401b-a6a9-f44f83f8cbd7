#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 11:30:00
# <AUTHOR> Assistant
# @File         : company_update_example.py
# @Description  : 公司数据修改接口使用示例
"""

import requests
import json
from datetime import datetime


class CompanyUpdateClient:
    """公司数据修改客户端"""
    
    def __init__(self, base_url="http://localhost:9029"):
        """
        初始化客户端
        :param base_url: 服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
    
    def get_company_info(self, company_code: str):
        """
        获取公司信息
        :param company_code: 企业编号
        :return: 公司信息字典
        """
        url = f"{self.base_url}/company_info/{company_code}"
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def update_company(self, company_code: str, chi_name: str, chi_name_abbr: str = None, eng_name: str = None):
        """
        修改公司信息
        :param company_code: 企业编号
        :param chi_name: 中文名称
        :param chi_name_abbr: 企业别称
        :param eng_name: 英文全称
        :return: 修改结果字典
        """
        url = f"{self.base_url}/company_update"
        data = {
            "company_code": company_code,
            "chi_name": chi_name
        }
        
        if chi_name_abbr is not None:
            data["chi_name_abbr"] = chi_name_abbr
        
        if eng_name is not None:
            data["eng_name"] = eng_name
        
        try:
            response = requests.post(url, json=data, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def check_name_duplicate(self, chi_name: str, exclude_company_code: str = None):
        """
        检查公司名称是否重复
        :param chi_name: 中文名称
        :param exclude_company_code: 排除的企业编号
        :return: 检查结果字典
        """
        url = f"{self.base_url}/company_name_check"
        params = {"chi_name": chi_name}
        
        if exclude_company_code:
            params["exclude_company_code"] = exclude_company_code
        
        try:
            response = requests.post(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def print_company_info(self, company_info, title="公司信息"):
        """
        格式化打印公司信息
        :param company_info: 公司信息
        :param title: 标题
        """
        print(f"\n{title}")
        print("=" * 50)
        
        if "error" in company_info:
            print(f"错误: {company_info['error']}")
            return
        
        if company_info.get("code") == 200:
            data = company_info.get("data", {})
            print(f"企业编号: {data.get('company_code', 'N/A')}")
            print(f"中文名称: {data.get('chi_name', 'N/A')}")
            print(f"企业别称: {data.get('chi_name_abbr', 'N/A')}")
            print(f"曾用名: {data.get('pre_name', 'N/A')}")
            print(f"英文全称: {data.get('eng_name', 'N/A')}")
            print(f"英文简称: {data.get('eng_name_abbr', 'N/A')}")
            print(f"股票简称: {data.get('stock_abbr', 'N/A')}")
            print(f"品牌名称: {data.get('brand_name', 'N/A')}")
            print(f"大模型简称: {data.get('large_model_abbr', 'N/A')}")
            print(f"其他简称: {data.get('other_abbr', 'N/A')}")
            print(f"信用代码: {data.get('credit_code', 'N/A')}")
            print(f"创建时间: {data.get('create_time', 'N/A')}")
            print(f"更新时间: {data.get('update_time', 'N/A')}")
            print(f"状态: {data.get('status', 'N/A')}")
        else:
            print(f"请求失败: {company_info.get('message', 'Unknown error')}")
            if 'data' in company_info and 'error' in company_info['data']:
                print(f"错误详情: {company_info['data']['error']}")
    
    def print_update_result(self, update_result, title="修改结果"):
        """
        格式化打印修改结果
        :param update_result: 修改结果
        :param title: 标题
        """
        print(f"\n{title}")
        print("=" * 50)
        
        if "error" in update_result:
            print(f"错误: {update_result['error']}")
            return
        
        if update_result.get("code") == 200:
            data = update_result.get("data", {})
            print(f"企业编号: {data.get('company_code', 'N/A')}")
            print(f"原中文名称: {data.get('old_chi_name', 'N/A')}")
            print(f"新中文名称: {data.get('new_chi_name', 'N/A')}")
            print(f"企业别称: {data.get('chi_name_abbr', 'N/A')}")
            print(f"英文全称: {data.get('eng_name', 'N/A')}")
            print(f"更新后曾用名: {data.get('pre_name', 'N/A')}")
            print(f"更新时间: {data.get('update_time', 'N/A')}")
            print(f"操作结果: {data.get('message', 'N/A')}")
        else:
            print(f"请求失败: {update_result.get('message', 'Unknown error')}")
            if 'data' in update_result and 'error' in update_result['data']:
                print(f"错误详情: {update_result['data']['error']}")


def main():
    """主函数 - 演示如何使用公司数据修改接口"""
    
    print("公司数据修改接口使用示例")
    print("=" * 60)
    
    # 创建客户端（请根据实际服务器地址修改）
    client = CompanyUpdateClient("http://localhost:9029")
    
    # 测试用的企业编号（请根据实际数据修改）
    test_company_code = "COMP001"  # 请替换为实际存在的企业编号
    
    print(f"使用测试企业编号: {test_company_code}")
    print("注意：请确保该企业编号在数据库中存在")
    
    # 1. 获取原始公司信息
    print("\n1. 获取原始公司信息")
    original_info = client.get_company_info(test_company_code)
    client.print_company_info(original_info, "原始公司信息")
    
    if original_info.get("code") != 200:
        print("无法获取公司信息，请检查企业编号是否正确")
        return
    
    original_data = original_info.get("data", {})
    
    # 2. 检查名称重复
    print("\n2. 检查名称重复")
    print("=" * 50)
    
    # 检查新名称是否重复
    new_name = f"测试修改公司名称_{int(datetime.now().timestamp())}"
    check_result = client.check_name_duplicate(new_name)
    
    if check_result.get("code") == 200:
        check_data = check_result.get("data", {})
        print(f"检查名称: {check_data.get('chi_name', 'N/A')}")
        print(f"是否重复: {check_data.get('is_duplicate', 'N/A')}")
        print(f"检查结果: {check_data.get('message', 'N/A')}")
    else:
        print(f"检查失败: {check_result}")
    
    # 3. 修改公司信息
    print("\n3. 修改公司信息")
    print("=" * 50)
    
    # 准备修改数据
    new_chi_name = new_name
    new_chi_name_abbr = "测试简称"
    new_eng_name = "Test Updated Company Name"
    
    print(f"准备修改:")
    print(f"  - 新中文名称: {new_chi_name}")
    print(f"  - 新企业别称: {new_chi_name_abbr}")
    print(f"  - 新英文全称: {new_eng_name}")
    
    # 执行修改
    update_result = client.update_company(
        company_code=test_company_code,
        chi_name=new_chi_name,
        chi_name_abbr=new_chi_name_abbr,
        eng_name=new_eng_name
    )
    
    client.print_update_result(update_result, "修改结果")
    
    # 4. 验证修改结果
    print("\n4. 验证修改结果")
    updated_info = client.get_company_info(test_company_code)
    client.print_company_info(updated_info, "修改后的公司信息")
    
    # 5. 恢复原始数据（可选）
    print("\n5. 恢复原始数据")
    print("=" * 50)
    
    restore_result = client.update_company(
        company_code=test_company_code,
        chi_name=original_data.get('chi_name', ''),
        chi_name_abbr=original_data.get('chi_name_abbr'),
        eng_name=original_data.get('eng_name')
    )
    
    client.print_update_result(restore_result, "恢复结果")
    
    print("\n" + "=" * 60)
    print("示例运行完成")
    print("=" * 60)


def demo_error_handling():
    """演示错误处理"""
    print("\n" + "=" * 60)
    print("错误处理演示")
    print("=" * 60)
    
    client = CompanyUpdateClient("http://localhost:9029")
    
    # 1. 测试空企业编号
    print("\n1. 测试空企业编号...")
    result = client.update_company("", "测试名称")
    client.print_update_result(result, "空企业编号测试结果")
    
    # 2. 测试空中文名称
    print("\n2. 测试空中文名称...")
    result = client.update_company("COMP001", "")
    client.print_update_result(result, "空中文名称测试结果")
    
    # 3. 测试不存在的企业编号
    print("\n3. 测试不存在的企业编号...")
    result = client.update_company("NONEXISTENT", "测试名称")
    client.print_update_result(result, "不存在企业编号测试结果")


if __name__ == "__main__":
    print("注意：此示例需要实际的数据库数据")
    print("请修改 test_company_code 为实际存在的企业编号")
    print("=" * 60)
    
    # 运行主示例（取消注释以运行）
    # main()
    
    # 演示错误处理
    demo_error_handling()
    
    print("\n使用说明:")
    print("1. 确保服务器已启动: python main.py")
    print("2. 修改客户端中的服务器地址和测试企业编号")
    print("3. 取消注释 main() 函数调用")
    print("4. 运行此示例: python examples/company_update_example.py")
