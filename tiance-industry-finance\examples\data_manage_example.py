#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : data_manage_example.py
# @Description  : 数据管理模块使用示例
"""

import requests
import json
from datetime import datetime


class DataManageClient:
    """数据管理客户端"""
    
    def __init__(self, base_url="http://localhost:9029"):
        """
        初始化客户端
        :param base_url: 服务器基础URL
        """
        self.base_url = base_url.rstrip('/')
        self.data_manage_url = f"{self.base_url}/data_manage"
    
    def get_company_data_stats(self):
        """
        获取公司数据统计
        :return: 统计数据字典
        """
        url = f"{self.data_manage_url}/company_data_stats_simple"
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def get_company_info(self, company_code: str):
        """
        获取公司信息
        :param company_code: 企业编号
        :return: 公司信息字典
        """
        url = f"{self.data_manage_url}/company_info/{company_code}"
        try:
            response = requests.get(url, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def update_company(self, company_code: str, chi_name: str, chi_name_abbr: str = None, eng_name: str = None):
        """
        修改公司信息
        :param company_code: 企业编号
        :param chi_name: 中文名称
        :param chi_name_abbr: 企业别称
        :param eng_name: 英文全称
        :return: 修改结果字典
        """
        url = f"{self.data_manage_url}/company_update"
        data = {
            "company_code": company_code,
            "chi_name": chi_name
        }
        
        if chi_name_abbr is not None:
            data["chi_name_abbr"] = chi_name_abbr
        
        if eng_name is not None:
            data["eng_name"] = eng_name
        
        try:
            response = requests.post(url, json=data, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def batch_update_companies(self, companies_data: list):
        """
        批量修改公司信息
        :param companies_data: 公司修改数据列表
        :return: 批量修改结果字典
        """
        url = f"{self.data_manage_url}/company_update"
        data = {"companies": companies_data}
        
        try:
            response = requests.post(url, json=data, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def check_name_duplicate(self, chi_name: str, exclude_company_code: str = None):
        """
        检查公司名称是否重复
        :param chi_name: 中文名称
        :param exclude_company_code: 排除的企业编号
        :return: 检查结果字典
        """
        url = f"{self.data_manage_url}/company_name_check"
        params = {"chi_name": chi_name}
        
        if exclude_company_code:
            params["exclude_company_code"] = exclude_company_code
        
        try:
            response = requests.post(url, params=params, timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {str(e)}"}
    
    def print_stats(self, stats_data, title="公司数据统计"):
        """
        格式化打印统计数据
        :param stats_data: 统计数据
        :param title: 标题
        """
        print(f"\n{title}")
        print("=" * 50)
        
        if "error" in stats_data:
            print(f"错误: {stats_data['error']}")
            return
        
        if stats_data.get("code") == 200:
            data = stats_data.get("data", {})
            print(f"公司数据总量: {data.get('total_count', 'N/A')}")
            print(f"上市公司总数: {data.get('listed_count', 'N/A')}")
            print(f"当日更新总数: {data.get('today_updated_count', 'N/A')}")
            print(f"查询日期: {data.get('query_date', 'N/A')}")
        else:
            print(f"请求失败: {stats_data.get('message', 'Unknown error')}")
    
    def print_company_info(self, company_info, title="公司信息"):
        """
        格式化打印公司信息
        :param company_info: 公司信息
        :param title: 标题
        """
        print(f"\n{title}")
        print("=" * 50)
        
        if "error" in company_info:
            print(f"错误: {company_info['error']}")
            return
        
        if company_info.get("code") == 200:
            data = company_info.get("data", {})
            print(f"企业编号: {data.get('company_code', 'N/A')}")
            print(f"中文名称: {data.get('chi_name', 'N/A')}")
            print(f"企业别称: {data.get('chi_name_abbr', 'N/A')}")
            print(f"曾用名: {data.get('pre_name', 'N/A')}")
            print(f"英文全称: {data.get('eng_name', 'N/A')}")
            print(f"股票简称: {data.get('stock_abbr', 'N/A')}")
            print(f"更新时间: {data.get('update_time', 'N/A')}")
        else:
            print(f"请求失败: {company_info.get('message', 'Unknown error')}")
    
    def print_update_result(self, update_result, title="修改结果"):
        """
        格式化打印修改结果
        :param update_result: 修改结果
        :param title: 标题
        """
        print(f"\n{title}")
        print("=" * 50)
        
        if "error" in update_result:
            print(f"错误: {update_result['error']}")
            return
        
        if update_result.get("code") == 200:
            data = update_result.get("data", {})
            
            # 单个修改结果
            if "company_code" in data:
                print(f"企业编号: {data.get('company_code', 'N/A')}")
                print(f"原中文名称: {data.get('old_chi_name', 'N/A')}")
                print(f"新中文名称: {data.get('new_chi_name', 'N/A')}")
                print(f"企业别称: {data.get('chi_name_abbr', 'N/A')}")
                print(f"英文全称: {data.get('eng_name', 'N/A')}")
                print(f"更新时间: {data.get('update_time', 'N/A')}")
                print(f"操作结果: {data.get('message', 'N/A')}")
            
            # 批量修改结果
            elif "success" in data:
                print(f"操作成功: {data.get('success', 'N/A')}")
                print(f"总数量: {data.get('total_count', 'N/A')}")
                print(f"更新数量: {data.get('updated_count', 'N/A')}")
                print(f"消息: {data.get('message', 'N/A')}")
                
                if not data.get('success', True):
                    print(f"错误索引: {data.get('error_index', 'N/A')}")
                    print(f"错误企业编号: {data.get('error_company_code', 'N/A')}")
                    print(f"错误消息: {data.get('error_message', 'N/A')}")
                    print(f"错误类型: {data.get('error_type', 'N/A')}")
        else:
            print(f"请求失败: {update_result.get('message', 'Unknown error')}")


def main():
    """主函数 - 演示如何使用数据管理模块"""
    
    print("数据管理模块使用示例")
    print("=" * 60)
    
    # 创建客户端
    client = DataManageClient("http://localhost:9029")
    
    # 1. 获取公司数据统计
    print("\n1. 获取公司数据统计")
    stats_data = client.get_company_data_stats()
    client.print_stats(stats_data)
    
    # 2. 获取公司信息
    print("\n2. 获取公司信息")
    test_company_code = "COMP001"  # 请替换为实际存在的企业编号
    company_info = client.get_company_info(test_company_code)
    client.print_company_info(company_info, f"公司信息 - {test_company_code}")
    
    # 3. 检查名称重复
    print("\n3. 检查名称重复")
    new_name = f"测试公司名称_{int(datetime.now().timestamp())}"
    check_result = client.check_name_duplicate(new_name, test_company_code)
    
    if check_result.get("code") == 200:
        data = check_result.get("data", {})
        print(f"检查名称: {data.get('chi_name', 'N/A')}")
        print(f"是否重复: {data.get('is_duplicate', 'N/A')}")
        print(f"检查结果: {data.get('message', 'N/A')}")
    else:
        print(f"检查失败: {check_result}")
    
    # 4. 单个公司修改
    print("\n4. 单个公司修改")
    update_result = client.update_company(
        company_code=test_company_code,
        chi_name=new_name,
        chi_name_abbr="测试简称",
        eng_name="Test Company Name"
    )
    client.print_update_result(update_result, "单个修改结果")
    
    # 5. 批量公司修改
    print("\n5. 批量公司修改")
    batch_data = [
        {
            "company_code": "COMP001",  # 请替换为实际存在的企业编号
            "chi_name": f"批量测试A_{int(datetime.now().timestamp())}",
            "chi_name_abbr": "批量A",
            "eng_name": "Batch Test A"
        },
        {
            "company_code": "COMP002",  # 请替换为实际存在的企业编号
            "chi_name": f"批量测试B_{int(datetime.now().timestamp())}",
            "chi_name_abbr": "批量B",
            "eng_name": "Batch Test B"
        }
    ]
    
    batch_result = client.batch_update_companies(batch_data)
    client.print_update_result(batch_result, "批量修改结果")
    
    print("\n" + "=" * 60)
    print("示例运行完成")
    print("=" * 60)


def demo_error_handling():
    """演示错误处理"""
    print("\n" + "=" * 60)
    print("错误处理演示")
    print("=" * 60)
    
    client = DataManageClient("http://localhost:9029")
    
    # 1. 测试不存在的企业编号
    print("\n1. 测试不存在的企业编号...")
    result = client.get_company_info("NONEXISTENT")
    client.print_company_info(result, "不存在企业编号测试结果")
    
    # 2. 测试批量修改错误
    print("\n2. 测试批量修改错误...")
    error_batch_data = [
        {
            "company_code": "",  # 空企业编号
            "chi_name": "测试名称"
        },
        {
            "company_code": "COMP001",
            "chi_name": ""  # 空中文名称
        }
    ]
    
    result = client.batch_update_companies(error_batch_data)
    client.print_update_result(result, "批量修改错误测试结果")


if __name__ == "__main__":
    print("注意：此示例需要实际的数据库数据")
    print("请修改代码中的企业编号为实际存在的值")
    print("=" * 60)
    
    # 演示错误处理
    demo_error_handling()
    
    print("\n使用说明:")
    print("1. 确保服务器已启动: python main.py")
    print("2. 修改客户端中的服务器地址和测试企业编号")
    print("3. 取消注释 main() 函数调用")
    print("4. 运行此示例: python examples/data_manage_example.py")
    
    # 运行主示例（取消注释以运行）
    # main()
