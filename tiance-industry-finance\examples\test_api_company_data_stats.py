#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 10:00:00
# <AUTHOR> Assistant
# @File         : test_api_company_data_stats.py
# @Description  : 测试公司数据总量查询API接口
"""

import requests
import json
import time


def test_api_endpoints():
    """测试所有API端点"""
    
    # 配置服务器地址（请根据实际情况修改）
    base_url = "http://localhost:9029"  # 请根据实际端口修改
    
    print("=" * 60)
    print("开始测试公司数据总量查询API接口")
    print("=" * 60)
    
    # 测试1: POST /company_data_stats
    print("\n1. 测试 POST /company_data_stats")
    print("-" * 40)
    try:
        url = f"{base_url}/company_data_stats"
        response = requests.post(url, json={}, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    except Exception as e:
        print(f"其他异常: {str(e)}")
    
    # 等待一秒
    time.sleep(1)
    
   


def test_curl_commands():
    """生成curl测试命令"""
    base_url = "http://localhost:9029"  # 请根据实际端口修改
    
    print("\n" + "=" * 60)
    print("curl 测试命令")
    print("=" * 60)
    
    print("\n1. POST /company_data_stats")
    print(f'curl -X POST "{base_url}/company_data_stats" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{}\'')



def check_server_status():
    """检查服务器状态"""
    base_url = "http://localhost:9029"
    
    print("检查服务器状态...")
    try:
        # 尝试访问根路径或健康检查端点
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到服务器: {str(e)}")
        print("请确保服务器已启动并运行在正确的端口上")
        return False


if __name__ == "__main__":
    print("公司数据总量查询API测试工具")
    print("请确保服务器已启动")
    
    # 检查服务器状态
    if check_server_status():
        # 测试API端点
        test_api_endpoints()
    else:
        print("\n服务器未运行，显示curl测试命令供参考：")
        test_curl_commands()
        
        print("\n启动服务器的命令:")
        print("python main.py")
        
        print("\n或者使用uvicorn:")
        print("uvicorn main:app --host 0.0.0.0 --port 8000")
