#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 10:00:00
# <AUTHOR> Assistant
# @File         : test_company_data_stats.py
# @Description  : 测试公司数据总量查询功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service.company_data_stats_service import CompanyDataStatsService
from utils.log_util import LogUtil


def test_company_data_stats():
    """测试公司数据统计功能"""
    try:
        print("=" * 50)
        print("开始测试公司数据统计功能")
        print("=" * 50)
        
        # 初始化日志
        LogUtil.init(process_name="test_company_data_stats")
        
        # 测试基础统计
        print("\n1. 测试基础统计功能...")
        stats = CompanyDataStatsService.get_company_data_statistics()
        
        print(f"查询结果:")
        print(f"  - 公司数据总量: {stats['total_count']}")
        print(f"  - 上市公司总数: {stats['listed_count']}")
        print(f"  - 当日更新总数: {stats['today_updated_count']}")
        print(f"  - 查询日期: {stats['query_date']}")
        
        # 测试详细统计
        print("\n2. 测试详细统计功能...")
        detailed_stats = CompanyDataStatsService.get_detailed_statistics()
        
        print(f"详细统计结果:")
        for key, value in detailed_stats.items():
            print(f"  - {key}: {value}")
        
        print("\n" + "=" * 50)
        print("测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_sql_util_count():
    """测试 SQL 工具类的计数功能"""
    try:
        print("\n" + "=" * 50)
        print("测试 SQL 工具类计数功能")
        print("=" * 50)
        
        from utils.sql_util import SQLUtil
        from entity.mysql_entity import CompanyMain
        from sqlalchemy import and_
        from datetime import datetime, date
        
        # 连接数据库
        SQLUtil.connect()
        
        # 测试基础计数
        print("\n1. 测试基础计数...")
        total = SQLUtil.count_records(CompanyMain)
        print(f"CompanyMain 表总记录数: {total}")
        
        # 测试带条件计数
        print("\n2. 测试带条件计数...")
        
        # 状态为1的记录数
        active_count = SQLUtil.count_records_with_condition(
            CompanyMain, 
            CompanyMain.status == 1
        )
        print(f"状态为1的记录数: {active_count}")
        
        # 上市公司数量
        listed_count = SQLUtil.count_records_with_condition(
            CompanyMain,
            and_(
                CompanyMain.status == 1,
                CompanyMain.StockAbbr.isnot(None),
                CompanyMain.StockAbbr != '',
                CompanyMain.StockAbbr != 'NULL'
            )
        )
        print(f"上市公司数量: {listed_count}")
        
        # 当日更新数量
        today = date.today()
        today_start = datetime.combine(today, datetime.min.time())
        
        today_updated = SQLUtil.count_records_with_condition(
            CompanyMain,
            and_(
                CompanyMain.status == 1,
                CompanyMain.update_time >= today_start
            )
        )
        print(f"当日更新数量: {today_updated}")
        
        print("\n" + "=" * 50)
        print("SQL 工具类测试完成！")
        print("=" * 50)
        
    except Exception as e:
        print(f"SQL 工具类测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            SQLUtil.close()
        except:
            pass


if __name__ == "__main__":
    # 测试 SQL 工具类
    test_sql_util_count()
    
    # 测试服务层
    test_company_data_stats()
