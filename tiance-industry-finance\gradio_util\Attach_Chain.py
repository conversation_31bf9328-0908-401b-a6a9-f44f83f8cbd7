import requests
import json
import gradio as gr
class Attach_Chain:
    @staticmethod
    def call_company_full_name_supplement(node_companies: list, model: str):
        node_companies = json.loads(node_companies)
        # 构建请求体
        request_data = {
            "node_companies": node_companies,
            "model": model,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/company_full_name_supplement", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def get_fullname_companies(industry: str, node_companies: dict):
        # 构建请求体
        request_data = {
            "node_companies": node_companies,
            "industry": industry,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/get_fullname_companies", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def annual_report_info_ext(company_name: str):
        # 构建请求体
        request_data = {
            "company_name": company_name,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/annual_report_info_ext", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def company_relation(pdf_name: str):
        # 构建请求体
        request_data = {
            "pdf_name": pdf_name,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/company_relation", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def company_relation_with_file(pdf_name: str,pdf_filename:str,industry_chain:str):
        # 构建请求体
        request_data = {
            "pdf_name": pdf_name,
            "pdf_filename": pdf_filename,
            "industry_chain": industry_chain,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/company_relation_with_file", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def annual_report_info_ext_with_file(company_name: str,year:str,pdf_filename:str,industry_chain:str):
        # 构建请求体
        request_data = {
            "company_name": company_name,
            "year": year,
            "pdf_filename": pdf_filename,
            "industry_chain": industry_chain,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/annual_report_info_ext_with_file", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def company_summary(industry: str, company_type: str, prompt: str):
        # 构建请求体
        request_data = {
            "industry": industry,
            "company_type": company_type,
            "prompt": prompt
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/company_summary", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def get_key_companies(industry: str):
        # 构建请求体
        request_data = {
            "industry": industry
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/get_key_companies", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def key_companies(industry: str):
        # 构建请求体
        request_data = {
            "industry": industry
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/key_companies", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def related_companies(industry: str):
        # 构建请求体
        request_data = {
            "industry": industry
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/related_companies", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def label_similar(input_labels: list):
        input_labels = json.loads(input_labels)
        # 构建请求体
        request_data = {
            "input_labels": input_labels,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/label_similar", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"
    @staticmethod
    def label_replace(input_labels: list, style: str):
        input_labels = json.loads(input_labels)
        # 构建请求体
        request_data = {
            "input_labels": input_labels,
            "style": style,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/label_replace", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def label_similar_tab():
        with gr.TabItem("标签相似度分类") as tab:
            gr.Markdown("标签相似度分类")
            input_labels = gr.Textbox(label="Input Labels", value='["协作机器人","协助机器人","整车销售"]')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.label_similar,
                inputs=[input_labels],
                outputs=output
            )
        return tab

    @staticmethod
    def label_replace_tab():
        with gr.TabItem("标签替换") as tab:
            gr.Markdown("标签替换")
            input_labels = gr.Textbox(label="Input Labels", value='["协作机器人","协助机器人","整车销售"]')
            style = gr.Textbox(label="Style", value="num", info="标签划分方式：标签出现次数：num,标签置信度：value")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.label_replace,
                inputs=[input_labels, style],
                outputs=output
            )
        return tab

    @staticmethod
    def call_company_full_name_supplement_tab():
        with gr.TabItem("中心客群全称补充") as tab:
            gr.Markdown("中心客群全称补充")
            node_companies = gr.Textbox(label="node_companies", value='["伯朗特","埃弗米"]')
            model = gr.Textbox(label="model", value="qwen2.5-72B")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.call_company_full_name_supplement,
                inputs=[node_companies, model],
                outputs=output
            )
        return tab

    @staticmethod
    def get_fullname_companies_tab():
        with gr.TabItem("获取中心客群全称") as tab:
            gr.Markdown("获取中心客群全称")
            industry = gr.Textbox(label="industry", value='')
            node_companies = gr.Textbox(label="node_companies", value="")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.get_fullname_companies,
                inputs=[ industry,node_companies],
                outputs=output
            )
        return tab

    @staticmethod
    def annual_report_info_ext_with_file_tab():
        with gr.TabItem("年报相关公司信息提取"):
            gr.Markdown("年报相关公司信息提取")
            company_name = gr.Textbox(label="company_name", value='')
            year = gr.Textbox(label="year", value="")
            pdf_filename = gr.Textbox(label="pdf_filename", value='')
            industry_chain = gr.Textbox(label="industry_chain", value='')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.annual_report_info_ext_with_file,
                inputs=[company_name, year, pdf_filename, industry_chain],
                outputs=output
            )

    @staticmethod
    def annual_report_info_ext_tab():
        with gr.TabItem("指定年报相关公司信息提取"):
            gr.Markdown("指定年报相关公司信息提取")
            company_name = gr.Textbox(label="company_name", info='请从"中国石油","南钢股份","天奇股份","中国石化"选择')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.annual_report_info_ext,
                inputs=[company_name],
                outputs=output
            )

    @staticmethod
    def company_relation_tab():
        with gr.TabItem("招股说明书公司信息提取"):
            gr.Markdown("招股说明书公司信息提取")
            pdf_name = gr.Textbox(label="company_name", )
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.company_relation,
                inputs=[pdf_name],
                outputs=output
            )

    @staticmethod
    def company_relation_with_file_tab():
        with gr.TabItem("招股说明书公司文件信息提取"):
            gr.Markdown("招股说明书公司文件信息提取")
            pdf_name = gr.Textbox(label="company_name", )
            pdf_filename = gr.Textbox(label="company_name")
            industry_chain = gr.Textbox(label="company_name")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.company_relation_with_file,
                inputs=[pdf_name,pdf_filename,industry_chain],
                outputs=output
            )

    @staticmethod
    def company_summary_tab():
        with gr.TabItem("客群总结"):
            gr.Markdown("客群总结")
            industry = gr.Textbox(label="industry", )
            company_type = gr.Textbox(label="company_name")
            prompt = gr.Textbox(label="company_name")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.company_summary,
                inputs=[industry, company_type, prompt],
                outputs=output
            )

    @staticmethod
    def get_key_companies_tab():
        with gr.TabItem("抽取中心客群"):
            gr.Markdown("抽取中心客群")
            industry = gr.Textbox(label="industry", )
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.get_key_companies,
                inputs=[industry],
                outputs=output
            )

    @staticmethod
    def key_companies_tab():
        with gr.TabItem("中心客群详情"):
            gr.Markdown("中心客群详情")
            industry = gr.Textbox(label="industry", )
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.key_companies,
                inputs=[industry],
                outputs=output
            )


    @staticmethod
    def related_companies_tab():
        with gr.TabItem("关联客群详情"):
            gr.Markdown("关联客群详情")
            industry = gr.Textbox(label="industry", )
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Attach_Chain.related_companies,
                inputs=[industry],
                outputs=output
            )



    @staticmethod
    def tabs():
        with gr.Row(visible=False) as tabs:
                Attach_Chain.key_companies_tab()
                Attach_Chain.get_key_companies_tab()
                Attach_Chain.get_fullname_companies_tab()
                Attach_Chain.call_company_full_name_supplement_tab()
                Attach_Chain.annual_report_info_ext_with_file_tab()
                Attach_Chain.annual_report_info_ext_tab()
                Attach_Chain.label_replace_tab()
                Attach_Chain.label_similar_tab()
                Attach_Chain.company_relation_tab()
                Attach_Chain.company_relation_with_file_tab()
                Attach_Chain.company_summary_tab()
                Attach_Chain.related_companies_tab()

        return tabs

    @staticmethod
    def  tabs_sub1():
        with gr.Row(visible=False) as tabs:
            Attach_Chain.get_key_companies_tab()
            Attach_Chain.key_companies_tab()
            Attach_Chain.annual_report_info_ext_with_file_tab()
            Attach_Chain.annual_report_info_ext_tab()
            Attach_Chain.company_relation_tab()
            Attach_Chain.company_relation_with_file_tab()

        return tabs

    @staticmethod
    def tabs_sub2():
        with gr.Row(visible=False) as tabs:
            Attach_Chain.get_fullname_companies_tab()
            Attach_Chain.call_company_full_name_supplement_tab()
            Attach_Chain.label_similar_tab()
            Attach_Chain.label_replace_tab()
            Attach_Chain.company_summary_tab()
            Attach_Chain.related_companies_tab()
        return tabs