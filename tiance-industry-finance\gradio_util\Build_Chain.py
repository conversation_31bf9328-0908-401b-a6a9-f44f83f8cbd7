import requests
import json
import gradio as gr
class Build_Chain:
    @staticmethod
    def industry_chain_map(node_name_info: str,stream_type:str):
        # 构建请求体
        request_data = {
            "node_name_info": node_name_info,
            "stream_type": stream_type
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/industry_chain_map", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def industry_chain_map_tab():
        with gr.TabItem("产业链图谱生成") as tab:
            gr.Markdown("industry_chain_map")
            node_name_info = gr.Textbox(label="node_name_info", value='工业机器人')
            stream_type = gr.Textbox(label="stream_type", value="", info="上游|中游|下游")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.industry_chain_map,
                inputs=[node_name_info, stream_type],
                outputs=output
            )
        return tab

    @staticmethod
    def label_extract_perform(model: str, k: int, doc_id_and_type: list, system_prompt: str, is_cached: bool):
        doc_id_and_type = json.loads(doc_id_and_type)
        # 构建请求体
        request_data = {
            "model": model,
            "k": k,
            "doc_id_and_type": doc_id_and_type,
            "system_prompt": system_prompt,
            "is_cached": is_cached
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/label_extract_perform", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def label_extract_perform_tab():
        with gr.TabItem("产业链标签抽取") as tab:
            gr.Markdown("产业链标签抽取")
            model = gr.Textbox(label="model", value='qwen2.5-72B')
            k = gr.Textbox(label="k", value="5", info="检索数量")
            doc_id_and_type = gr.Textbox(label="doc_id_and_type", value='''[
                {"doc_type": "research_report", "mongodb_id": "3ad4695c6cdc44e9ade70bf84d9c5049"},
                {"doc_type": "research_report", "mongodb_id": "2e25bd437c0d4cfb9a1b61d15a76868b"}]''')
            is_cached = gr.Textbox(label="is_cached", value="False", info="True|False")
            system_prompt = gr.Textbox(label="system_prompt", value="", info="提示词")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.label_extract_perform,
                inputs=[model, k,doc_id_and_type, system_prompt,is_cached],
                outputs=output
            )
        return tab

    @staticmethod
    def label_merge_perform(mongodb_id: str, model: str, prompt: str, merge_type: str, is_ai_extend: bool, is_cached: bool):
        # 构建请求体
        request_data = {
            "mongodb_id": mongodb_id,
            "model": model,
            "prompt": prompt,
            "merge_type": merge_type,
            "is_ai_extend": is_ai_extend,
            "is_cached": is_cached
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/label_merge_perform", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def label_merge_perform_tab():
        with gr.TabItem("产业链标签抽取") as tab:
            gr.Markdown("产业链标签抽取")
            mongodb_id = gr.Textbox(label="mongodb_id", value='********************************',info="标签提取返回的id")
            model = gr.Textbox(label="model", value="qwen2.5-72B")
            merge_type = gr.Textbox(label="merge_type", value='Frequency',info='合并类型')
            is_ai_extend = gr.Textbox(label="is_ai_extend", value="False", info="True|False")
            is_cached = gr.Textbox(label="is_cached", value="False", info="True|False")
            prompt = gr.Textbox(label="prompt", value="", info="提示词")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.label_merge_perform,
                inputs=[mongodb_id, model, prompt, merge_type,is_ai_extend, is_cached],
                outputs=output
            )
        return tab

    @staticmethod
    def node_analysis(node_name_info: str, stream_type: str):
        # 构建请求体
        request_data = {
            "node_name_info": node_name_info,
            "stream_type": stream_type
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/node_analysis", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def node_analysis_tab():
        with gr.TabItem("节点分析") as tab:
            gr.Markdown("节点分析")
            node_name_info = gr.Textbox(label="node_name_info", value='工业机器人')
            stream_type = gr.Textbox(label="stream_type", value="", info="上游|中游|下游")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.node_analysis,
                inputs=[node_name_info, stream_type],
                outputs=output
            )
        return tab

    @staticmethod
    def research_report_compose(model: str, k: str,mongodb_ids: list, industry: str, system_prompt: str):
        mongodb_ids = json.loads(mongodb_ids)
        # 构建请求体
        request_data = {
            "model": model,
            "k": k,
            "mongodb_ids":mongodb_ids,
            "industry":industry,
            "system_prompt":system_prompt,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/research_report_compose", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def research_report_compose_tab():
        with gr.TabItem("多研报产业链结构组合结果") as tab:
            gr.Markdown("多研报产业链结构组合结果")
            model = gr.Textbox(label="model", value='DeepSeek-R1-671B')
            k = gr.Textbox(label="k", value="5", info="上游|中游|下游")
            mongodb_ids = gr.Textbox(label="mongodb_ids", value='研报mongo_id列表')
            industry = gr.Textbox(label="industry", value="", info="产业链名称")
            system_prompt = gr.Textbox(label="system_prompt", value='')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.research_report_compose,
                inputs=[model, k,mongodb_ids, industry, system_prompt],
                outputs=output
            )
        return tab

    @staticmethod
    def research_report_structure(model: str, k: str, mongodb_ids: list, industry: str, system_prompt: str):
        mongodb_ids = json.loads(mongodb_ids)
        # 构建请求体
        request_data = {
            "model": model,
            "k": k,
            "mongodb_ids": mongodb_ids,
            "industry": industry,
            "system_prompt": system_prompt,
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/research_report_structure", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def research_report_structure_tab():
        with gr.TabItem("单研报产业链结构梳理agent") as tab:
            gr.Markdown("单研报产业链结构梳理agent")
            model = gr.Textbox(label="model", value='DeepSeek-R1-671B')
            k = gr.Textbox(label="k", value="5", info="上游|中游|下游")
            mongodb_ids = gr.Textbox(label="mongodb_ids", value='研报mongo_id列表')
            industry = gr.Textbox(label="industry", value="", info="产业链名称")
            system_prompt = gr.Textbox(label="system_prompt", value='')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.research_report_structure,
                inputs=[model, k,mongodb_ids, industry, system_prompt],
                outputs=output
            )
        return tab

    @staticmethod
    def research_report_extension(mongodb_ids: list, industry: str):
        # 构建请求体
        mongodb_ids = json.loads(mongodb_ids)
        request_data = {
            "mongodb_ids": mongodb_ids,
            "industry": industry
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/research_report_extension", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def research_report_extension_tab():
        with gr.TabItem("研报扩展获取产业链图谱") as tab:
            gr.Markdown("研报扩展获取产业链图谱")
            mongodb_ids = gr.Textbox(label="node_name_info", value='研报mongo_id列表')
            industry = gr.Textbox(label="industry", value="", info="产业链名称")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.research_report_extension,
                inputs=[mongodb_ids, industry],
                outputs=output
            )
        return tab

    @staticmethod
    def search_key_companies(model: str, k: str,  collection_name:str,mongodb_id: str,industry: str, system_prompt: str, link:str):
        # 构建请求体
        # 构建请求体
        request_data = {
            "model": model,
            "k": k,
            "collection_name": collection_name,
            "mongodb_id": mongodb_id,
            "industry": industry,
            "system_prompt": system_prompt,
            "link":link
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/search_key_companies", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def search_key_companies_tab():
        with gr.TabItem("查询单环节中心客群") as tab:
            gr.Markdown("查询单环节中心客群")
            model = gr.Textbox(label="model", value='qwen2.5-72B')
            k = gr.Textbox(label="k", value="5", info="检索内容数")
            collection_name = gr.Textbox(label="", value="5", info="文档所在mongo集合")
            mongodb_id = gr.Textbox(label="mongodb_id", value='研报mongo_id')
            industry = gr.Textbox(label="industry", value="", info="产业链名称")
            link = gr.Textbox(label="link", value="", info="产业链环节名称")
            system_prompt = gr.Textbox(label="system_prompt", value='')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.search_key_companies,
                inputs=[model, k, collection_name,mongodb_id, industry, system_prompt,link],
                outputs=output
            )
        return tab


    @staticmethod
    def search_key_companies_compose(key_companies: list):
        # 构建请求体
        key_companies = json.loads(key_companies)
        request_data = {
            "key_companies": key_companies
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/search_key_companies_compose", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def search_key_companies_compose_tab():
        with gr.TabItem("研报扩展获取产业链图谱") as tab:
            gr.Markdown("研报扩展获取产业链图谱")
            key_companies = gr.Textbox(label="key_companies", value='中心客群')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Build_Chain.search_key_companies_compose,
                inputs=[key_companies],
                outputs=output
            )
        return tab

    @staticmethod
    def tabs():
        with gr.Row(visible=False) as tabs:
            Build_Chain.industry_chain_map_tab()
            Build_Chain.label_extract_perform_tab()
            Build_Chain.label_merge_perform_tab()
            Build_Chain.node_analysis_tab()
            Build_Chain.research_report_extension_tab()
            Build_Chain.research_report_compose_tab()
            Build_Chain.research_report_structure_tab()
            Build_Chain.search_key_companies_tab()
            Build_Chain.search_key_companies_compose_tab()
        return tabs

    @staticmethod
    def  tabs_sub1():
        with gr.Row(visible=False) as tabs:
            Build_Chain.label_extract_perform_tab()
            Build_Chain.label_merge_perform_tab()
            Build_Chain.node_analysis_tab()
        return tabs

    @staticmethod
    def tabs_sub2():
        with gr.Row(visible=False) as tabs:
            Build_Chain.industry_chain_map_tab()
            Build_Chain.research_report_extension_tab()
            Build_Chain.research_report_compose_tab()
            Build_Chain.research_report_structure_tab()
            Build_Chain.search_key_companies_tab()
            Build_Chain.search_key_companies_compose_tab()
        return tabs