import requests
import json
import gradio as gr
class Chain_Data:


    @staticmethod
    def common_extension(node_name_info: str,stream_type:str):
        # 构建请求体
        request_data = {
            "node_name_info": node_name_info,
            "stream_type": stream_type
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/common_extension", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def common_extension_tab():
        with gr.TabItem("通用扩展") as tab:
            gr.Markdown("通用扩展")
            node_name_info = gr.Textbox(label="node_name_info", value='专用车产业链|橡胶')
            stream_type = gr.Textbox(label="stream_type", value="", info="上游")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.<PERSON><PERSON>("Process")
            button.click(
                fn=Chain_Data.common_extension,
                inputs=[node_name_info, stream_type],
                outputs=output
            )
        return tab


    @staticmethod
    def tabs():
        with gr.Row(visible=False) as tabs:
            Chain_Data.common_extension_tab()
        return tabs

    @staticmethod
    def  tabs_sub1():
        with gr.Row(visible=False) as tabs:
            Chain_Data.common_extension_tab()
        return tabs

    @staticmethod
    def tabs_sub2():
        with gr.Row(visible=False) as tabs:
            Chain_Data.common_extension_tab()
        return tabs