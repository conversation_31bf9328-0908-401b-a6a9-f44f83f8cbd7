import requests
import json
import gradio as gr
class Extension:


    @staticmethod
    def common_extension(node_name_info: str,stream_type:str):
        # 构建请求体
        request_data = {
            "node_name_info": node_name_info,
            "stream_type": stream_type
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/common_extension", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def common_extension_tab():
        with gr.TabItem("通用扩展") as tab:
            gr.Markdown("通用扩展")
            node_name_info = gr.Textbox(label="node_name_info", value='专用车产业链|橡胶')
            stream_type = gr.Textbox(label="stream_type", value="", info="上游")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.<PERSON><PERSON>("Process")
            button.click(
                fn=Extension.common_extension,
                inputs=[node_name_info, stream_type],
                outputs=output
            )
        return tab


    @staticmethod
    def financial_suggestions(node_name_info: str,stream_type:str):
        # 构建请求体
        request_data = {
            "node_name_info": node_name_info,
            "stream_type": stream_type
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/common_extension", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def financial_suggestions_tab():
        with gr.TabItem("融资营销建议") as tab:
            gr.Markdown("融资营销建议")
            industry = gr.Textbox(label="industry", info='产业链名称')
            suggestion_type = gr.Textbox(label="suggestion_type", value="", info="建议类型")
            prompt = gr.Textbox(label="prompt", value="", info="")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Extension.common_extension,
                inputs=[industry, prompt,suggestion_type],
                outputs=output
            )
        return tab

    @staticmethod
    def industry_evaluation(industry: str,prompt:str):
        # 构建请求体
        request_data = {
            "industry": industry,
            "prompt": prompt
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/industry_evaluation", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def industry_evaluation_tab():
        with gr.TabItem("产业评价") as tab:
            gr.Markdown("产业评价")
            industry = gr.Textbox(label="industry", info='产业链名称')
            prompt = gr.Textbox(label="prompt", value="", info="")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Extension.industry_evaluation,
                inputs=[industry, prompt],
                outputs=output
            )
        return tab


    @staticmethod
    def product_extension(node_name_info: str,stream_type:str,document_list:list):
        document_list = json.loads(document_list)
        # 构建请求体
        request_data = {
            "node_name_info": node_name_info,
            "stream_type": stream_type,
            "document_list":document_list
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/product_extension", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def product_extension_tab():
        with gr.TabItem("产品扩展") as tab:
            gr.Markdown("产品扩展")
            node_name_info = gr.Textbox(label="node_name_info", info='产业链节点信息名称')
            stream_type = gr.Textbox(label="stream_type", value="", info="上下游类型")
            document_list = gr.Textbox(label="document_list", value="", info="")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Extension.product_extension,
                inputs=[node_name_info, stream_type,document_list],
                outputs=output
            )
        return tab

    @staticmethod
    def push_company_info(list:list):
        list = json.loads(list)
        # 构建请求体
        request_data = {
            "list": list
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/push_company_info", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def push_company_info_tab():
        with gr.TabItem("推送企业信息") as tab:
            gr.Markdown("推送企业信息")
            list = gr.Textbox(label="list", info='推送企业信息')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Extension.push_company_info,
                inputs=[list],
                outputs=output
            )
        return tab

    @staticmethod
    def search_document(doc_keyword:str):
        # 构建请求体
        request_data = {
            "doc_keyword": doc_keyword
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/search_document", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def search_document_tab():
        with gr.TabItem("文档检索") as tab:
            gr.Markdown("文档检索")
            doc_keyword = gr.Textbox(label="doc_keyword", info='推送企业信息')
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Extension.search_document,
                inputs=[doc_keyword],
                outputs=output
            )
        return tab

    @staticmethod
    def search_key_companies_compose(model:str,k:int,collection_names:str,mongodb_ids:list,industry:str,chain_structure:dict,epochs:int):
        # 构建请求体
        request_data = {
            "model": model,
            "k": k,
            "collection_names": collection_names,
            "mongodb_ids": mongodb_ids,
            "industry": industry,
            "chain_structure": chain_structure,
            "epochs": epochs
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/search_key_companies_compose", json=request_data)

        # 解析响应
        if response.status_code == 200:
            result = response.json()
            return result.get("data")
        else:
            return f"Error: {response.status_code}"

    @staticmethod
    def search_key_companies_compose_tab():
        with gr.TabItem("多研报中心客群组合结果") as tab:
            gr.Markdown("多研报中心客群组合结果")
            model = gr.Textbox(label="model", value='qwen2.5-72B')
            k = gr.Textbox(label="k", value="5", info="检索内容数")
            collection_names = gr.Textbox(label="collection_names", value="5", info="文档所在mongo集合")
            mongodb_ids = gr.Textbox(label="mongodb_ids", value='研报mongo_id')
            industry = gr.Textbox(label="industry", value="", info="产业链名称")
            chain_structure = gr.Textbox(label="chain_structure", value="", info="产业链结构")
            epochs = gr.Textbox(label="epochs", value='',info="单环节调用次数")
            output = gr.Textbox(label="Output", show_copy_button=True)
            button = gr.Button("Process")
            button.click(
                fn=Extension.search_key_companies_compose,
                inputs=[model, k, collection_names,mongodb_ids,industry,chain_structure,epochs],
                outputs=output
            )
        return tab

    @staticmethod
    def tabs():
        with gr.Row(visible=False) as tabs:
            Extension.common_extension_tab()
            Extension.financial_suggestions_tab()
            Extension.industry_evaluation_tab()
            Extension.product_extension_tab()
            Extension.push_company_info_tab()
            Extension.search_document_tab()
            Extension.search_key_companies_compose_tab()
        return tabs

    @staticmethod
    def  tabs_sub1():
        with gr.Row(visible=False) as tabs:
            Extension.common_extension_tab()
            Extension.product_extension_tab()
            Extension.push_company_info_tab()
            Extension.search_document_tab()
            Extension.search_key_companies_compose_tab()
        return tabs

    @staticmethod
    def tabs_sub2():
        with gr.Row(visible=False) as tabs:
            Extension.financial_suggestions_tab()
            Extension.industry_evaluation_tab()
        return tabs