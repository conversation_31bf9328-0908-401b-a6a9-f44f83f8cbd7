import gradio as gr
from gradio_util.Attach_Chain import Attach_Chain
from gradio_util.extension import Extension
from gradio_util.Build_Chain import Build_Chain
from gradio_util.Chain_Data import Chain_Data
import requests
import json

sub_btns_visible = False


class Gradio_util:

    @staticmethod
    def ai_assistant(input: str,st:str):
        question = input['text']
        # 构建请求体
        request_data = {
            "user_id": "user",
            "question": question,
            "stream": 0
        }

        # 发送 POST 请求到 FastAPI 接口
        response = requests.post("http://127.0.0.1:9029/ai_assistant", json=request_data)
        # 解析响应
        if response.status_code == 200:
            raw_data = response.text
            lines = raw_data.strip().splitlines()

            # 提取每行中的 JSON 数据并解析
            combined_data = []
            for line in lines:
                if line != "":
                    json_str = line.split(':', 1)[1].strip()  # 提取 JSON 字符串部分
                    data_dict = json.loads(json_str)  # 解析 JSON 字符串为字典
                    combined_data.append(data_dict['data'])
                else:
                    continue
            complete_text = ''.join(combined_data)
            return complete_text
        else:
            return f"Error: {response.status_code}"


    @staticmethod
    def show_tabs_btn(tabs_all, tab, main_content,sub_btn1_col):
        global sub_btns_visible
        sub_btns_visible = not sub_btns_visible
        update = {main_content: gr.Row.update(visible=False),
                  sub_btn1_col: gr.update(visible=sub_btns_visible)}
        for i in tabs_all.children:
            i.visible = False
            update[i] = gr.Row.update(visible=False)
        id = tab
        # 隐藏所有标签页
        # update = {
        #     tabs_1: gr.Row.update(visible=False),
        #     tabs_2: gr.Row.update(visible=False),
        #     tabs_3: gr.Row.update(visible=False),
        #     tabs_4: gr.Row.update(visible=False),
        #     main_content: gr.Row.update(visible=False),
        #     sub_btn1_col: gr.update(visible=sub_btns_visible)
        # }
        # if selected_type == "首页":
        #     update[main_content] = gr.Row.update(visible=True)
        # else:
        update[main_content] = gr.Row.update(visible=True)
        update[id] = gr.Row.update(visible=True)
        return update

    @staticmethod
    def show_tabs(selected_type,tabs_all,tab,main_content):
        update = {main_content: gr.Row.update(visible=False)}
        for i in tabs_all.children:
            i.visible = False
            update[i] = gr.Row.update(visible=False)
        id = tab
        #隐藏所有标签页
        if selected_type == "首页":
            update[id] = gr.Row.update(visible=True)
            # update[main_content] = gr.Row.update(visible=True)
            return update
        else:
            update[main_content] = gr.Row.update(visible=True)
            update[id] = gr.Row.update(visible=True)
            return update




    @staticmethod
    def toggle_sub_buttons():
        global sub_btns_visible
        sub_btns_visible = not sub_btns_visible
        return gr.update(visible=sub_btns_visible)

    @staticmethod
    def gradio_demo():
        with gr.Blocks() as demo:
            with gr.Row(scale=1):
                with gr.Column(scale=1, min_width=200,variant="panel"):
                    with gr.Row():
                        with gr.Column():
                            gr.Markdown("# 产融")
                            with gr.Row():
                                btn0 = gr.Button("首页")
                                btn1 = gr.Button("数据解析")
                                with gr.Column(visible=False) as sub_col1:
                                    btn1_sub1 = gr.Button("数据解析-子按钮1", size="sm",min_width=100)
                                    btn1_sub2 = gr.Button("数据解析-子按钮2", size="sm",min_width=100)
                                btn2 = gr.Button("建链")
                                with gr.Column(visible=False) as sub_col2:
                                    btn2_sub1 = gr.Button("数据抽取", size="sm",min_width=100)
                                    btn2_sub2 = gr.Button("数据构建", size="sm",min_width=100)
                                btn3 = gr.Button("挂链")
                                with gr.Column(visible=False) as sub_col3:
                                    btn3_sub1 = gr.Button("数据抽取", size="sm",min_width=100)
                                    btn3_sub2 = gr.Button("数据构建", size="sm",min_width=100)
                                btn4 = gr.Button("扩展功能")
                                with gr.Column(visible=False) as sub_col4:
                                    btn4_sub1 = gr.Button("扩展", size="sm",min_width=100)
                                    btn4_sub2 = gr.Button("汇总", size="sm",min_width=100)


                with gr.Column(scale=6,variant="panel") as tabs_all:
                    tabs_1 = Chain_Data.tabs()
                    tabs_sub1_1 = Chain_Data.tabs_sub1()
                    tabs_sub1_2 = Chain_Data.tabs_sub2()
                    tabs_2 = Build_Chain.tabs()
                    tabs_sub2_1 = Build_Chain.tabs_sub1()
                    tabs_sub2_2 = Build_Chain.tabs_sub2()
                    tabs_3 = Attach_Chain.tabs()
                    tabs_sub3_1 = Attach_Chain.tabs_sub1()
                    tabs_sub3_2 = Attach_Chain.tabs_sub2()
                    tabs_4 = Extension.tabs()
                    tabs_sub4_1 = Extension.tabs_sub1()
                    tabs_sub4_2 = Extension.tabs_sub2()
                    with gr.Row(visible=True) as tabs_main_content:
                        # with gr.Row():
                        #     gr.Markdown("Select a type from the sidebar to see options.")
                        with gr.Row():
                            gr.ChatInterface(
                                fn=Gradio_util.ai_assistant,
                                title="AI 助手",
                                multimodal=True

                            )
                with gr.Column(min_width=100):
                    with gr.Row(visible=False) as main_content:
                        with gr.Row(visible=True):
                            gr.Markdown("# AI 助手")
                        # with gr.Row():
                        #     gr.Markdown("Select a type from the sidebar to see options.")
                        with gr.Row():
                            gr.ChatInterface(
                                fn=Gradio_util.ai_assistant,
                                multimodal=True,
                            )
                            # chatbot_widget = gr.Chatbot(label="Chatbot")
                            # user_id = gr.Textbox(label="对话主体",visible=False,value='user')
                            # question = gr.Textbox(label="对话框")
                            # send_button = gr.Button("发送")
                            #
                            # # 定义按钮的点击事件
                            # send_button.click(fn=Gradio_util.ai_assistant, inputs=[user_id, question], outputs=chatbot_widget)
                            #
                            # # 清空输入框
                            # send_button.click(fn=lambda: "", inputs=None, outputs=user_id)

            def extract_tabs_from_context(context):
                tabs_list = []
                for key, value in context.items():
                    if key.startswith("tabs_"):
                        tabs_list.append(value)
                    if key.startswith("sub"):
                        tabs_list.append(value)
                return tabs_list

            list_1 = extract_tabs_from_context(locals())
            list_1.append(main_content)

            #list = [tabs_1,tabs_sub1_1,tabs_sub1_2, tabs_2,tabs_sub2_1,tabs_sub2_2,tabs_3,tabs_sub3_1,tabs_sub3_2,tabs_4,tabs_sub4_1,tabs_sub4_2,main_content]
            # 为每个按钮绑定点击事件
            btn0.click(
                fn=lambda: Gradio_util.show_tabs("首页",tabs_all,tabs_main_content,main_content),
                outputs=list_1)

            btn1.click(
                fn=lambda: Gradio_util.show_tabs_btn(tabs_all,tabs_1, main_content,sub_col1),
                outputs=list_1)
            btn1_sub1.click(
                fn=lambda: Gradio_util.show_tabs("数据解析-子按钮1", tabs_all,tabs_sub1_1,main_content),
                outputs=list_1)
            btn1_sub2.click(
                fn=lambda: Gradio_util.show_tabs("数据解析-子按钮2", tabs_all,tabs_sub1_2,main_content),
                outputs=list_1)


            btn2.click(
                fn=lambda: Gradio_util.show_tabs_btn(tabs_all,tabs_2, main_content,sub_col2),
                outputs=list_1)
            btn2_sub1.click(
                fn=lambda: Gradio_util.show_tabs("建链-子按钮1", tabs_all, tabs_sub2_1, main_content),
                outputs=list_1)
            btn2_sub2.click(
                fn=lambda: Gradio_util.show_tabs("建链-子按钮2", tabs_all, tabs_sub2_2, main_content),
                outputs=list_1)


            btn3.click(
                fn=lambda: Gradio_util.show_tabs_btn(tabs_all,tabs_3, main_content,sub_col3),
                outputs=list_1)
            btn3_sub1.click(
                fn=lambda: Gradio_util.show_tabs("挂链-子按钮1", tabs_all, tabs_sub3_1, main_content),
                outputs=list_1)
            btn3_sub2.click(
                fn=lambda: Gradio_util.show_tabs("挂链-子按钮2", tabs_all, tabs_sub3_2, main_content),
                outputs=list_1)


            btn4.click(
                fn=lambda: Gradio_util.show_tabs_btn(tabs_all,tabs_4, main_content,sub_col4),
                outputs=list_1)
            btn4_sub1.click(
                fn=lambda: Gradio_util.show_tabs("扩展功能-子按钮1", tabs_all, tabs_sub4_1, main_content),
                outputs=list_1)
            btn4_sub2.click(
                fn=lambda: Gradio_util.show_tabs("扩展功能-子按钮2", tabs_all, tabs_sub4_2, main_content),
                outputs=list_1)
        return demo
