#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 13:45:00
# <AUTHOR> Assistant
# @File         : analyze_code_reuse.py
# @Description  : 分析批量更新是否正确复用了单个更新接口
"""

import ast
import os


def analyze_batch_update_reuse():
    """分析批量更新方法是否复用了单个更新接口"""
    
    service_file = "service_data_manage/service/data_manage_service.py"
    
    if not os.path.exists(service_file):
        print(f"❌ 文件不存在: {service_file}")
        return False
    
    print("=" * 60)
    print("代码复用分析")
    print("=" * 60)
    
    with open(service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    
    # 查找 CompanyUpdateService 类
    company_update_service = None
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef) and node.name == "CompanyUpdateService":
            company_update_service = node
            break
    
    if not company_update_service:
        print("❌ 未找到 CompanyUpdateService 类")
        return False
    
    print("✅ 找到 CompanyUpdateService 类")
    
    # 查找方法
    methods = {}
    for node in company_update_service.body:
        if isinstance(node, ast.FunctionDef):
            methods[node.name] = node
    
    print(f"✅ 找到 {len(methods)} 个方法: {list(methods.keys())}")
    
    # 检查 batch_update_companies 方法
    if "batch_update_companies" not in methods:
        print("❌ 未找到 batch_update_companies 方法")
        return False
    
    batch_method = methods["batch_update_companies"]
    print("✅ 找到 batch_update_companies 方法")
    
    # 分析方法调用
    method_calls = []
    for node in ast.walk(batch_method):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute):
                if (isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == "CompanyUpdateService"):
                    method_calls.append(node.func.attr)
    
    print(f"\n📊 batch_update_companies 方法中调用的 CompanyUpdateService 方法:")
    for call in set(method_calls):
        count = method_calls.count(call)
        print(f"  - {call}: {count} 次")
    
    # 检查是否复用了关键方法
    expected_calls = ["update_company_info", "get_company_info", "check_chi_name_duplicate"]
    reused_methods = []
    
    for expected in expected_calls:
        if expected in method_calls:
            reused_methods.append(expected)
            print(f"✅ 复用了 {expected} 方法")
        else:
            print(f"❌ 未复用 {expected} 方法")
    
    # 检查是否有重复的数据库操作代码
    print(f"\n🔍 检查是否避免了代码重复:")
    
    # 检查是否直接使用了 SQLUtil
    sqlutil_calls = []
    for node in ast.walk(batch_method):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute):
                if (isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == "SQLUtil"):
                    sqlutil_calls.append(node.func.attr)
    
    if sqlutil_calls:
        print(f"⚠️  batch_update_companies 中直接调用了 SQLUtil 方法: {set(sqlutil_calls)}")
        print("   这可能表示存在代码重复")
    else:
        print("✅ batch_update_companies 没有直接调用 SQLUtil，很好地复用了现有接口")
    
    # 总结
    print(f"\n📋 复用分析总结:")
    print(f"  - 复用的方法数量: {len(reused_methods)}/{len(expected_calls)}")
    print(f"  - 复用的方法: {reused_methods}")
    print(f"  - 直接数据库调用: {len(set(sqlutil_calls))} 个不同方法")
    
    # 判断复用程度
    reuse_score = len(reused_methods) / len(expected_calls)
    if reuse_score >= 0.8:
        print("🎉 批量更新方法很好地复用了现有接口！")
        return True
    elif reuse_score >= 0.5:
        print("⚠️  批量更新方法部分复用了现有接口")
        return True
    else:
        print("❌ 批量更新方法复用程度较低")
        return False


def analyze_method_complexity():
    """分析方法复杂度"""
    
    service_file = "service_data_manage/service/data_manage_service.py"
    
    with open(service_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"\n📏 方法复杂度分析:")
    
    # 简单的行数统计
    in_batch_method = False
    batch_method_lines = 0
    indent_level = 0
    
    for line in lines:
        stripped = line.strip()
        if "def batch_update_companies" in stripped:
            in_batch_method = True
            indent_level = len(line) - len(line.lstrip())
            continue
        
        if in_batch_method:
            current_indent = len(line) - len(line.lstrip())
            if stripped and current_indent <= indent_level and not stripped.startswith('#'):
                # 方法结束
                break
            if stripped and not stripped.startswith('#'):
                batch_method_lines += 1
    
    print(f"  - batch_update_companies 方法行数: {batch_method_lines}")
    
    if batch_method_lines < 100:
        print("✅ 方法长度合理，复用效果良好")
    elif batch_method_lines < 200:
        print("⚠️  方法较长，可能存在一些重复代码")
    else:
        print("❌ 方法过长，建议进一步重构")


def main():
    """主函数"""
    print("批量更新代码复用分析工具")
    
    if not os.path.exists("service_data_manage/service/data_manage_service.py"):
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    success = analyze_batch_update_reuse()
    analyze_method_complexity()
    
    if success:
        print(f"\n🎯 结论:")
        print("✅ batch_update_companies 方法成功复用了 update_company_info 接口")
        print("✅ 避免了代码重复，提高了维护性")
        print("✅ 保持了验证逻辑的一致性")
        
        print(f"\n💡 复用的优势:")
        print("- 减少代码重复")
        print("- 保持逻辑一致性") 
        print("- 提高维护性")
        print("- 降低出错概率")
    else:
        print(f"\n⚠️  建议:")
        print("- 进一步提高方法复用程度")
        print("- 减少直接的数据库操作")
        print("- 统一验证逻辑")
    
    return success


if __name__ == "__main__":
    import sys
    success = main()
    sys.exit(0 if success else 1)
