﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/01/16 11:42:55
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   annual_parse_and_serialize.py
@Project    :   tiance-industry-finance
'''
from configs.collection_config import CollectionConfig
from utils.milvus_util import MilvusUtil
import re
import requests
from service.llm_extract_name_service import Llm<PERSON><PERSON>per

if __name__ == '__main__':

    llm_helper = LlmHelper()
    
    url = "http://10.8.21.163:8866/annual_report_info_ext_with_file"
    milvus_util = MilvusUtil()
    collection_name = ''
    docs = milvus_util.query_all_doc(collection_name=CollectionConfig.ANNUAL_REPORT_MILVUS)
    file_names = [doc["file_title"] for doc in docs]
    file_names = list(set(file_names))
    print(file_names)
    total_files = len(file_names)
    print("total files: ", total_files)
    pattern = r"(\d{4})年"
    process_file = 0
    # exclude_files = ["比亚迪-2023年年度报告","宁德时代-2023年年度报告","赛力斯-2023年年度报告","卧龙电驱2023年年度报告（更正后）","广汇汽车服务集团股份公司2023年年度报告"]
    exclude_files = []
    # print(file_name)
    for file_name in file_names:
        if file_name in exclude_files:
            print("has processed")
            process_file = process_file+1
            continue
        process_file = process_file+1
        print(process_file, "/", total_files)
        years = re.findall(pattern, file_name)
        if '-' in file_name:
            # company_name = file_name.split("-")[0].replace("st","").replace("ST","").replace("*","")
            # 2025 05 20 因为标题不确定性，全部改为大模型抽取
            company_name = llm_helper.extract_name(pdf_filename=file_name)
        else :
            company_name = llm_helper.extract_name(pdf_filename=file_name)
        # if company_name != "南钢股份":
        #     continue
        print(company_name)
        print(years)

        response = requests.post(url, json={"company_name": company_name,"pdf_filename":file_name,"year":years[0],"industry_chain":"新材料"})
        print(response.json())

    # response = requests.post(url, json={"company_name": "比亚迪","pdf_filename":"比亚迪-2023年年度报告","year":"2023"})
    # print(response.json())

    # response = requests.post(url, json={"company_name": "杉杉股份","pdf_filename":"杉杉股份2023年年度报告（更正版）","year":"2023","industry_chain":"新能源汽车"})
    # print(response.json())

    # response = requests.post(url, json={"company_name": "宁德时代","pdf_filename":"宁德时代-2023年年度报告","year":"2023"})
    # print(response.json())

    # response = requests.post(url, json={"company_name": "赛力斯","pdf_filename":"赛力斯-2023年年度报告","year":"2023"})
    # print(response.json())
    
    # response = requests.post(url, json={"company_name": "卧龙电驱","pdf_filename":"卧龙电驱2023年年度报告（更正后）","year":"2023"})
    # print(response.json())

    # response = requests.post(url, json={"company_name": "广汇汽车服务集团股份公司","pdf_filename":"广汇汽车服务集团股份公司2023年年度报告","year":"2023"})
    # print(response.json())
    
        # break
    # print(docs
    # print(docs[0][0])
    # for doc in docs:
    #     print(doc)
    # print(len(docs))