﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/03/10 18:25:01
<AUTHOR>   W<PERSON><PERSON><PERSON> HONG 
@Email    :   <EMAIL>
@File    :   chain_to_excel.py
@Project    :   tiance-industry-finance
'''
# from config.collection_config import CollectionConfig
from utils.mongodb_util import MongodbUtil
# from Requirement_API.services.llm_extract_name_service import L<PERSON><PERSON><PERSON><PERSON>
from script.hundson_api import HundsonApi
from service.filter_company_rules_service import remove_unknown_symbol



import pandas as pd
is_net_cross_val = False
def process_data(input_data,source_list,companies_info):
    hundson_api = HundsonApi(is_update=False)
    rows = []
    filter_rows = []
    abb2id = {}
    id2info = {}
    for _id,abb_list in source_list.items():
        # print(_id,abb_list)
        for abb in abb_list:
            if abb not in abb2id:
                abb2id[abb] = []
            abb2id[abb].append(_id)
        report_info = MongodbUtil.query_doc_by_id("research_report_info",_id)
        # print(report_info)
        id2info[_id] = {"title":report_info["title"],"source_url":report_info["source_url"]}
    print(abb2id)
    print(id2info)
    abb2companyinfo = {}
    for row in companies_info:
        abb2companyinfo[row["abb"]] = row
    # 溯源
    # company_info["source_list"] = []
    # for _id, abbs in mongdb_id_companies.items():
    #     if company_info["abb"] in abbs:
    #         report_info = MongodbUtil.coll(CollectionConfig.RESEARCH_REPORT_LABEL_INFO).find_one({"_id": _id})
    #         source_info = {"source_title": report_info["title"], "source_name": report_info["file_source"], "source_type": "研报", "source_url": report_info["source_url"]}
    for key, companies in input_data.items():
        parts = key.split('|')
        # 处理产业链层级
        level_1 = parts[0] if len(parts) >= 1 else ''
        level_2 = parts[1] if len(parts) >= 2 else ''
        level_3 = parts[2] if len(parts) >= 3 else ''
        level_4 = parts[3] if len(parts) >= 4 else ''
        level_5 = parts[4] if len(parts) >= 5 else ''
        
        for company in companies:
            title = []
            for _id in abb2id[company.get('abb', '')]:
                title.append(id2info[_id]["title"])
            company["name"] = remove_unknown_symbol(company["name"])
            company["abb"] = remove_unknown_symbol(company["abb"])
            # company["key_company"] = remove_unknown_symbol(company["key_company"])
            # if(company_output_rules_filter_processor(company["abb"])):
            #     continue
            hundson_result = hundson_api.checkIsExist(company.get('name', ''),is_cache=True)
            if is_net_cross_val == True and (hundson_result["is_match"] == False and hundson_result["is_company_before_name"] == False):
                filter_row = {
                    '一级产业链': level_1,
                    '上中下游': level_2,
                    '一级产业链环节': level_3,
                    '二级产业链环节': level_4,
                    '三级产业链环节': level_5,
                    '企业名称': company.get('abb', ''),
                    '企业全称（库内）': company.get('name', ''),
                    '企业全称（恒生）':company["name"] if hundson_result["is_company_before_name"] == False else hundson_result["name_list"],
                    '研报/公告标题' : " | ".join(title),
                    "是否上市" : abb2companyinfo[company.get('abb', '')]["is_listed"] if company.get('abb', '') in abb2companyinfo else '',
                    "产业地位" : abb2companyinfo[company.get('abb', '')]["industry_position"] if company.get('abb', '') in abb2companyinfo else '',
                    "股票代码":abb2companyinfo[company.get('abb', '')]["stock_code"] if company.get('abb', '') in abb2companyinfo else '',
                    "所属省":abb2companyinfo[company.get('abb', '')]["province"] if company.get('abb', '') in abb2companyinfo else '',
                    "所属市":abb2companyinfo[company.get('abb', '')]["city"] if company.get('abb', '') in abb2companyinfo else '',
                    "恒生校验一致":"正确" if hundson_result["is_match"] else "错误",
                    "恒生校验是否为曾用名":"正确" if hundson_result["is_company_before_name"] else "错误",
                    "恒生搜索结果" : hundson_result["name_list"]
                    # '研报/公告来源' : id2info[abb2id[company.get('abb', '')]]["source_url"]
                }
                filter_rows.append(filter_row)
                continue
            row = {
                '一级产业链': level_1,
                '上中下游': level_2,
                '一级产业链环节': level_3,
                '二级产业链环节': level_4,
                '三级产业链环节': level_5,
                '企业名称': company.get('abb', ''),
                '企业全称（库内）': company.get('name', ''),
                '企业全称（恒生）':company["name"] if hundson_result["is_company_before_name"] == False else hundson_result["name_list"],
                '研报/公告标题' : " | ".join(title),
                "是否上市" : abb2companyinfo[company.get('abb', '')]["is_listed"] if company.get('abb', '') in abb2companyinfo else '',
                "产业地位" : abb2companyinfo[company.get('abb', '')]["industry_position"] if company.get('abb', '') in abb2companyinfo else '',
                "股票代码":abb2companyinfo[company.get('abb', '')]["stock_code"] if company.get('abb', '') in abb2companyinfo else '',
                "所属省":abb2companyinfo[company.get('abb', '')]["province"] if company.get('abb', '') in abb2companyinfo else '',
                "所属市":abb2companyinfo[company.get('abb', '')]["city"] if company.get('abb', '') in abb2companyinfo else '',
                "恒生校验一致":"正确" if hundson_result["is_match"] else "错误",
                "恒生校验是否为曾用名":"正确" if hundson_result["is_company_before_name"] else "错误",
                "恒生搜索结果" : hundson_result["name_list"]
                # '研报/公告来源' : id2info[abb2id[company.get('abb', '')]]["source_url"]
            }
            rows.append(row)
    
    return pd.DataFrame(rows),pd.DataFrame(filter_rows),

if __name__ == '__main__':

    # llm_helper = LlmHelper()
    
    # url = "http://10.8.21.163:8866/annual_report_info_ext_with_file"
    # milvus_util = MilvusUtil()
    collection_name = ''
    MongodbUtil.connect()
    docs = MongodbUtil.query_docs_by_condition(collection_name="key_companies_test",search_condition={'industry':{'$regex':"新材料"}})
    # print(docs)
    for doc in docs:
        industry = doc["industry"]
        print(doc["key_companies"])
        raw_data = doc["node_companies"]
        # print(raw_data)
            # 处理数据并导出Excel
        df,filter_df = process_data(raw_data,doc["mongdb_id_companies"],doc["key_companies"])
        if len(df) > 0 :
            df.to_excel(f'{industry}_6.xlsx', index=False, 
                        columns=['一级产业链', '上中下游', '一级产业链环节', 
                                '二级产业链环节', '三级产业链环节', 
                                '企业名称', '企业全称（库内）','企业全称（恒生）','研报/公告标题','是否上市','产业地位','股票代码','所属省','所属市',"恒生校验一致","恒生校验是否为曾用名","恒生搜索结果" ])
        
        if len(filter_df) > 0 :
            filter_df.to_excel(f'{industry}_filter_6.xlsx', index=False, 
                    columns=['一级产业链', '上中下游', '一级产业链环节', 
                            '二级产业链环节', '三级产业链环节', 
                            '企业名称', '企业全称（库内）','企业全称（恒生）',
                            '研报/公告标题','是否上市','产业地位','股票代码',
                            '所属省','所属市',"恒生校验一致","恒生校验是否为曾用名",
                            "恒生搜索结果" ])
                           


    # # 示例输入数据（需要符合Python字典语法）
    # input_data = {
    #     "新能源汽车|下游": [
    #         {
    #             "abb": "中科创达",
    #             "name": "中科创达软件股份有限公司",
    #             "is_listed": "是",
    #             "is_special": "否",
    #             "is_high_tech": "否"
    #         }
    #     ],
    #     "新能源汽车|下游|研发中心": [
    #         {
    #             "abb": "中科创达",
    #             "name": "中科创达软件股份有限公司",
    #             "is_listed": "是",
    #             "is_special": "否",
    #             "is_high_tech": "否"
    #         }
    #     ],
    #     "新能源汽车|下游|研发中心|研发软件": [
    #         {
    #             "abb": "中科创达",
    #             "name": "中科创达软件股份有限公司",
    #             "is_listed": "是",
    #             "is_special": "否",
    #             "is_high_tech": "否"
    #         }
    #     ]
    # }

    # # 处理数据并导出Excel
    # df = process_data(input_data)
    # df.to_excel('产业链数据.xlsx', index=False, 
    #             columns=['一级产业链', '上中下游', '一级产业链环节', 
    #                     '二级产业链环节', '三级产业链环节', 
    #                     '企业名称', '企业全称（库内）'])
    