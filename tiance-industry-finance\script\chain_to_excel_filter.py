﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/03/10 18:25:01
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   chain_to_excel.py
@Project    :   tiance-industry-finance
'''
# from config.collection_config import CollectionConfig
from utils.mongodb_util import MongodbUtil
# from Requirement_API.services.llm_extract_name_service import LlmHelper


import pandas as pd

def process_data(input_data,source_list):
    # hundson_api = HundsonApi()
    rows = []
    abb2id = {}
    id2info = {}
    for _id,abb_list in source_list.items():
        # print(_id,abb_list)
        for abb in abb_list:
            if abb not in abb2id:
                abb2id[abb] = []
            abb2id[abb].append(_id)
        report_info = MongodbUtil.query_doc_by_id("research_report_info",_id)
        # print(report_info)
        id2info[_id] = {"title":report_info["title"],"source_url":report_info["source_url"]}
    print(abb2id)
    print(id2info)
    for abb in input_data:
        title = []
        for _id in abb2id[abb]:
            title.append(id2info[_id]["title"])
        row = {'简称':abb,
        '研报/公告标题' : " | ".join(title),}
        rows.append(row)
    # company_info["source_list"] = []
    # for _id, abbs in mongdb_id_companies.items():
    #     if company_info["abb"] in abbs:
    #         report_info = MongodbUtil.coll(CollectionConfig.RESEARCH_REPORT_LABEL_INFO).find_one({"_id": _id})
    #         source_info = {"source_title": report_info["title"], "source_name": report_info["file_source"], "source_type": "研报", "source_url": report_info["source_url"]}
    # for key, companies in input_data.items():
    #     parts = key.split('|')
    #     # 处理产业链层级
    #     level_1 = parts[0] if len(parts) >= 1 else ''
    #     level_2 = parts[1] if len(parts) >= 2 else ''
    #     level_3 = parts[2] if len(parts) >= 3 else ''
    #     level_4 = parts[3] if len(parts) >= 4 else ''
    #     level_5 = parts[4] if len(parts) >= 5 else ''
        
    #     for company in companies:
    #         title = []
    #         for _id in abb2id[company.get('abb', '')]:
    #             title.append(id2info[_id]["title"])
    #         hundson_result = hundson_api.checkIsExist(company.get('name', ''),is_cache=False)
    #         row = {
    #             '一级产业链': level_1,
    #             '上中下游': level_2,
    #             '一级产业链环节': level_3,
    #             '二级产业链环节': level_4,
    #             '三级产业链环节': level_5,
    #             '企业名称': company.get('abb', ''),
    #             '企业全称（库内）': company.get('name', ''),
    #             '研报/公告标题' : " | ".join(title),
    #             "是否上市" : abb2companyinfo[company.get('abb', '')]["is_listed"],
    #             "产业地位" : abb2companyinfo[company.get('abb', '')]["industry_position"],
    #             "股票代码":abb2companyinfo[company.get('abb', '')]["stock_code"],
    #             "所属省":abb2companyinfo[company.get('abb', '')]["province"],
    #             "所属市":abb2companyinfo[company.get('abb', '')]["city"],
    #             "恒生校验一致":"正确" if hundson_result["is_match"] else "错误",
    #             "恒生校验是否为曾用名":"正确" if hundson_result["is_company_before_name"] else "错误",
    #             "恒生搜索结果" : hundson_result["name_list"]
    #             # '研报/公告来源' : id2info[abb2id[company.get('abb', '')]]["source_url"]
    #         }
    #         rows.append(row)
    
    return pd.DataFrame(rows)

if __name__ == '__main__':

    # llm_helper = LlmHelper()
    
    # url = "http://10.8.21.163:8866/annual_report_info_ext_with_file"
    # milvus_util = MilvusUtil()
    collection_name = ''
    MongodbUtil.connect()
    docs = MongodbUtil.query_docs_by_condition(collection_name="key_companies_test",search_condition={'industry':{'$regex':"新能源汽车"}})
    # print(docs)
    for doc in docs:
        industry = doc["industry"]
        print(doc["key_companies"])
        raw_data = doc["filter_companies"]
        # print(raw_data)
            # 处理数据并导出Excel
        df = process_data(raw_data,doc["mongdb_id_companies"])
        df.to_excel(f'{industry}_filter.xlsx', index=False, 
                    columns=['简称','研报/公告标题'])


    # # 示例输入数据（需要符合Python字典语法）
    # input_data = {
    #     "新能源汽车|下游": [
    #         {
    #             "abb": "中科创达",
    #             "name": "中科创达软件股份有限公司",
    #             "is_listed": "是",
    #             "is_special": "否",
    #             "is_high_tech": "否"
    #         }
    #     ],
    #     "新能源汽车|下游|研发中心": [
    #         {
    #             "abb": "中科创达",
    #             "name": "中科创达软件股份有限公司",
    #             "is_listed": "是",
    #             "is_special": "否",
    #             "is_high_tech": "否"
    #         }
    #     ],
    #     "新能源汽车|下游|研发中心|研发软件": [
    #         {
    #             "abb": "中科创达",
    #             "name": "中科创达软件股份有限公司",
    #             "is_listed": "是",
    #             "is_special": "否",
    #             "is_high_tech": "否"
    #         }
    #     ]
    # }

    # # 处理数据并导出Excel
    # df = process_data(input_data)
    # df.to_excel('产业链数据.xlsx', index=False, 
    #             columns=['一级产业链', '上中下游', '一级产业链环节', 
    #                     '二级产业链环节', '三级产业链环节', 
    #                     '企业名称', '企业全称（库内）'])
    