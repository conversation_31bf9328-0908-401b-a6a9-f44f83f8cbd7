﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/03/11 16:06:25
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   clean_output_database.py
@Project    :   tiance-industry-finance
'''
from utils.mongodb_util import MongodbUtil
from service.filter_company_rules_service import common_clean_process,company_output_rules_filter_processor,remove_unknown_symbol


def custom_clean_process(company_list:list) -> list:
    # 使用 while 循环删除偶数
    i = 0
    while i < len(company_list):
        company_list[i]["affiliate"] = remove_unknown_symbol(company_list[i]["affiliate"])
        if company_output_rules_filter_processor(company_list[i]["affiliate"]):
            del company_list[i]
        else:
            i += 1
    if len(company_list) == 0:
        return ['']
    return company_list

if __name__ == '__main__':
    MongodbUtil.connect()


    # clean annual_report_history
    docs = MongodbUtil.query_docs_by_condition(collection_name="annual_report_history")
    seen = set()
    # print(len(docs.count_documents()))
    # count = 0
    for doc in docs:
        # count = count+1
        if (doc["year"],doc["company_name"]) not in seen:
            seen.add((doc["year"],doc["company_name"]))
        else:
            continue
        # print("before=========")
        # print(doc["年报关联公司汇总"])
        for item in doc["年报关联公司汇总"]:
            # print(item)
            for key,value in item.items():
                if key == "is_keyword_hit":
                    continue
                # print(key,value)
                value = common_clean_process(value)
                item[key] = value
        # print("after=========")
        # print(doc["年报关联公司汇总"])
        # print("before=========")
        # print(doc["待入库具体信息"])
        # print(len(doc["待入库具体信息"]))
        doc["待入库具体信息"] = custom_clean_process(doc["待入库具体信息"])
        # print(doc["待入库具体信息"])
        # print(len(doc["待入库具体信息"]))
        # print("after=========")
        # print(doc)
        MongodbUtil.insert_one(collection_name="annual_report_history_clean",doc_content = doc)
    # print(count)
    # print(len(docs.count_documents()))
        









        # doc["年报关联公司汇总"]["母公司"] = common_clean_process(doc["年报关联公司汇总"]["母公司"])
        # doc["年报关联公司汇总"]["子公司"] = common_clean_process(doc["年报关联公司汇总"]["子公司"])
        # doc["年报关联公司汇总"]["联营企业和合营企业"] = common_clean_process(doc["年报关联公司汇总"]["联营企业和合营企业"])
        # doc["年报关联公司汇总"]["其他关联方"] = common_clean_process(doc["年报关联公司汇总"]["其他关联方"])
        # doc["年报关联公司汇总"]["出售商品或提供劳务"] = common_clean_process(doc["年报关联公司汇总"]["出售商品或提供劳务"])
        # doc["年报关联公司汇总"]["采购商品或接受劳务"] = common_clean_process(doc["年报关联公司汇总"]["采购商品或接受劳务"])
        # doc["年报关联公司汇总"]["主要租赁或出租方"] = common_clean_process(doc["年报关联公司汇总"]["主要租赁或出租方"])
        # doc["年报关联公司汇总"]["担保方"] = common_clean_process(doc["年报关联公司汇总"]["担保方"])
        # doc["年报关联公司汇总"]["被担保方"] = common_clean_process(doc["年报关联公司汇总"]["被担保方"])
        # doc["年报关联公司汇总"]["资金拆入"] = common_clean_process(doc["年报关联公司汇总"]["资金拆入"])
        # doc["年报关联公司汇总"]["资产转让"] = common_clean_process(doc["年报关联公司汇总"]["资产转让"])
        # doc["年报关联公司汇总"]["债务重组"] = common_clean_process(doc["年报关联公司汇总"]["债务重组"])
        # doc["年报关联公司汇总"]["资金拆出企业"] = common_clean_process(doc["年报关联公司汇总"]["资金拆出企业"])
        # print(doc["年报关联公司汇总"])
        