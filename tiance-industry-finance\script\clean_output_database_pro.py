﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/03/11 16:06:25
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   clean_output_database.py
@Project    :   tiance-industry-finance
'''
from utils.mongodb_util import MongodbUtil
from service.filter_company_rules_service import common_clean_process,company_output_rules_filter_processor,remove_unknown_symbol

def custom_clean_process(company_list:list) -> list:
    # 使用 while 循环删除偶数
    i = 0
    while i < len(company_list):
        company_list[i]["affiliate"] = remove_unknown_symbol(company_list[i]["affiliate"])
        if company_output_rules_filter_processor(company_list[i]["affiliate"]):
            del company_list[i]
        else:
            i += 1
    if len(company_list) == 0:
        return ['']
    return company_list


if __name__ == '__main__':
    MongodbUtil.connect()


    # clean annual_report_history
    docs = MongodbUtil.query_docs_by_condition(collection_name="company_relation_history")
    seen = set()
    count_all = 0
    for doc in docs:
        if doc["company_name"]not in seen:
            seen.add(doc["company_name"])
        else:
            continue
        # print(doc)
        count_all = count_all +1
        count = 0
        print("=====before=====")
        print(doc["公司数量"])
        print(doc["供应商汇总"])
        print(doc["同行业公司汇总"]["same_industry_list"])
        print(doc["客户汇总"])
        print(doc["待入库具体信息"])
        for year,companies in doc["供应商汇总"].items():
            if(year == "is_keyword_hit"):
                continue
            # print(year,companies)
            companies = common_clean_process(companies)
            doc["供应商汇总"][year] = companies
            count = count + len(companies)

        doc["同行业公司汇总"]["same_industry_list"] = common_clean_process(doc["同行业公司汇总"]["same_industry_list"])

        for year,companies in doc["客户汇总"].items():
            if(year == "is_keyword_hit"):
                continue
            print(year,companies)
            companies = common_clean_process(companies)
            doc["客户汇总"][year] = companies
            count = count + len(companies)
        count = count + len(doc["同行业公司汇总"]["same_industry_list"])
        doc["公司数量"] = count

        doc["待入库具体信息"] = custom_clean_process(doc["待入库具体信息"])
        
        print("=====after=====")
        print(doc["公司数量"])
        print(doc["供应商汇总"])
        print(doc["同行业公司汇总"]["same_industry_list"])
        print(doc["客户汇总"])
        print(doc["待入库具体信息"])
        MongodbUtil.insert_one(collection_name="company_relation_history_clean",doc_content = doc)

        # if (doc["year"],doc["company_name"]) not in seen:
        #     seen.add((doc["year"],doc["company_name"]))
        # else:
        #     continue
        # print("before=========")
        # print(doc["年报关联公司汇总"])
        # print("after=========")
        # print(doc["年报关联公司汇总"])
        # print("before=========")

        # MongodbUtil.insert_one(collection_name="company_relation_history_clean",doc_content = doc)
    print(count_all)
        