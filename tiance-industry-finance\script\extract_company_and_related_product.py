#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
从发票、海关、授信报告文档抽取出公司名称及其对应产品，保存到MongoDB
# @Time    : 2025/4/14 15:34
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : extract_company_and_related_product.py
# @Project : tiance-industry-finance
"""
import asyncio
import json
import re

import pandas as pd
# from pyasn1_modules.rfc3709 import LogotypeInfo

from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from service.kb_service import KbService
from typing import Dict, List
from tqdm import tqdm

from configs.collection_config import CollectionConfig
from configs.prompt_config import PromptConfig
from configs.run_config import RunConfig
from configs.model_config import ModelConfig

from service.llm_service import Llm_Service

from entity.message_entity import SystemMessage, UserMessage, MessageConverter

LogUtil.init("extract_company_and_related_product")
MongodbUtil.connect()
kb_service = KbService()

# TODO 多页解析

# TODO DEBUG
EXTRACT_CONFIG = [
    {
        "doc_type": "发票文档",
        "collection_name": CollectionConfig.INVOICE_LABEL_INFO,
        "company_name_keys": ["销售方名称"],
        "product_name_keys": ["货物或应税劳务", "服务名称"]
    },
    {
        "doc_type": "海关文档",
        "collection_name": CollectionConfig.CUSTOMS_LABEL_INFO,
        "company_name_keys": ["境内发货人", "托运人"],
        "product_name_keys": ["商品名称及规格型号", "货物名称"]
    },
    {
        "doc_type": "授信报告文档",
        "collection_name": CollectionConfig.CREDIT_REPORT_LABEL_INFO,
        "company_name_keys": ["申请人名称"],
        "product_name_keys": ["主要产品"]
    }
]


def load_json_from_str(json_str):
    json_str = json_str.replace("\\n", "\n")
    json_str = json_str.replace("\\t", "\t")
    json_str = json_str.replace("\\'", "\'")
    json_str = json_str.replace('\\"', '\"')
    match = re.match(r".*({.*}).*", json_str, re.DOTALL)  # `.+` 默认不匹配换行符。如果 JSON 数据跨越多行，需要使用 `re.DOTALL` 标志来匹配换行符。
    if match:
        json_str = match.group(1)
        try:
            json_obj = json.loads(json_str)
            return json_obj
        except Exception as e:
            detail = f"失败详情：{str(e)}"
            LogUtil.error(msg=detail)
            LogUtil.info(f"json匹配异常，返回None。json_str: {json_str}")
            return None
    else:
        LogUtil.info(f"未匹配到json，返回None。json_str: {json_str}")
        return None


async def extract_multi_thread(config_dict: Dict[str, str], thread_num=None):
    try:
        doc_list = MongodbUtil.query_all_doc(config_dict["collection_name"])
        tasks = []
        doc_type_list = []
        milvus_collection_name_list = []
        file_title_list = []
        mongodb_id_list = []

        for doc_info in tqdm(doc_list, desc="doc_list"):
            # print(doc_info)
            doc_type = config_dict['doc_type']
            milvus_collection_name = doc_info['milvus_collection_name']
            file_title = doc_info['file_title']
            mongodb_id = doc_info['_id']

            LogUtil.info("1.检索文本块")
            search_res_list = await kb_service.search_knowledge_by_question(
                collection_name=milvus_collection_name,
                question='',
                limit_top_k=1,
                expr=f"mongodb_id == '{mongodb_id}'"
            )
            LogUtil.info(f"doc_info: {doc_info}")
            LogUtil.info(f"search_res_list: {search_res_list}")

            if search_res_list is None or len(search_res_list) == 0:
                LogUtil.info("未能在向量库中检索到信息，跳过")
                continue
            doc_content_str = search_res_list[0]['chunk_content_father']
            LogUtil.info(f"doc_content_str: {doc_content_str}")

            LogUtil.info("2.组装提示词")
            messages = []
            system_prompt = PromptConfig.EXTRACT_COMPANY_AND_RELATED_PRODUCT_SYSTEM_PROMPT
            user_prompt = PromptConfig.EXTRACT_COMPANY_AND_RELATED_PRODUCT_USER_PROMPT.format(
                company_name_keys=config_dict['company_name_keys'],
                product_name_keys=config_dict['product_name_keys'],
                text_content=doc_content_str
            )
            messages.append(SystemMessage(system_prompt))
            messages.append(UserMessage(user_prompt))
            messages = MessageConverter.convert_messages(messages)
            LogUtil.info("抽取提示词：" + str(messages))

            # 构造线程池任务，使用大模型抽取
            LogUtil.info("3.调用大模型生成回答")
            model = RunConfig.MAX_LLM_MODEL_NAME
            llm_service = Llm_Service(model)
            tasks.append(llm_service.answer_question(messages, ModelConfig.MAX_LLM_MODEL_NAME, max_tokens=8192))

            doc_type_list.append(doc_type)
            # milvus_collection_name_list.append(milvus_collection_name)
            file_title_list.append(file_title)
            mongodb_id_list.append(mongodb_id)

            # LogUtil.info("answer: " + str(answer))

        res_dict_list = []

        for task, doc_type, file_title, mongodb_id in zip(tasks, doc_type_list, file_title_list,
                                                          mongodb_id_list):
            result = await asyncio.gather(task)
            try:
                result_json = load_json_from_str(str(result))
                res_dict_list.append({
                    "doc_type": doc_type,
                    "collection_name": config_dict["collection_name"],
                    "mongodb_id": mongodb_id,
                    "file_title": file_title,
                    "company_name": result_json["company_name"],
                    "products": result_json["products"]
                })
            except Exception as e:
                res_dict_list.append({
                    "doc_type": doc_type,
                    "collection_name": config_dict["collection_name"],
                    "mongodb_id": mongodb_id,
                    "file_title": file_title,
                    "company_name": "",
                    "products": ""
                })
                detail = f"失败详情：{str(e)}"
                LogUtil.error(msg=detail)
        LogUtil.info(f"res_dict_list: " + str(res_dict_list))
        return res_dict_list


    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)


def save_data(res_dict_list, overwrite=False):
    """
    保存数据到MongoDB的extracted_company_and_related_product集合
    :param res_dict_list:
    :param overwrite:
    :return:
    """
    for res_dict in res_dict_list:
        # MongoDB以 mongodb_id 查询一次，不提取重复网页
        db_search_keyword = {
            'mongodb_id': res_dict['mongodb_id'],
        }

        if not overwrite:
            query_result = MongodbUtil.query_one_doc_by_condition(
                CollectionConfig.EXTRACTED_COMPANY_AND_RELATED_PRODUCT,
                db_search_keyword)
            if query_result:
                LogUtil.info(f"MongoDB已存在该条目，跳过。 res_dict: {res_dict}")
                continue

        MongodbUtil.del_docs_by_condition(CollectionConfig.EXTRACTED_COMPANY_AND_RELATED_PRODUCT, db_search_keyword)
        MongodbUtil.insert_one(CollectionConfig.EXTRACTED_COMPANY_AND_RELATED_PRODUCT, res_dict)


async def main():
    res_dict_list = []
    for config in EXTRACT_CONFIG:
        temp = await extract_multi_thread(config)
        res_dict_list.extend(temp)
    LogUtil.info(f"main() res_dict_list: " + str(res_dict_list))

    LogUtil.info("正在保存数据到MongoDB")
    save_data(res_dict_list)

    # 转换为 DataFrame
    df = pd.DataFrame(res_dict_list)
    # 保存为 Excel 文件
    df.to_excel("output.xlsx")
    LogUtil.info("Excel file 'output.xlsx' has been created.")

    return res_dict_list


if __name__ == '__main__':
    asyncio.run(main())

    # save_data([
    #     {
    #         "mongodb_id": "asd"
    #     }
    # ])

    # load_json_from_str(
    #     """
    #     {\\n  "company_name": "大族激光科技产业集团股\\'份有限公司",\n  "products": "激光雕刻机, 激光焊接机, 激光切割机, 激光器及相关元件, 机器人相关产品, 30W紫外纳秒激光器, 70W红外皮秒激光器, HAN\'S 801数控系统"\n}
    #     """
    # )
