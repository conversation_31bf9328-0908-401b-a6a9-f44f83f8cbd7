import pandas as pd
import json

# from pyarrow.error import detail


from api.routes.company_full_name_supplement import label_similar
# 读取Excel文件
df = pd.read_excel('E:/data/产业链关联产品&商品标签表.xlsx', engine='openpyxl')
result_dict = {}
items = []
selected_columns = df.columns[:8].tolist() + [df.columns[11]]
start_col_idx = 2  # 第3列
end_col_idx = 7  # 第8列

for col_idx in range(start_col_idx, end_col_idx + 1):
    current_column_name = df.columns[col_idx]
    print(current_column_name)

    for index, row in df.iterrows():
        value = row[current_column_name]  # 获取当前列的值

        if pd.notna(value):  # 如果值不为空
                val_str = str(value)

                first_col_value = str(row[df.columns[0]])  # 第一列
                second_col_value = str(row[df.columns[1]])  # 第二列
                tenth_col_value = str(row[df.columns[12]])  # 第十列

                # 组合数据
                result_dict[val_str] = {
                    '环节':current_column_name,
                    '产业链':first_col_value,
                    '上中下游':second_col_value,
                    '来源':tenth_col_value
                }

# 输出最终结果
print(result_dict)

matching_keys =[]    # 上游环节
matching_keys1 =[]   # 中游环节
matching_keys2 =[]   # 下游环节
for key, value in result_dict.items():
    # 检查'产业链'是否存在于内层字典且其值为'低空经济'
    if isinstance(value, dict) and value.get('产业链') == '直升机产业链'and value.get('上中下游') == '上游':
        matching_keys.append(key)
    if isinstance(value, dict) and value.get('产业链') == '直升机产业链'and value.get('上中下游') == '中游':
        matching_keys1.append(key)
    if isinstance(value, dict) and value.get('产业链') == '直升机产业链'and value.get('上中下游') == '下游':
        matching_keys2.append(key)

matching_keys = json.dumps(matching_keys, ensure_ascii=False)
matching_keys1 = json.dumps(matching_keys1, ensure_ascii=False)
matching_keys2 = json.dumps(matching_keys2, ensure_ascii=False)
print(matching_keys)
print(matching_keys1)
print(matching_keys2)
A=[    {"A001": ["轮胎", "刹车副", "传动轴承"]},    {"A002": ["机身原材料", "起落原材料"]},    {"A003": ["电子零部件", "结构件", "唯一"]},    {"A004": ["飞行控制系统", "航空铝合金", "低合金超高强度钢", "航空玻璃"]},    {"A005": ["碳纤维", "热塑性碳纤维", "特种线缆"]},    {"A006": ["舱门"]}]
B=[    {"A001": ["子系统", "通信系统", "传感系统", "传动系统"]},    {"A002": ["整机制造与集成", "机电系统", "航电系统", "光电系统"]},    {"A003": ["雷达系统", "旋翼系统", "旋翼毂"]},    {"A004": ["直升机", "运输直升机", "通用运输直升机", "旅客运输直升机", "起重直升机"]},    {"A005": ["民用直升机", "军用直升机", "武装直升机", "战斗勤务直升机", "公共服务直升机", "特种作业直升机", "教练直升机"]}]
C=[    {"A001": ["应用与服务", "民用", "军用"]},    {"A002": ["航空维修", "航空租赁"]},    {"A003": ["武装侦察", "战斗支援", "搜索救援"]},    {"A004": ["战场运输", "商业运输"]},    {"A005": ["石油和天然气勘探", "农业作业"]},    {"A006": ["管管旅游", "城市景物"]},    {"A007": ["医疗急救", "灾害救援", "消防灭火"]},    {"A008": ["航空救援"]}]
combined_list = A + B + C

# 重新分配键值
result = []
all_labels_in_order = []  # 用于保存所有标签的顺序

for i, item in enumerate(combined_list, start=1):
    for old_key, value in item.items():
        new_key = f"A00{i}"
        result.append({new_key: value})
        all_labels_in_order.extend(value)
print(all_labels_in_order)
print(result)

for group in result:
    for group_key, item_list in group.items():
        # 遍历当前分组下的所有项
        for item in item_list:
            if item in result_dict:
                # 为匹配到的项添加新的属性："分组": group_key
                result_dict[item]['分组'] = group_key

# print(result_dict)

headers = ['分组', '产业链', '上中下游', '环节', '标签', '来源']
rows = []

for label in all_labels_in_order:
    if label in result_dict and result_dict[label]['产业链']=='直升机产业链':
        details = result_dict[label]
        row = [
            details.get('分组', ''),   # 分组
            details['产业链'], # 产业链
            details['上中下游'], # 上中下游
            details['环节'],   # 环节
            label,             # 标签
            details['来源']    # 来源
        ]
        rows.append(row)
df = pd.DataFrame(rows, columns=headers)
output_file = 'E:/data/产业链环节待跑相似/直升机产业链.xlsx'
df.to_excel(output_file, index=False, engine='openpyxl')
