﻿import requests
import base64
import json
import re
from utils.mongodb_util import MongodbUtil
def clean_company_names(company_name):
    MongodbUtil.connect()
    # 步骤1：移除所有括号及其中内容，替换为分隔符
    temp = re.sub(r'\(.*?\)', '_', company_name)
    # 步骤2：合并连续分隔符为单个下划线
    temp = re.sub(r'_+', '_', temp)
    # 步骤3：去除首尾的分隔符
    temp = temp.strip('_')
    # 步骤4：处理特殊场景（如保留未匹配的括号）
    return temp
class HundsonApi:
    def __init__(self,app_key="44d12e74-90c8-47f5-9abd-fd5aa8778d00"
                 ,app_secrect="4b79b3e5-1ffd-4967-870f-7521bf36e73e",is_update=True):
        self.app_key = app_key
        self.app_secrect = app_secrect
        if is_update:
            self.token = self.getToken(app_key=app_key,app_secrect=app_secrect)
        else:
            self.token = ''
        self.companyarchives_url = "https://sandbox.hs.net/zhimou/v1/companyinfo/companyarchives"
        self.companysearch = "https://sandbox.hs.net/zhimou/v1/companyinfo/companysearch"
        self.companytabsearch = "https://sandbox.hs.net/zhimou/v1/companyinfo/companytabsearch"
        self.companytagsearch = "https://sandbox.hs.net/zhimou/v1/companyinfo/companytag"
        self.lawsregulations_url="https://sandbox.hs.net/zhimou//v1/law/lawsregulations"
        self.legallist_url="https://sandbox.hs.net/zhimou/v1/legalaction/legallist"
        self.caseinfolist_url="https://sandbox.hs.net/zhimou/v1/legalaction/caseinfolist"
        self.judgementlist_url="https://sandbox.hs.net/zhimou/v1/legalaction/judgementlist"
        self.epoperabnorminfo_url= "https://sandbox.hs.net/zhimou/v1/corporateaction/epoperabnorminfo"
        self.equityfreezelist_url= "https://sandbox.hs.net/zhimou/v1/huarong/equityfreezelist"

        self.newsinceritylist_url= "https://sandbox.hs.net/zhimou/v1/legalaction/newsinceritylist"
        self.company_info_collection = "hundson_companyarchives"
        self.company_search_collection = "hundson_companysearch"
        self.company_tab_search_collection = "hundson_companytabsearch"
        self.is_update = is_update
    def getToken(self,app_key,app_secrect):
        bytesString = (app_key+':'+app_secrect).encode(encoding="utf-8")
        url = 'https://sandbox.hscloud.cn/oauth2/oauth2/token'
        header = {'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': 'Basic '+str(base64.b64encode(bytesString),encoding="utf-8")}
        field = {'grant_type' : 'client_credentials'}
        r = requests.post(url,data=field,headers=header)
        if r.json().get('access_token') :
            token = r.json().get('access_token')
            print("获取公共令牌:"+str(token))
            return token
        else :
            print("获取公共令牌失败")
            return ""
    def postOpenApi(self,url,params):
        if self.token == '':
            self.token = self.getToken(app_key=app_key,app_secrect=app_secrect)
        header = {'Content-Type': 'application/x-www-form-urlencoded',
            'Authorization': 'Bearer '+self.token}
        # print(url)
        r = requests.post(url,data=params,headers=header)
        print(r)
        return r.json().get('data')
    def getCompanyInfo(self,enterprise_code=""):
        results = self.postOpenApi(self.companyarchives_url,f"enterprise_code={enterprise_code}")
        print(results)
        if results is None:
            return []
        for result in results:
            _id = self.clean_company_names(f"{result['company_name']}_{result['enterprise_code']}_{result['credit_code']}")
            cur = MongodbUtil.del_doc_by_id(self.company_info_collection,doc_id=_id)
            if(cur is not None):
                MongodbUtil.del_doc_by_id(collection_name=self.company_info_collection,doc_id=_id)
            result["_id"] = _id
            MongodbUtil.insert_one(collection_name=self.company_info_collection,doc_content=result)


        return results
    def companyTabSearch(self,company_name=""):
        # company_name = company_name.replace('(','（').replace(')','）')
        results = self.postOpenApi(self.companytabsearch,f"keyword={company_name}")
        result_archives = []
        # print(results)
        if results is None:
            return [],[]
        for result in results:
            archive = self.getCompanyInfo(result["enterprise_code"])
            result_archives.extend(archive)
            _id = self.clean_company_names(f"{result['company_name']}_{result['enterprise_code']}_{result['credit_code']}")
            cur = MongodbUtil.del_doc_by_id(self.company_tab_search_collection,doc_id=_id)
            if(cur is not None) :
                MongodbUtil.del_doc_by_id(collection_name=self.company_tab_search_collection,doc_id=_id)
            result["_id"] = _id
            MongodbUtil.insert_one(collection_name=self.company_tab_search_collection,doc_content=result)
        return results,result_archives
    def companySearch(self,company_name=""):
        # company_name = company_name.replace('(','（').replace(')','）')
        results = self.postOpenApi(self.companysearch,f"company_name={company_name}")
        result_archives = []
        print(results)
        for result in results:
            archive = self.getCompanyInfo(result["enterprise_code"])
            result_archives.extend(archive)
            _id = self.clean_company_names(f"{result['company_name']}_{result['enterprise_code']}_{result['credit_code']}")
            cur = MongodbUtil.del_doc_by_id(self.company_search_collection,doc_id=_id)
            if(cur is not None):
                MongodbUtil.del_doc_by_id(collection_name=self.company_search_collection,doc_id=_id)
            result["_id"] = _id
            MongodbUtil.insert_one(collection_name=self.company_search_collection,doc_content=result)

        return results,result_archives
    def companySearchCache(self,company_name=""):
        company_name = re.escape(company_name)
        print(company_name)
        # cur = MongodbUtil.query_docs_by_condition(self.company_search_collection,search_condition={"company_name":{'$regex':company_name}})
        cur = MongodbUtil.query_docs_by_condition(self.company_search_collection,search_condition={"company_name": company_name})
        results = list(cur)
        result_archives = []
        # print(cur)
        if len(results) == 0 :
            results,result_archives = self.companySearch(company_name)
        else:
            for result in results:
                cur = MongodbUtil.query_docs_by_condition(self.company_info_collection,search_condition={"enterprise_code":result["enterprise_code"]})
                result_archives.extend(list(cur))
        return results,result_archives
    def companyTabSearchCache(self,company_name="",is_update=True):
        company_name = re.escape(company_name)
        # print(company_name)
        # cur = MongodbUtil.query_docs_by_condition(self.company_tab_search_collection,search_condition={"company_name":{'$regex':company_name}})
        # cur = MongodbUtil.query_docs_by_condition(self.company_search_collection,search_condition={"company_name": company_name})
        cur = MongodbUtil.query_docs_by_condition(self.company_tab_search_collection,search_condition={
                                                                                    "$or": [
                                                                                    { "company_name": company_name },
                                                                                    { "company_before_names": company_name }]})
        is_update = self.is_update
        results = list(cur)
        result_archives = []
        # print(cur)
        if len(results) == 0 and is_update:
            print("search net")
            results,result_archives = self.companyTabSearch(company_name,is_update)
        else:
            for result in results:
                cur = MongodbUtil.query_docs_by_condition(self.company_info_collection,search_condition={"enterprise_code":result["enterprise_code"]})
                results = list(cur)
                if len(results) == 0 and is_update:
                    print("search net")
                    archive = self.getCompanyInfo(result["enterprise_code"])
                    
                    _id = self.clean_company_names(f"{result['company_name']}_{result['enterprise_code']}_{result['credit_code']}")
                    cur = MongodbUtil.del_doc_by_id(self.company_search_collection,doc_id=_id)
                    if(cur is not None):
                        MongodbUtil.del_doc_by_id(collection_name=self.company_search_collection,doc_id=_id)
                    result["_id"] = _id
                    MongodbUtil.insert_one(collection_name=self.company_search_collection,doc_content=result)

                    results = archive
                result_archives.extend(results)
        return results,result_archives
    def checkIsExist(self,company_name:str,is_cache = False) -> dict:
        is_company_before_name = False
        is_match = False
        name_list = ''
        best_match = ''
        results = []
        results_search = []
        before_name = []
        state = '-'
        area_name = '-'
        if is_cache:
            results_search,results = self.companyTabSearchCache(company_name=company_name)
        else :
            results_search,results = self.companyTabSearch(company_name=company_name)
        if(len(results) > 0):
            for result in results:
                is_matched = False
                first_name = result["company_name"]
                # name_list = first_name
                # 括号全称匹配，统一替换返回来的内容为中文括号
                # name_list = [name.replace('(','（').replace(')','）') for name in first_name]
                name_list = first_name.replace('(','（').replace(')','）')
                # print(result["company_before_names"])
                if company_name == name_list:
                    is_match = True
                    is_matched = True
                    state = result["state"]
                    area_name = result["area_name"]
                if result["company_before_names"] != '' and company_name in result["company_before_names"]:
                    is_company_before_name = True
                    before_name.extend(result["company_before_names"])
                    state = result["state"]
                    area_name = result["area_name"]
                    is_matched = True
                    
                if is_matched:
                    break

        return {"is_company_before_name":is_company_before_name,
                "is_match":is_match,
                "name_list":name_list,
                "before_name":before_name,
                "state" : state,
                "area_name" : area_name
                }
    def companyTagSearch(self,company_code=""):
        results = self.postOpenApi(self.companytagsearch,f"company_code={company_code}")
        return results

    def clean_company_names(self,company_name):
        # 步骤1：移除所有括号及其中内容，替换为分隔符
        temp = re.sub(r'\(.*?\)', '_', company_name)
        # 步骤2：合并连续分隔符为单个下划线
        temp = re.sub(r'_+', '_', temp)
        # 步骤3：去除首尾的分隔符
        temp = temp.strip('_')
        # 步骤4：处理特殊场景（如保留未匹配的括号）
        return temp
    def lawsregulations(self,enterprise_code):
        print(enterprise_code)
        results = self.postOpenApi(self.lawsregulations_url,f"enterprise_code={enterprise_code}")
        result_archives = []
        print(results)
        return results
    def legallist(self,enterprise_code):
        print(enterprise_code)
        results = self.postOpenApi(self.legallist_url,f"enterprise_code={enterprise_code}")
        print(results)
        return results
    def caseinfolist(self,company_code):
        print(company_code)
        results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.caseinfolist_url,f"company_code={company_code}")
        print(results)
        return results
    def judgementlist(self,company_code):
        print(company_code)
        # results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.judgementlist_url,f"company_code={company_code}")
        print(results)
        return results
    def epoperabnorminfo(self,enterprise_code):
        print(enterprise_code)
        # results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.epoperabnorminfo_url,f"enterprise_code={enterprise_code}")
        print(results)
        return results
    
    def newsinceritylist(self,keyword):
        print(keyword)
        # results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.newsinceritylist_url,f"keyword={keyword}")
        print(results)
        return results
    def newsinceritylist2(self,company_code):
        print(company_code)
        # results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.newsinceritylist_url,f"party_company_code={company_code}")
        print(results)
        return results
    
    def newsinceritylist3(self,enterprise_code):
        print(enterprise_code)
        # results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.newsinceritylist_url,f"party_enterprise_code={enterprise_code}")
        print(results)
        return results
    def equityfreezelist(self,enterprise_code):
        print(enterprise_code)
        # results = self.postOpenApi(self.legallist_url,f"company_code={company_code}")
        results = self.postOpenApi(self.equityfreezelist_url,f"enterprise_code={enterprise_code}")
        print(results)
        return results
    
    
    
    
if __name__ == '__main__':
    company_names = ['(09.5.26注销)蓬莱市管道液化气按装公司',"蓬莱市糖酒副食品有限公司名酒之家","(1)广东省茂名五金交电采购批发站(2)茂名市五金交电公司"]
    for name in company_names:
        print(clean_company_names(name).split("_"))
    api = HundsonApi()
    # api.getCompanyInfo("ETP00012A84D")
    # api.companySearch("恒生电子")
    # print(api.checkIsExist("恒生电子"))
    # print(api.checkIsExist("安徽蚌埠恒生电子有限公司"))
    # print(api.checkIsExist("长沙白裕电子商务有限公司"))
    # print(api.checkIsExist("杭州贤达内燃机配件厂"))
    # result,result2 = api.companySearchCache("安徽蚌埠恒生电子有限公司")
    # api.companyTabSearch("长沙恒生电子商务有限公司")
    # api.companyTabSearch("四川雅化实业集团股份有限公司")
    
    # print(api.companyTabSearch("安徽华源医药股份有限公司"))
    # print(api.companyTabSearchCache("安徽华源医药股份有限公司"))
    # print(api.companyTagSearch("91341200713902565T"))
    # print(api.checkIsExist("安徽华源医药股份有限公司"))
    # print(api.companySearch("海南太古可口可乐饮料有限公司"))
    # print(api.lawsregulations(enterprise_code="ETP0001K8SE0"))

    # print(api.lawsregulations(enterprise_code="ETP000036PQH"))
    # print(api.lawsregulations(enterprise_code="ETP000019CEJ"))

    # print(api.legallist(enterprise_code="ETP0001K8SE0"))
    # print(api.legallist(enterprise_code="ETP000036PQH"))
    # print(api.legallist(enterprise_code="ETP000019CEJ"))

    # print(api.caseinfolist(company_code="85862473"))
    # print(api.caseinfolist(company_code="14034301"))
    
    # print(api.caseinfolist(company_code="11850090"))

    # print(api.judgementlist(company_code="85862473"))
    # print(api.judgementlist(company_code="14034301"))
    # print(api.judgementlist(company_code="11850090"))

    # print(api.newsinceritylist(keyword="宁夏塞上行"))
    # print(api.newsinceritylist(keyword="银川伊百盛生物工程有限公司"))
    # print(api.newsinceritylist(keyword="银川伊百盛清真食品有限公司"))
    # print(api.newsinceritylist(keyword="羊胎素"))

    # print(api.newsinceritylist2(company_code="85862473"))
    # print(api.newsinceritylist2(company_code="14034301"))
    # print(api.newsinceritylist2(company_code="11850090"))

    # print(api.newsinceritylist3(enterprise_code="ETP0001K8SE0"))
    # print(api.newsinceritylist3(enterprise_code="ETP000036PQH"))
    # print(api.newsinceritylist3(enterprise_code="ETP000019CEJ"))


    # print(api.equityfreezelist(enterprise_code="ETP0001K8SE0"))
    # print(api.equityfreezelist(enterprise_code="ETP000036PQH"))
    print(api.equityfreezelist(enterprise_code="ETP000019CEJ"))
    # print(api.newsinceritylist2(company_code="85862473"))
    # print(api.newsinceritylist2(company_code="14034301"))
    api.lawsregulations(enterprise_code="ETP000019CEJ")
    # print(result2)
