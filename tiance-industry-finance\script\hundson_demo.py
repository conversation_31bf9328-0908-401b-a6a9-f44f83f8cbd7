﻿# -*- coding: utf-8 -*-
import requests
import base64
import json
app_key = "44d12e74-90c8-47f5-9abd-fd5aa8778d00"
app_secrect = "4b79b3e5-1ffd-4967-870f-7521bf36e73e"
token = ''
def getToken(app_key,app_secrect):
	global token 
	bytesString = (app_key+':'+app_secrect).encode(encoding="utf-8")
	url = 'https://sandbox.hscloud.cn/oauth2/oauth2/token';
	header = {'Content-Type': 'application/x-www-form-urlencoded',
		'Authorization': 'Basic '+str(base64.b64encode(bytesString),encoding="utf-8")}
	field = {'grant_type' : 'client_credentials'}
	r = requests.post(url,data=field,headers=header)
	if r.json().get('access_token') :
		token = r.json().get('access_token')
		print("获取公共令牌:"+str(token))
		return
	else :
		print("获取公共令牌失败")
		exit
def postOpenApi(url,params,interfacename):
	global token
	print(token)
	header = {'Content-Type': 'application/x-www-form-urlencoded',
		'Authorization': 'Bearer '+token}
	r = requests.post(url,data=params,headers=header)
	print(r)
	print("功能名称："+interfacename)
	print("result = "+str(r.json().get('data')))
if __name__ == '__main__':
    getToken(app_key,app_secrect)
    url = "https://sandbox.hs.net/zhimou/v1/companyinfo/companysearch"
    # url = "https://sandbox.hs.net/zhimou/v1/companyinfo/companyarchives"
    # url = "https://open.hs.net/gildatacompany/v1/companyinfo/companysearch"
    # url = "https://sandbox.hscloud.cn/gildataastock/v1/astock/financial_analysis/per_share_index"
    # params = 'en_prod_code=600570.SH&report_date=2016-12-31&report_type=0&unit=0'
    params = 'company_name=恒生电子'
    postOpenApi(url, params, "公司每股指标")