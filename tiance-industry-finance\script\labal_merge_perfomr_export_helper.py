﻿import requests
import pandas as pd
if __name__ == '__main__':
    # url = "http://10.8.21.163:9029/label_merge_perform"
    url = "http://127.0.0.1:19029/label_merge_perform"
    json_data = {
  "mongodb_id": "338ea781843e45bd892593587724f63a",
  "model": "qwen2.5-72B",
  "prompt": "\n# 角色\n你是一个具有产业链背景知识的产业链标签合并助手\n\n## 技能\n### 技能 合并标签\n1. 每一行为一个标签\n2. 对每一个标签进行合并，如果语义上相同，便可以合并\n3. 以 JSON 格式输出合并后的标签result作为key，value为合并后的结果数组，数组元素为标签项，以及被替换的标签和对应用以替换的值，即{\"原标签\":[\"被替换标签1\",\"被替换标签2\"]}\n4. 输出的结构类似于{\"result\":[\"标签项1\",\"标签项2\"],\"标签项1\":[\"被替换标签1\",\"被替换标签2\"],\"标签项2\":[\"被替换标签3\",\"被替换标签4\"]}\n\n## 限制:\n- 只专注于标签的合并，拒绝回答与标签合并无关的问题。\n- 输出的 JSON 格式必须严格按照要求，不能有格式错误。\n- 确保信息的准确性和客观性，只整合已有信息，不进行主观猜测或添加未经证实的内容，不新增标签。\n- 一个标签只能被一个标签替换\n- 只输出 JSON 结果，必须具有result作为key，value为合并后的结果数组，数组元素为标签项。\n ",
  "merge_type": "Frequency",
  "is_ai_extend": False
}
    json_data = {
    "mongodb_id": "123456767",
    "model": "qwen2.5-72B",
    "prompt": "\n# 角色\n你是一个具有产业链背景知识的产业链标签合并助手\n\n## 技能\n### 技能 合并标签\n1. 每一行为一个标签\n2. 对每一个标签进行合并，如果语义上相同，便可以合并\n3. 以 JSON 格式输出合并后的标签result作为key，value为合并后的结果数组，数组元素为标签项，以及被替换的标签和对应用以替换的值，即{\"原标签\":[\"被替换标签1\",\"被替换标签2\"]}\n4. 输出的结构类似于{\"result\":[\"标签项1\",\"标签项2\"],\"标签项1\":[\"被替换标签1\",\"被替换标签2\"],\"标签项2\":[\"被替换标签3\",\"被替换标签4\"]}\n\n## 限制:\n- 只专注于标签的合并，拒绝回答与标签合并无关的问题。\n- 输出的 JSON 格式必须严格按照要求，不能有格式错误。\n- 确保信息的准确性和客观性，只整合已有信息，不进行主观猜测或添加未经证实的内容，不新增标签。\n- 一个标签只能被一个标签替换\n- 只输出 JSON 结果，必须具有result作为key，value为合并后的结果数组，数组元素为标签项。\n ",
    "merge_type": "Frequency",
    "is_ai_extend": True,
    "is_cached":False
    }
    json_data = {
  "mongodb_id": "921557220d4745c3bee6c8741ca09a7e",
  "model": "qwen2.5-72B",
  "prompt": "\n\n{你是一个产业链合并助手，能够准确地整合不同来源的产业链信息，给出最完整、最准确、最客观的产业链结构}\n\n## 技能\n### 技能 整合产业链结构\n1. 分析不同来源的产业链信息\n2. 对各个部分的内容进行合并和整理，去除重复项，确保结构的完整性和准确性，满足MECE原则，产业链结构相互独立，完全穷尽\n3. 以 JSON 格式输出合并后的产业链结构## 限制:\n- 只专注于产业链结构的合并，拒绝回答与产业链无关的问题。\n- 输出的 JSON 格式必须严格按照要求，不能有格式错误。\n- 只输出 JSON 结果。\n ",
  "merge_type": "Frequency",
  "is_ai_extend": False,
  "is_cached": False
}
    json_data = {
  "mongodb_id": "ad7f059c7cf34212adeb81f6f74aff03",
  "model": "qwen2.5-72B",
  "prompt": "你是一个专业的产业链合并助手，能够准确地整合不同来源的产业链信息，给出最完整、最准确、最客观的产业链结构。请返回最精简的结果",
  "merge_type": "Source",
  "is_ai_extend": False,
  "is_cached": False
}
    json_data = {
  "mongodb_id": "cb24fb65ad5b40fd950a700f085f3769",
  "model": "DeepSeek-R1-Distill-Qwen-32B",
  "prompt": "你是一个专业的产业链合并助手，能够准确地整合不同来源的产业链信息，给出最完整、最准确、最客观的产业链结构。请返回最精简的结果",
  "merge_type": "Source",
  "is_ai_extend": False,
  "is_cached": False
}
#     json_data={
#     "mongodb_id": "2a7212695c994d0d9f80d81c4b00f5a1",
#     "model": "DeepSeek-R1-Distill-Qwen-32B",
#     "prompt": "你是一个专业的产业链合并助手，能够准确地整合不同来源的产业链信息，给出最完整、最准确、最客观的产业链结构，请返回最精简的结果",
#     "merge_type": "",
#     "is_ai_extend": True,
#     "is_cached": False
# }

    # json_data ={"mongodb_id":"3d3d958030944474be0415611a667fc5","model":"qwen2.5-72B","prompt":"你是一个专业的产业链合并助手，能够准确地整合不同来源的产业链信息，给出最完整、最准确、最客观的产业链结构。","merge_type":"Frequency","is_ai_extend":False}
    
    # json_data = {"mongodb_id":"01030debdfb34ded970f7330b579ea97","model":"qwen2.5-72B","prompt":"\n# 角色\n你是一个具有产业链背景知识的产业链标签合并助手\n\n## 技能\n### 技能 合并标签\n1. 每一行为一个标签\n2. 对每一个标签进行合并，如果语义上相同，便可以合并\n3. 以 JSON 格式输出>合并后的标签result作为key，value为合并后的结果数组，数组元素为标签项，以及被替换的标签和对应用以替换的值，即{\"原标签\":[\"被替换标签1\",\"被替换标签2\"]}\n4. 输出的结构类似于{\"result\":[\"标签项1\",\"标签项2\"],\"标签项1\":[\"被替换标签1\",\"被替换标签2\"],\"标签项2\":[\"被替换标签3\",\"被替换标签4\"]}\n\n## 限制:\n- 只专注于标签的合并，拒绝回答与标签>合并无关的问题。\n- 输出的 JSON 格式必须严格按照要求，不能有格式错误。\n- 确保信息的准确性和客观性，只整合已有信息，不进行主观猜测或添加未经证实的内容，不新增标签。\n- 一个标签只能被一个标签替换\n- 只输出 JSON 结果，必须具有result作为key，value为合并后的结果数组，数组元素为标签项。\n","merge_type":"","is_ai_extend":False}
    # # json_data = {
    # "mongodb_id": "123456767",
    # "model": "DeepSeek-R1-Distill-Qwen-32B",
    # "prompt": "\n# 角色\n你是一个具有产业链背景知识的产业链标签合并助手\n\n## 技能\n### 技能 合并标签\n1. 每一行为一个标签\n2. 对每一个标签进行合并，如果语义上相同，便可以合并\n3. 以 JSON 格式输出合并后的标签result作为key，value为合并后的结果数组，数组元素为标签项，以及被替换的标签和对应用以替换的值，即{\"原标签\":[\"被替换标签1\",\"被替换标签2\"]}\n4. 输出的结构类似于{\"result\":[\"标签项1\",\"标签项2\"],\"标签项1\":[\"被替换标签1\",\"被替换标签2\"],\"标签项2\":[\"被替换标签3\",\"被替换标签4\"]}\n\n## 限制:\n- 只专注于标签的合并，拒绝回答与标签合并无关的问题。\n- 输出的 JSON 格式必须严格按照要求，不能有格式错误。\n- 确保信息的准确性和客观性，只整合已有信息，不进行主观猜测或添加未经证实的内容，不新增标签。\n- 一个标签只能被一个标签替换\n- 只输出 JSON 结果，必须具有result作为key，value为合并后的结果数组，数组元素为标签项。\n ",
    # "merge_type": "Frequency",
    # "is_ai_extend": True
    # }
    prompt2 = """# 角色
    #                 你是一个新能源产业链合并助手，能够准确地整合不同来源的新能源产业链信息，给出最完整，最准确、最容易的，专注于新能源产业链的整合，拒绝回答新能源产业链无关的问题，输出的json格式必须严格按照要求，不能有格式错误"""
    # mongodb_list = ["bc141372c98e433db2ff273e56c8dde7","123456767","338ea781843e45bd892593587724f63a","c94a93fd506b48568f7227f39c4a68e2"]
    from datetime import datetime
    type_en2zh = {"research_report":"研报","invoice":"发票","customs":"海关","credit_report":"授信报告"}
    # 记录开始时间
    start_time = datetime.now()
    mongodb_list = ["44e80ec166324684b95b22e41469ef1c"]
    for mongodb_id in mongodb_list:
        json_data["mongodb_id"] = mongodb_id
        # json_data["prompt"]=""
        # json_data["prompt"]=prompt2
        response = requests.post(url, json=json_data)
        # print(response.status_code)
        
        if response.status_code == 200:
            result_json = response.json()["data"]
            print(result_json)
            chain_structure = result_json["chain_structure"]
            # print(chain_structure)
            chain_structure_excel = []
            index_num = 0
            if len(chain_structure) > 0:
                for key,value in chain_structure.items():
                    index_num = index_num + 1 
                    nodes = key.split('|')
                    # print(nodes)
                    node_export = ["" for _ in range(8)]
                    for index,val in enumerate(nodes):
                        if index < 6:
                            node_export[index] = val
                        else:
                            break
                    # print(f"node_array:{node_export}")
                    product_array = [product_company["product_abb"] for product_company in value["product"]]
                    source_list = [source["title"] for source in value["source_list"]]
                    source_type_list = [type_en2zh[source["type"]] for source in value["source_list"]]
                    row = {
                        "序号":index_num,
                        "产业类型":node_export[0],
                        "上中下游":node_export[1],
                        "一级产业链环节":node_export[2],
                        "二级产业链环节":node_export[3],
                        "三级产业链环节":node_export[4],
                        "四级产业链环节":node_export[5],
                        "关联产品/商品/业务":",".join(product_array),
                        "数据来源类型（产业链）": ",".join(source_type_list),
                        "数据来源标题（产业链）": ",".join(source_list)
                    }
                    chain_structure_excel.append(row)
                output_data = pd.DataFrame(chain_structure_excel)
                output_data.to_excel(f'{mongodb_id}_产业链.xlsx', index=False, 
                        columns=['序号', '产业类型', '上中下游', 
                                '一级产业链环节', '二级产业链环节', 
                                '三级产业链环节','四级产业链环节',
                                '关联产品/商品/业务',
                                '数据来源类型（产业链）',
                                "数据来源标题（产业链）"],
                                )
            
            if "chain_structure_llm_merge" in result_json:
                chain_structure = result_json["chain_structure_llm_merge"]
                # print(chain_structure)
                chain_structure_excel = []
                index_num = 0
                for key,value in chain_structure.items():
                    index_num = index_num + 1 
                    nodes = key.split('|')
                    # print(nodes)
                    node_export = ["" for _ in range(6)]
                    for index,val in enumerate(nodes):
                        if index < 6:
                            node_export[index] = val
                        else:
                            break
                    # print(f"node_array:{node_export}")
                    product_array = [product_company["product_abb"] for product_company in value["product"]]
                    source_list = [source["title"] for source in value["source_list"]]
                    row = {
                        "序号":index_num,
                        "产业类型":node_export[0],
                        "上中下游":node_export[1],
                        "一级产业链环节":node_export[2],
                        "二级产业链环节":node_export[3],
                        "三级产业链环节":node_export[4],
                        "四级产业链环节":node_export[5],
                        "产品/商品/业务":" ".join(product_array),
                        "数据来源": " ".join(source_list)
                    }
                    chain_structure_excel.append(row)
                output_data = pd.DataFrame(chain_structure_excel)
                output_data.to_excel(f'{mongodb_id}_产业链_llm_merge.xlsx', index=False, 
                        columns=['序号', '产业类型', '上中下游', 
                                '一级产业链环节', '二级产业链环节', 
                                '三级产业链环节','四级产业链环节','产品/商品/业务',
                                '数据来源'])

            company_array = result_json["company"]
            company_excel = []
            index_num = 0
            for company_info in company_array:
                index_num = index_num + 1
                # print(company_info)
                source_list = [source["title"] for source in company_info["source_list"]]
                row = {
                    "序号":index_num,
                    "公司简称/品牌名称/文中名称":company_info["abb"],
                    "中文全称":company_info["name"],
                    "所属省":company_info["province"],
                    "所属市":company_info["city"],
                    "产业链末级环节":company_info["chain_end_position"],
                    "产业类型":company_info["chain_name"],
                    "数据来源": " ".join(source_list)
                }
                company_excel.append(row)
            if(len(company_excel) > 0):
                output_data = pd.DataFrame(company_excel)
                output_data.to_excel(f'{mongodb_id}_公司.xlsx', index=False,
                        columns=['序号', '公司简称/品牌名称/文中名称', '中文全称',
                                '所属省', '所属市',
                                '产业链末级环节','产业类型',
                                '数据来源'])
            product_excel = []
            index_num = 0
            product_array = result_json["product"]
            for product_item in product_array:
                index_num = index_num + 1
                # print(product_item)
                source_list = [source["title"] for source in product_item["source_list"]]
                # print(source_list)
                row = {
                    "序号":index_num,
                    "产品/商品/服务":product_item["product_abb"],
                    "公司简称/品牌名称/文中名称":product_item["company_abb"],
                    "中文全称":product_item["company_name"],
                    "数据来源": " ".join(source_list)
                }
                product_excel.append(row)
            if (len(product_excel)>0):
                output_data = pd.DataFrame(product_excel)
                output_data.to_excel(f'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/{mongodb_id}_产品.xlsx', index=False,
                        columns=['序号',"产品/商品/服务", '公司简称/品牌名称/文中名称', '中文全称',
                                '数据来源'])
    # 记录结束时间
    end_time = datetime.now()
    elapsed_time = (end_time - start_time).total_seconds()
    print(f"程序用时：{elapsed_time} 秒")

