import json
import re
import sys
from pathlib import Path
import uuid

from bs4 import BeautifulSoup,Comment
from bson import ObjectId
import requests


project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from service.news_data_processer import DataProcessor
from service.news_pageparse_service import WebPageParser
from utils.chorme_driver_utils import ChromeDriver
from utils.text_embed_util import TextEmbedService
from utils.milvus_util import MilvusUtil
from utils.llm_model_util import Llm_Service,MessageConverter,UserMessage
from configs.model_config import ModelConfig
from utils.reranker_util import QueryReranker
from configs.collection_config import CollectionConfig
from configs.news_crawl_config import NewsCrawlConfig
from datetime import datetime
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil


class LlmCrawl():
    def __init__(self):

        self.llm_service = Llm_Service()

    def news_crawl_url(self,page_source):
        for_llm= f"""
                你将扮演一个HTML解析器的角色。我将会提供一段HTML代码，主要是一些资讯文章的相关网页的HTML代码。

                请根据以下规则进行处理：
                1. 分析提供的HTML代码。
                2. 抽取出网页中的关键信息，包括：标题、发布时间、文章来源、文章正文的Xpath，附件对应的url
                3. 文章正文是指网页内具体文章所在的位置，尽量不包括广告或者添加qq、添加微信或者加群等信息，确保文章正文的XPath包含所有正文内容标签，例如使用该Xpath加`/*`就能获得所有具体正文内容所在的标签，然后再使用`@属性`或者`text()`来获取确切的文本内容，但是你所提供的XPath不用包含/*，只需要提供具体的XPath即可。
                4. 尽量使用具有特征性的属性如`id`或`class`来构造XPath，以确保XPath简洁且鲁棒。而且请确保你的Xpath是正确的，与标签能对应上。比如，如果文章正文在`<article id="content">`中，那么你的XPath应该是`//article[@id="content"]`,而不是`//div[@id="content"]`。
                5. 如果得出的Xpath结果是例如`//div[@id="content"]`，请重新确认id="content"是否在div标签中，如果不是，请改成正确的标签,请一定要进行这一步的判断。确认是正确的Xpath。
                6. 文章的来源可以是人名表示作者，也可以是机构名,优先以发布人为来源，如果没有发布人，可以以网页的机构作为来源。
                7. 发布时间格式为：2023-11-11 11:11:11，如果没有发布时间，则输出为空字符串。如果只有年月日，则输出为：2023-11-11 00:00:00,时间格式一定要完整
                8. 按照指定格式输出结果。
                9. 只需要返回JSON结构，不要解释，不要返回无关内容，返回的字符串内容可以直接被解析为JSON。

                **输出格式：**

                返回JSON结构格式如下：

                    "title": "文章标题",
                    "release_time":  "发布时间",
                    "source": "文章来源",
                    "content_xpath": "XPath to the content",
                    "附件": "url "
                
                现在，请接收以下HTML代码并开始分析：
                {page_source}

                """
        # print(for_llm)
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)
        return response
    
def clean_html(html_content):
    """
    清洗HTML代码，保留关键内容
    参数:
        html_content (str): 原始HTML字符串
    返回:
        str: 清洗后的HTML
    """
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 移除<script>标签
    for script in soup.find_all('script'):
        script.decompose()
        
    # 移除<style>标签
    for style in soup.find_all('style'):
        style.decompose()

    # 2. 移除所有标签的style属性
    for tag in soup.find_all(style=True):  # 找到所有带style属性的标签
        del tag['style']  # 删除style属性
        
    # 移除HTML注释
    for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
        comment.extract()
    # 转换为字符串并压缩
    compressed = str(soup)
    # 合并连续空白（包括换行）为单个空格
    compressed = re.sub(r'\s+', ' ', compressed)
    return compressed.strip()

def get_json(llm_response):
    pattern = r'\{(.*)\}'
    match = re.search(pattern, llm_response, re.DOTALL)
    
    if match:
        # 提取 JSON 字符串
        json_str = '{' + match.group(1) + '}'
        
        # 解析 JSON
        llm_data = json.loads(json_str)
    return llm_data

def get_llm_answer(url):
    """
    获取网页的HTML源代码

    首先经过清洗的网页结果
    然后交由大模型处理，获得结果，处理成json

    """
    headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
    }
    #3获取HTML并清洗
    response = requests.get(url,headers=headers)
    response.encoding='utf-8'
    soup = BeautifulSoup(response.text, 'html.parser')
    cleaned_html = clean_html(str(soup))

    ## 使用大模型处理HTML
    crawl = LlmCrawl()
    llm_response = crawl.news_crawl_url(cleaned_html)
    llm_data = get_json(llm_response)
    print(llm_data)

    ## 将处理的结果进行解析
    parser = WebPageParser(str(soup))
    file_name = parser.get_content_info(llm_data['content_xpath'], llm_data['title'])

    release_time= llm_data['release_time']
    if release_time != "":
                release_time = release_time.strip()
                release_time = datetime.strptime(release_time, '%Y-%m-%d %H:%M:%S')

    data = {
        "title": llm_data['title'],
        "release_time": release_time,
        "source": llm_data['source'],
        "pdf_file_name": file_name,
        "distinct": None,
        "url":url,
    }
    return data




if __name__ == '__main__':
    # llm_service = Llm_Service()

    # # 构造输入消息（格式参考OpenAI API）
    # # url = "https://xueqiu.com/4318019005/160292243?md5__1038=1e761e013c-Ui%2F%3DfIH%3DGIA%3DSfqIVqIFIaIEQ%3Dx%3D5%2B354%2BrzRiRzI9n%3DOT%3DSqT64fGE%3Dc3I4rr%2BI9jOSWI3%3DGgI4%3D5gIbwyOXIXdszpI5uIUIx%2B3IY%3Dj0I%3D%3DGRlpJds0I%2B%3D30Itq%3DxR5IsHSYY1B63OqO%3D5hMCI0IbdsoJp62McWs4jIixnUQdSHklOAOrIl4r5W4WSI%3DI"
    # url ='https://www.sohu.com/a/857529570_121124372'
    
    url_list = ['https://www.xhby.net/content/s6843d8a2e4b07e7bc9e2fa52.html','https://stcn.com/article/detail/1346580.html',
                'https://www.xhby.net/content/s681ef789e4b0044ce9b67683.html','https://www.cie.org.cn/list_42/13350.html']
    data_list = []
    for url in url_list:
        data = get_llm_answer(url)
        data_list.append(data)
    # print(data_list)
    process_data = []
    for data in data_list:
        process_data.append(DataProcessor.process_data(data))
    print(process_data)

    MongodbUtil.connect()
    MinIoUtil.connect()
   
    # mongodb入库
    MongodbUtil.insert_many(NewsCrawlConfig.MONGODB_COLLECTION,process_data)
    print("mongodb入库成功")

    ## pdf存入minio
    for data in process_data:
        remote_path = data["minio"]["minio_document_path"]
        local_path = 'file/news_pdf/' + data["file"]["file_title"]+'.pdf'
        MinIoUtil.upload_file(NewsCrawlConfig.BUCKET_NAME,remote_path,local_path)
    print("minio上传成功")



    
        
    

    
   
    # mongodb入库
    # MongodbUtil.insert_many("research_report_label_info",data_list)
    # print("mongodb入库成功")
    
    # for remote_path in remote_path_list:
    #     last_part = remote_path.split('/')[-1]
    #     local_path = 'file/policy_pdf/' + last_part
    #     print(local_path)
 

    ## pdf存入minio
    # for data in data_list:
    #     remote_path_list = data["minio"]["minio_document_path"]
    #     for remote_path in remote_path_list:
    #         last_part = remote_path.split('/')[-1]
    #         local_path = 'file/policy_pdf/' + last_part
        
        
    #         MinIoUtil.upload_file(NewsCrawlConfig.BUCKET_NAME,remote_path,local_path)
    # print("minio上传成功")


   
    