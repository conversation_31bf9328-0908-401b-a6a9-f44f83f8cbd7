#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:40
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : news_crawl.py
# @Description  : 资讯类数据爬取脚本文件
"""

import sys
from pathlib import Path
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from utils.minio_util import MinIoUtil
from utils.mongodb_util import MongodbUtil


from configs.mongodb_config import MongodbConfig
from configs.minio_config import MinioConfig
from service.news_data_processer import DataProcessor
from configs.news_crawl_config import NewsCrawlConfig
from service.news_crawl_service import NewsCrawler


if __name__ == '__main__':
    # baijiahao_urls = ['https://baijiahao.baidu.com/s?id=1791683653520481221&wfr=spider&for=pc','https://baijiahao.baidu.com/s?id=1705157066092073854&wfr=spider&for=pc','https://baijiahao.baidu.com/s?id=1809853789674566085&wfr=spider&for=pc']
    sohu_urls = ['https://www.sohu.com/a/857529570_121124372']
    # xueqiu_urls = ['https://xueqiu.com/4318019005/160292243?md5__1038=1e761e013c-Ui%2F%3DfIH%3DGIA%3DSfqIVqIFIaIEQ%3Dx%3D5%2B354%2BrzRiRzI9n%3DOT%3DSqT64fGE%3Dc3I4rr%2BI9jOSWI3%3DGgI4%3D5gIbwyOXIXdszpI5uIUIx%2B3IY%3Dj0I%3D%3DGRlpJds0I%2B%3D30Itq%3DxR5IsHSYY1B63OqO%3D5hMCI0IbdsoJp62McWs4jIixnUQdSHklOAOrIl4r5W4WSI%3DI']
    
    # urls = ['https://baijiahao.baidu.com/s?id=1809853789674566085&wfr=spider&for=pc']
    chinadaily_tx_urls = ['https://column.chinadaily.com.cn/a/202506/03/WS683e8c7ea3102053770363eb.html']
    zhiyan_opinion_urls = ['https://www.chyxx.com/industry/1211722.html']
    chinabgao_urls = ['https://www.chinabgao.com/freereport/103042.html']

    yiou_urls = ['https://www.iyiou.com/news/202502121089998']
    ## 初始化爬虫配置
    # config_kr36 = NewsCrawlConfig.KR36
    # config_baijiahao = NewsCrawlConfig.BAIJIAHAO
 
    # config_xueqiu = NewsCrawlConfig.XUEQIU
    # crawler_kr36 = NewsCrawler(config_kr36)
    # crawler_baijiahao = NewsCrawler(config_baijiahao)
    
    # crawler_xueqiu = NewsCrawler(config_xueqiu)

    # config_chinadaily_tx = NewsCrawlConfig.ChinaDaily_tx
    # crawler_chinadaily_tx = NewsCrawler(config_chinadaily_tx)

    config_zhiyan_opinion = NewsCrawlConfig.ZHIYAN_opinion
    crawler_zhiyan_opinion = NewsCrawler(config_zhiyan_opinion)

    # config_chinabgao = NewsCrawlConfig.Chinabgao
    # crawler_chinabgao = NewsCrawler(config_chinabgao)

    # config_sohu = NewsCrawlConfig.SOHU
    # crawler_sohu = NewsCrawler(config_sohu)

    # config_yiou = NewsCrawlConfig.Yiou
    # crawler_yiou = NewsCrawler(config_yiou)
    
    ## 调用爬虫函数，获取返回数据
    data_list = []
    # data_list += crawler_baijiahao.get_news_pdf(baijiahao_urls)

    # data_list += crawler_xueqiu.get_news_pdf(xueqiu_urls)

    # data_list += crawler_chinadaily_tx.get_news_pdf(chinadaily_tx_urls)
    data_list += crawler_zhiyan_opinion.get_news_pdf(zhiyan_opinion_urls)
    # data_list += crawler_chinabgao.get_news_pdf(chinabgao_urls)
    # data_list += crawler_sohu.get_news_pdf(sohu_urls)
    # data_list += crawler_yiou.get_news_pdf(yiou_urls)


    process_data = []
    for data in data_list:
        process_data.append(DataProcessor.process_data(data))
    print(process_data)

    # MongodbUtil.connect()
    # MinIoUtil.connect()

    ## mongodb入库
    # MongodbUtil.insert_many(MongodbConfig.MONGODB_COLLECTION,process_data)
    # print("mongodb入库成功")


    # ## pdf存入minio
    # for data in data_list:
    #     remote_path = data["minio"]["minio_document_path"]
    #     local_path = 'pdf/' + data["file"]["file_title"]+'.pdf'
    #     MinIoUtil.upload_file(MinioConfig.BUCKET_NAME,remote_path,local_path)
    # print("minio上传成功")