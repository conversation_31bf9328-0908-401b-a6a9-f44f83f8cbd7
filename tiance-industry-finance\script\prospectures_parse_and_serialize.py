﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/01/16 11:42:55
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   annual_parse_and_serialize.py
@Project    :   tiance-industry-finance
'''
from configs.collection_config import CollectionConfig
from service.llm_extract_name_service import LlmHelper
from utils.milvus_util import MilvusUtil
import re
import requests


if __name__ == '__main__':

    url = "http://10.8.21.163:8866/company_relation_with_file"
    
    llm_helper = LlmHelper()
    
    milvus_util = MilvusUtil()
    collection_name = ''
    docs = milvus_util.query_all_doc(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS)
    file_names = [doc["file_title"] for doc in docs]
    file_names = list(set(file_names))
    print(file_names)
    total_files = len(file_names)
    print("total files: ", total_files)
    # pattern = r"(\d{4})年"
    process_file = 0
    # exclude_names = ["比亚迪-首次公开发行A股股票招股说明书","宁德时代-首次公开发行股票并在创业板上市招股说明书","卧龙电驱-卧龙科技招股说明书","小康股份-首次公开发行股票招股说明书"]
    exclude_names = []
    for file_name in file_names:
        if file_name in exclude_names:
            print("has process")
            process_file = process_file+1
            continue
        process_file = process_file+1
        print(process_file, "/", total_files)
        # years = re.findall(pattern, file_name)
        if '-' in file_name:
            company_name = file_name.split("-")[0].replace("st","").replace("ST","").replace("*","")
        else :
            company_name = llm_helper.extract_name(pdf_filename=file_name)
        # if company_name != "南钢股份":
        #     continue
        print(company_name)
        # print(years)

        response = requests.post(url, json={"pdf_name": company_name,"pdf_filename":file_name,"industry_chain":"新材料"})
        print(response.json())
        
        # break
    # response = requests.post(url, json={"company_name": company_name,"pdf_filename":file_name,"year":years[0]})

    # response = requests.post(url, json={"pdf_name": "多氟多","pdf_filename":"多氟多-首次公开发行股票招股说明书"})
    # print(response.json())

    # response = requests.post(url, json={"pdf_name": "比亚迪","pdf_filename":"比亚迪-首次公开发行A股股票招股说明书"})
    # print(response.json())

    # response = requests.post(url, json={"pdf_name": "宁德时代","pdf_filename":"宁德时代-首次公开发行股票并在创业板上市招股说明书"})
    # print(response.json())

    # response = requests.post(url, json={"pdf_name": "卧龙电驱","pdf_filename":"卧龙电驱-卧龙科技招股说明书"})
    # print(response.json())
    # response = requests.post(url, json={"pdf_name": "小康股份","pdf_filename":"小康股份-首次公开发行股票招股说明书"})
    # print(response.json())

    # response = requests.post(url, json={"pdf_name": "新晨科技","pdf_filename":"新晨科技-首次公开发行股票并在创业板上市招股说明书","industry_chain":"低空经济"})
    # print(response.json())

    # response = requests.post(url, json={"pdf_name": "航天南湖","pdf_filename":"航天南湖首次公开发行股票并在科创板上市招股说明书","industry_chain":"低空经济"})
    # print(response.json())
    

    # print(docs
    # print(docs[0][0])
    # for doc in docs:
    #     print(doc)
    # print(len(docs))