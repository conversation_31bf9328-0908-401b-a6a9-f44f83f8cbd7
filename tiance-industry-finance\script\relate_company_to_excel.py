#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/03/11 11:09:34
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   relate_company_to_excel.py
@Project    :   tiance-industry-finance
'''
import requests
import pandas as pd
from service.filter_company_rules_service import company_output_rules_filter_processor,remove_unknown_symbol
from utils.mongodb_util import MongodbUtil
from script.hundson_api import HundsonApi

import datetime
def unix_time_to_utf(timestamp_ms = 1740794426035):
    
    # 将毫秒转换为秒
    timestamp_s = timestamp_ms / 1000.0

    # 转换为UTC时间
    utc_time = datetime.datetime.utcfromtimestamp(timestamp_s)

    # 转换为本地时间（如果你需要本地时间）
    local_time = datetime.datetime.fromtimestamp(timestamp_s)

    # print(f"UTC时间: {utc_time}")
    # print(f"本地时间: {local_time}")
    return local_time


if __name__ == '__main__':
    hundson_api = HundsonApi()
    url = "http://***********:9029/related_companies"
    industry = "新材料"
    MongodbUtil.connect()
    # docs = MongodbUtil.query_docs_by_condition(collection_name="key_companies_test",search_condition={'industry':{'$regex':"工业机器人"}})
   
    response = requests.post(url, json={"industry":industry })
    print(response.json())

    company_list = response.json()["data"]["company_list"]
    processed_data = []
    filter_data = []
    for company in company_list:
        company["name"] = remove_unknown_symbol(company["name"])
        company["abb"] = remove_unknown_symbol(company["abb"])
        company["key_company"] = remove_unknown_symbol(company["key_company"])
        if(company_output_rules_filter_processor(company["abb"])):
            continue
        filter_condition = {"title" : company["source_list"][0]["source_title"]}
        # print(filter_condition)
        source = MongodbUtil.query_docs_by_condition(collection_name="source_notice",search_condition=filter_condition)
        source = list(source)
        time = ""
        if(len(source)>0):
            # print("test")
            time = source[0]["release_time"]
            time = unix_time_to_utf(time)
        
        hundson_result = hundson_api.checkIsExist(company["name"],is_cache=True)
        print(hundson_result)
        if hundson_result["is_match"] == False and hundson_result["is_company_before_name"] == False:
            filter_row = {"企业简称":company["abb"],
            "企业名称":company["name"] if hundson_result["is_company_before_name"] == False else hundson_result["name_list"],
            "上下游标识":company["stream_type"],
            "关系扩展":company["related_type"],
            "被关联企业":company["key_company"],
            "更新时间":company["update_time"],
            "来源标题":company["source_list"][0]["source_title"],
            "来源链接":company["source_list"][0]["source_url"],
            "发布时间":time,
            "是否上市" : company["is_listed"],
            "产业地位" : company["industry_position"],
            "股票代码":company["stock_code"],
            "所属省":company["province"] if company["province"] != '-' else hundson_result['state'],
            "所属市":company["city"] if company["province"] != '-' else hundson_result['area_name'],
            "恒生校验一致":"正确" if hundson_result["is_match"] else "错误",
            "恒生校验是否为曾用名":"正确" if hundson_result["is_company_before_name"] else "错误",
            "恒生搜索结果" : hundson_result["name_list"],
            "此环节被替换的名字":company["name"] if hundson_result["is_company_before_name"] == True else ""
            }
            filter_data.append(filter_row)
            continue

        row = {"企业简称":company["abb"],
        "企业名称":company["name"] if hundson_result["is_company_before_name"] == False else hundson_result["name_list"],
        "上下游标识":company["stream_type"],
        "关系扩展":company["related_type"],
        "被关联企业":company["key_company"],
        "更新时间":company["update_time"],
        "来源标题":company["source_list"][0]["source_title"],
        "来源链接":company["source_list"][0]["source_url"],
        "发布时间":time,
        "是否上市" : company["is_listed"],
        "产业地位" : company["industry_position"],
        "股票代码":company["stock_code"],
        "所属省":company["province"],
        "所属市":company["city"],
        "恒生校验一致":"正确" if hundson_result["is_match"] else "错误",
        "恒生校验是否为曾用名":"正确" if hundson_result["is_company_before_name"] else "错误",
        "恒生搜索结果" : hundson_result["name_list"],
        "此环节被替换的名字":company["name"] if hundson_result["is_company_before_name"] == True else ""
        }
        processed_data.append(row)
    print(processed_data)
    output_data = pd.DataFrame(processed_data)
    output_data.to_excel(f'{industry}_关联企业_5.xlsx', index=False, 
                    columns=['企业简称', '企业名称', '上下游标识', 
                            '关系扩展', '被关联企业', 
                            '更新时间','来源标题',
                            '来源链接',"发布时间",
                            "恒生校验一致","恒生校验是否为曾用名","恒生搜索结果",
                            '股票代码','所属省','所属市','是否上市','产业地位',
                            "此环节被替换的名字"])
    filter_data_out = pd.DataFrame(filter_data)
    filter_data_out.to_excel(f'{industry}_被过滤关联企业_5.xlsx', index=False, 
                    columns=['企业简称', '企业名称', '上下游标识', 
                            '关系扩展', '被关联企业', 
                            '更新时间','来源标题',
                            '来源链接',"发布时间",
                            "恒生校验一致","恒生校验是否为曾用名","恒生搜索结果",
                            '股票代码','所属省','所属市','是否上市','产业地位',
                            "此环节被替换的名字"])