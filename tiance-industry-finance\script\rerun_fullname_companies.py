#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :rerun_fullname_companies.py
@Description  :
<AUTHOR>
@Date         :2025/03/13 16:31:12
'''

import asyncio
from entity.request_entity import GetFullnameCompaniesRequest
from utils.log_util import LogUtil
from api.routes.get_fullname_companies import get_fullname_companies
from configs.collection_config import CollectionConfig
from utils.mongodb_util import MongodbUtil
MongodbUtil.connect()
LogUtil.init("test")
industry = "新材料"
node_info = MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).find_one({"industry": industry})
node_companies = node_info.get("node_companies_abb")
mongdb_id_companies = node_info.get("mongdb_id_companies")

key_info = asyncio.run(get_fullname_companies(GetFullnameCompaniesRequest(
    industry=industry,
    node_companies=node_companies
)))

if key_info.code == 200:
    new_node_companies = key_info.data.get("result") # 有产业链结构的中心客群
    key_companies = key_info.data.get("all_key_companies") # 无产业链结构的中心客群列表
    filter_companies = key_info.data.get("filter_companies") # 被过滤的中心客群
    save_data = {"industry": industry, "node_companies": new_node_companies,
                    "key_companies": key_companies, "filter_companies": filter_companies, "mongdb_id_companies": mongdb_id_companies}
    MongodbUtil.coll(CollectionConfig.KEY_COMPANIES).update_one({"industry": industry},{"$set": save_data})