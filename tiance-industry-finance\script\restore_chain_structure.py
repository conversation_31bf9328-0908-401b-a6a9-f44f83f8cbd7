#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :restore_chain_structure.py
@Description  :
<AUTHOR>
@Date         :2025/03/14 16:08:42
'''

from utils.mongodb_util import MongodbUtil
from utils.tree_utils import TreeUtils
MongodbUtil.connect()
industry = "新能源汽车"
result = MongodbUtil.coll("key_companies").find_one({"industry": industry})
nodes = TreeUtils.restore_tree(list(result.get("node_companies").keys()))
MongodbUtil.coll("chain_structure").update_one({"industry": industry}, {"$set": {"chain_structure": nodes}})