#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 11:30:00
# <AUTHOR> Assistant
# @File         : test_api_company_update.py
# @Description  : 测试公司数据修改API接口
"""

import requests
import json
import time


def test_company_update_api():
    """测试公司数据修改API接口"""
    
    # 配置服务器地址
    base_url = "http://localhost:9029"
    
    print("=" * 60)
    print("开始测试公司数据修改API接口")
    print("=" * 60)
    
    # 测试用的企业编号（请根据实际数据修改）
    test_company_code = "COMP001"  # 请替换为实际存在的企业编号
    
    # 测试1: 获取公司信息
    print("\n1. 测试获取公司信息 GET /company_info/{company_code}")
    print("-" * 50)
    try:
        url = f"{base_url}/company_info/{test_company_code}"
        response = requests.get(url, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 保存原始信息用于后续测试
            if result.get("code") == 200:
                original_info = result.get("data", {})
            else:
                print("获取公司信息失败，跳过后续测试")
                return
        else:
            print(f"请求失败: {response.text}")
            return
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
        return
    
    time.sleep(1)
    
    # 测试2: 检查公司名称重复
    print("\n2. 测试检查公司名称重复 POST /company_name_check")
    print("-" * 50)
    try:
        url = f"{base_url}/company_name_check"
        
        # 测试原名称（应该重复）
        original_name = original_info.get('chi_name', '')
        if original_name:
            params = {
                "chi_name": original_name,
                "exclude_company_code": test_company_code
            }
            response = requests.post(url, params=params, timeout=30)
            
            print(f"请求URL: {url}")
            print(f"请求参数: {params}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print("响应内容:")
                print(json.dumps(result, indent=2, ensure_ascii=False))
        
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    time.sleep(1)
    
    # 测试3: 修改公司信息
    print("\n3. 测试修改公司信息 POST /company_update")
    print("-" * 50)
    try:
        url = f"{base_url}/company_update"
        
        # 准备测试数据
        test_data = {
            "company_code": test_company_code,
            "chi_name": f"测试修改名称_{int(time.time())}",
            "chi_name_abbr": "测试简称",
            "eng_name": "Test Updated Company Name"
        }
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 如果修改成功，尝试恢复原始数据
            if result.get("code") == 200:
                print("\n尝试恢复原始数据...")
                restore_data = {
                    "company_code": test_company_code,
                    "chi_name": original_info.get('chi_name', ''),
                    "chi_name_abbr": original_info.get('chi_name_abbr'),
                    "eng_name": original_info.get('eng_name')
                }
                
                restore_response = requests.post(url, json=restore_data, timeout=30)
                if restore_response.status_code == 200:
                    restore_result = restore_response.json()
                    print("恢复结果:")
                    print(json.dumps(restore_result, indent=2, ensure_ascii=False))
                else:
                    print(f"恢复失败: {restore_response.text}")
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("API接口测试完成")
    print("=" * 60)


def test_error_cases():
    """测试错误情况"""
    base_url = "http://localhost:9029"
    
    print("\n" + "=" * 60)
    print("测试错误情况")
    print("=" * 60)
    
    # 1. 测试空企业编号
    print("\n1. 测试空企业编号...")
    try:
        url = f"{base_url}/company_update"
        test_data = {
            "company_code": "",
            "chi_name": "测试名称"
        }
        response = requests.post(url, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"异常: {str(e)}")
    
    # 2. 测试空中文名称
    print("\n2. 测试空中文名称...")
    try:
        url = f"{base_url}/company_update"
        test_data = {
            "company_code": "COMP001",
            "chi_name": ""
        }
        response = requests.post(url, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"异常: {str(e)}")
    
    # 3. 测试不存在的企业编号
    print("\n3. 测试不存在的企业编号...")
    try:
        url = f"{base_url}/company_update"
        test_data = {
            "company_code": "NONEXISTENT",
            "chi_name": "测试名称"
        }
        response = requests.post(url, json=test_data, timeout=30)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(json.dumps(result, indent=2, ensure_ascii=False))
    except Exception as e:
        print(f"异常: {str(e)}")


def generate_curl_commands():
    """生成curl测试命令"""
    base_url = "http://localhost:9029"
    
    print("\n" + "=" * 60)
    print("curl 测试命令")
    print("=" * 60)
    
    print("\n1. 获取公司信息")
    print(f'curl -X GET "{base_url}/company_info/COMP001"')
    
    print("\n2. 检查公司名称重复")
    print(f'curl -X POST "{base_url}/company_name_check?chi_name=测试公司&exclude_company_code=COMP001"')
    
    print("\n3. 修改公司信息")
    print(f'curl -X POST "{base_url}/company_update" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{"company_code": "COMP001", "chi_name": "新公司名称", "chi_name_abbr": "新简称", "eng_name": "New Company Name"}\'')


def check_server_status():
    """检查服务器状态"""
    base_url = "http://localhost:9029"
    
    print("检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到服务器: {str(e)}")
        print("请确保服务器已启动并运行在正确的端口上")
        return False


if __name__ == "__main__":
    print("公司数据修改API测试工具")
    print("请确保服务器已启动")
    
    # 检查服务器状态
    if check_server_status():
        # 测试API端点
        print("\n注意：以下测试需要实际的数据库数据")
        print("请修改 test_company_code 为实际存在的企业编号")
        
        # test_company_update_api()  # 取消注释以运行实际测试
        # test_error_cases()         # 取消注释以测试错误情况
        
        print("\n如需运行实际测试，请取消注释相应的函数调用")
    else:
        print("\n服务器未运行，显示curl测试命令供参考：")
        generate_curl_commands()
        
        print("\n启动服务器的命令:")
        print("python main.py")
