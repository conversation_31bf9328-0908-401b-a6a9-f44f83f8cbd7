#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 13:00:00
# <AUTHOR> Assistant
# @File         : test_api_integration.py
# @Description  : 测试API集成是否正确
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试导入是否正确"""
    print("测试导入...")
    
    try:
        # 测试实体类导入
        from service_data_manage.entity.data_manage_entity import (
            CompanyDataStatsRequest, 
            CompanyUpdateRequest, 
            CompanyBatchUpdateRequest
        )
        print("✓ 实体类导入成功")
        
        # 测试服务层导入
        from service_data_manage.service.data_manage_service import (
            CompanyDataStatsService, 
            CompanyUpdateService
        )
        print("✓ 服务层导入成功")
        
        # 测试API路由导入
        from service_data_manage.api.routes import data_manage_route
        print("✓ API路由导入成功")
        
        # 测试API模块导入
        from service_data_manage.api import data_manage
        print("✓ API模块导入成功")
        
        # 测试主应用导入
        from main import app
        print("✓ 主应用导入成功")
        
        return True
        
    except ImportError as e:
        print(f"✗ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {str(e)}")
        return False


def test_api_routes():
    """测试API路由是否正确注册"""
    print("\n测试API路由...")
    
    try:
        from main import app
        
        # 获取所有路由
        routes = []
        for route in app.routes:
            if hasattr(route, 'path'):
                routes.append(f"{route.methods} {route.path}")
        
        # 检查数据管理相关路由
        data_manage_routes = [route for route in routes if '/data_manage' in route]
        
        if data_manage_routes:
            print("✓ 数据管理路由已注册:")
            for route in data_manage_routes:
                print(f"  - {route}")
            return True
        else:
            print("✗ 未找到数据管理路由")
            return False
            
    except Exception as e:
        print(f"✗ 路由测试失败: {str(e)}")
        return False


def test_service_methods():
    """测试服务层方法是否可用"""
    print("\n测试服务层方法...")
    
    try:
        from service_data_manage.service.data_manage_service import (
            CompanyDataStatsService, 
            CompanyUpdateService
        )
        
        # 检查方法是否存在
        stats_methods = [
            'get_company_data_statistics'
        ]
        
        update_methods = [
            'update_company_info',
            'get_company_info', 
            'check_chi_name_duplicate',
            'batch_update_companies'
        ]
        
        # 检查统计服务方法
        for method in stats_methods:
            if hasattr(CompanyDataStatsService, method):
                print(f"✓ CompanyDataStatsService.{method} 存在")
            else:
                print(f"✗ CompanyDataStatsService.{method} 不存在")
                return False
        
        # 检查更新服务方法
        for method in update_methods:
            if hasattr(CompanyUpdateService, method):
                print(f"✓ CompanyUpdateService.{method} 存在")
            else:
                print(f"✗ CompanyUpdateService.{method} 不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 服务层测试失败: {str(e)}")
        return False


def test_entity_classes():
    """测试实体类是否正确定义"""
    print("\n测试实体类...")
    
    try:
        from service_data_manage.entity.data_manage_entity import (
            CompanyDataStatsRequest, 
            CompanyUpdateRequest, 
            CompanyBatchUpdateRequest
        )
        
        # 测试实体类实例化
        stats_request = CompanyDataStatsRequest()
        print("✓ CompanyDataStatsRequest 实例化成功")
        
        update_request = CompanyUpdateRequest(
            company_code="TEST001",
            chi_name="测试公司"
        )
        print("✓ CompanyUpdateRequest 实例化成功")
        
        batch_request = CompanyBatchUpdateRequest(
            companies=[update_request]
        )
        print("✓ CompanyBatchUpdateRequest 实例化成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 实体类测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("=" * 60)
    print("API集成测试")
    print("=" * 60)
    
    tests = [
        ("导入测试", test_imports),
        ("API路由测试", test_api_routes),
        ("服务层方法测试", test_service_methods),
        ("实体类测试", test_entity_classes)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！API已成功迁移到 service_data_manage 模块")
        print("\n可用的API端点:")
        print("- POST /data_manage/company_data_stats")
        print("- GET  /data_manage/company_data_stats_simple") 
        print("- POST /data_manage/company_update")
        print("- GET  /data_manage/company_info/{company_code}")
        print("- POST /data_manage/company_name_check")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
