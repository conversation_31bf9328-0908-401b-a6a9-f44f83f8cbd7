#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 13:30:00
# <AUTHOR> Assistant
# @File         : test_batch_update_reuse.py
# @Description  : 测试批量更新是否正确复用 update_company_info 接口
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_data_manage.service.data_manage_service import CompanyUpdateService
from utils.log_util import LogUtil


def test_batch_update_reuse():
    """测试批量更新复用单个更新接口"""
    try:
        print("=" * 60)
        print("测试批量更新复用 update_company_info 接口")
        print("=" * 60)
        
        # 初始化日志
        LogUtil.init(process_name="test_batch_update_reuse")
        
        # 测试数据（请根据实际数据修改）
        test_companies_data = [
            {
                "company_code": "COMP001",
                "chi_name": "测试复用A",
                "chi_name_abbr": "复用A",
                "eng_name": "Test Reuse A"
            },
            {
                "company_code": "COMP002", 
                "chi_name": "测试复用B",
                "chi_name_abbr": "复用B",
                "eng_name": "Test Reuse B"
            }
        ]
        
        print(f"测试数据:")
        for i, company in enumerate(test_companies_data):
            print(f"  {i+1}. {company}")
        
        # 1. 测试单个更新接口
        print("\n1. 测试单个更新接口...")
        print("-" * 40)
        
        try:
            single_result = CompanyUpdateService.update_company_info(
                company_code=test_companies_data[0]['company_code'],
                chi_name=test_companies_data[0]['chi_name'] + "_单个",
                chi_name_abbr=test_companies_data[0]['chi_name_abbr'],
                eng_name=test_companies_data[0]['eng_name']
            )
            print("✓ 单个更新接口调用成功")
            print(f"  - 企业编号: {single_result.get('company_code')}")
            print(f"  - 原名称: {single_result.get('old_chi_name')}")
            print(f"  - 新名称: {single_result.get('new_chi_name')}")
            print(f"  - 更新时间: {single_result.get('update_time')}")
        except Exception as e:
            print(f"✗ 单个更新接口调用失败: {str(e)}")
            return False
        
        # 2. 测试批量更新接口（应该复用单个更新接口）
        print("\n2. 测试批量更新接口...")
        print("-" * 40)
        
        # 修改测试数据以避免重复
        batch_test_data = [
            {
                "company_code": "COMP001",
                "chi_name": "测试复用A_批量",
                "chi_name_abbr": "复用A批量",
                "eng_name": "Test Reuse A Batch"
            },
            {
                "company_code": "COMP002", 
                "chi_name": "测试复用B_批量",
                "chi_name_abbr": "复用B批量",
                "eng_name": "Test Reuse B Batch"
            }
        ]
        
        try:
            batch_result = CompanyUpdateService.batch_update_companies(batch_test_data)
            
            if batch_result.get('success'):
                print("✓ 批量更新接口调用成功")
                print(f"  - 总数量: {batch_result.get('total_count')}")
                print(f"  - 更新数量: {batch_result.get('updated_count')}")
                print(f"  - 消息: {batch_result.get('message')}")
                
                # 显示更新详情
                updated_companies = batch_result.get('updated_companies', [])
                for company in updated_companies:
                    print(f"  - 索引{company['index']}: {company['company_code']} -> {company['new_chi_name']}")
                
                return True
            else:
                print("✗ 批量更新失败")
                print(f"  - 错误索引: {batch_result.get('error_index')}")
                print(f"  - 错误企业编号: {batch_result.get('error_company_code')}")
                print(f"  - 错误消息: {batch_result.get('error_message')}")
                print(f"  - 错误类型: {batch_result.get('error_type')}")
                return False
                
        except Exception as e:
            print(f"✗ 批量更新接口调用失败: {str(e)}")
            return False
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_error_cases_reuse():
    """测试错误情况是否正确复用验证逻辑"""
    print("\n" + "=" * 60)
    print("测试错误情况复用验证逻辑")
    print("=" * 60)
    
    # 初始化日志
    LogUtil.init(process_name="test_batch_update_reuse_errors")
    
    # 1. 测试空数据验证
    print("\n1. 测试空数据验证...")
    try:
        result = CompanyUpdateService.batch_update_companies([
            {"company_code": "", "chi_name": "测试"},  # 空企业编号
        ])
        
        if not result.get('success') and result.get('error_type') == 'validation_error':
            print("✓ 空企业编号验证正确")
            print(f"  - 错误消息: {result.get('error_message')}")
        else:
            print("✗ 空企业编号验证失败")
            return False
    except Exception as e:
        print(f"✗ 空数据验证异常: {str(e)}")
        return False
    
    # 2. 测试不存在的企业编号
    print("\n2. 测试不存在的企业编号...")
    try:
        result = CompanyUpdateService.batch_update_companies([
            {"company_code": "NONEXISTENT", "chi_name": "测试"},  # 不存在的企业编号
        ])
        
        if not result.get('success') and result.get('error_type') == 'validation_error':
            print("✓ 不存在企业编号验证正确")
            print(f"  - 错误消息: {result.get('error_message')}")
        else:
            print("✗ 不存在企业编号验证失败")
            print(f"  - 结果: {result}")
            return False
    except Exception as e:
        print(f"✗ 不存在企业编号验证异常: {str(e)}")
        return False
    
    # 3. 测试重复名称验证
    print("\n3. 测试重复名称验证...")
    try:
        result = CompanyUpdateService.batch_update_companies([
            {"company_code": "COMP001", "chi_name": "重复名称测试"},
            {"company_code": "COMP002", "chi_name": "重复名称测试"},  # 批量内部重复
        ])
        
        if not result.get('success') and result.get('error_type') == 'internal_duplicate_error':
            print("✓ 重复名称验证正确")
            print(f"  - 错误消息: {result.get('error_message')}")
        else:
            print("✗ 重复名称验证失败")
            print(f"  - 结果: {result}")
            return False
    except Exception as e:
        print(f"✗ 重复名称验证异常: {str(e)}")
        return False
    
    return True


def test_interface_consistency():
    """测试接口一致性"""
    print("\n" + "=" * 60)
    print("测试接口一致性")
    print("=" * 60)
    
    print("\n检查批量更新是否正确调用单个更新接口...")
    
    # 检查方法签名
    import inspect
    
    # 获取 update_company_info 方法签名
    update_signature = inspect.signature(CompanyUpdateService.update_company_info)
    print(f"✓ update_company_info 方法签名: {update_signature}")
    
    # 获取 batch_update_companies 方法签名
    batch_signature = inspect.signature(CompanyUpdateService.batch_update_companies)
    print(f"✓ batch_update_companies 方法签名: {batch_signature}")
    
    # 检查方法是否存在
    if hasattr(CompanyUpdateService, 'update_company_info'):
        print("✓ update_company_info 方法存在")
    else:
        print("✗ update_company_info 方法不存在")
        return False
    
    if hasattr(CompanyUpdateService, 'batch_update_companies'):
        print("✓ batch_update_companies 方法存在")
    else:
        print("✗ batch_update_companies 方法不存在")
        return False
    
    return True


def main():
    """主测试函数"""
    print("批量更新复用接口测试")
    print("注意：以下测试需要实际的数据库数据")
    print("请修改测试数据中的企业编号为实际存在的值")
    
    tests = [
        ("接口一致性测试", test_interface_consistency),
        ("错误情况复用测试", test_error_cases_reuse),
        # ("批量更新复用测试", test_batch_update_reuse),  # 需要实际数据
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 批量更新已正确复用 update_company_info 接口！")
        print("\n复用特性:")
        print("- ✓ 批量更新调用单个更新接口进行实际更新")
        print("- ✓ 复用现有的验证逻辑（公司存在性、名称重复性）")
        print("- ✓ 保持错误处理的一致性")
        print("- ✓ 减少代码重复，提高维护性")
    else:
        print("❌ 部分测试失败，请检查实现")
    
    print("\n如需测试实际更新功能，请:")
    print("1. 修改测试数据中的企业编号为实际存在的值")
    print("2. 取消注释 test_batch_update_reuse 测试")
    print("3. 重新运行测试")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
