#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:00:00
# <AUTHOR> Assistant
# @File         : test_company_batch_update.py
# @Description  : 测试公司数据批量修改功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service.company_update_service import CompanyUpdateService
from utils.log_util import LogUtil


def test_batch_update_service():
    """测试批量更新服务功能"""
    try:
        print("=" * 60)
        print("开始测试公司数据批量修改服务功能")
        print("=" * 60)
        
        # 初始化日志
        LogUtil.init(process_name="test_company_batch_update")
        
        # 测试数据（请根据实际数据修改）
        test_companies_data = [
            {
                "company_code": "COMP001",
                "chi_name": "测试公司A_批量",
                "chi_name_abbr": "测试A",
                "eng_name": "Test Company A"
            },
            {
                "company_code": "COMP002", 
                "chi_name": "测试公司B_批量",
                "chi_name_abbr": "测试B",
                "eng_name": "Test Company B"
            }
        ]
        
        print(f"测试数据:")
        for i, company in enumerate(test_companies_data):
            print(f"  {i+1}. {company}")
        
        # 1. 测试只检查模式
        print("\n1. 测试只检查模式...")
        print("-" * 40)
        
        check_result = CompanyUpdateService.batch_update_companies(
            companies_data=test_companies_data,
            check_only=True
        )
        
        print(f"检查结果:")
        for key, value in check_result.items():
            if key == "errors" and value:
                print(f"  - {key}:")
                for error in value:
                    print(f"    * {error}")
            else:
                print(f"  - {key}: {value}")
        
        # 2. 如果检查通过，测试实际更新
        if check_result.get("error_count", 0) == 0:
            print("\n2. 测试实际批量更新...")
            print("-" * 40)
            
            update_result = CompanyUpdateService.batch_update_companies(
                companies_data=test_companies_data,
                check_only=False
            )
            
            print(f"更新结果:")
            for key, value in update_result.items():
                if key == "errors" and value:
                    print(f"  - {key}:")
                    for error in value:
                        print(f"    * {error}")
                elif key == "updated_companies" and value:
                    print(f"  - {key}:")
                    for company in value:
                        print(f"    * 索引{company['index']}: {company['company_code']}")
                else:
                    print(f"  - {key}: {value}")
        else:
            print("\n检查发现错误，跳过实际更新测试")
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_error_cases():
    """测试错误情况"""
    print("\n" + "=" * 60)
    print("测试错误情况")
    print("=" * 60)
    
    # 初始化日志
    LogUtil.init(process_name="test_company_batch_update_errors")
    
    # 1. 测试空数据
    print("\n1. 测试空数据...")
    try:
        result = CompanyUpdateService.batch_update_companies([], check_only=True)
        print(f"空数据测试结果: {result}")
    except Exception as e:
        print(f"空数据测试异常: {str(e)}")
    
    # 2. 测试无效数据
    print("\n2. 测试无效数据...")
    invalid_data = [
        {"company_code": "", "chi_name": "测试"},  # 空企业编号
        {"company_code": "COMP001", "chi_name": ""},  # 空中文名称
        {"company_code": "NONEXISTENT", "chi_name": "测试"},  # 不存在的企业编号
    ]
    
    try:
        result = CompanyUpdateService.batch_update_companies(invalid_data, check_only=True)
        print(f"无效数据测试结果:")
        print(f"  - 错误数量: {result.get('error_count', 0)}")
        print(f"  - 错误详情:")
        for error in result.get('errors', []):
            print(f"    * {error}")
    except Exception as e:
        print(f"无效数据测试异常: {str(e)}")
    
    # 3. 测试重复名称
    print("\n3. 测试重复名称...")
    duplicate_data = [
        {"company_code": "COMP001", "chi_name": "重复名称测试"},
        {"company_code": "COMP002", "chi_name": "重复名称测试"},  # 批量内部重复
    ]
    
    try:
        result = CompanyUpdateService.batch_update_companies(duplicate_data, check_only=True)
        print(f"重复名称测试结果:")
        print(f"  - 错误数量: {result.get('error_count', 0)}")
        print(f"  - 错误详情:")
        for error in result.get('errors', []):
            print(f"    * {error}")
    except Exception as e:
        print(f"重复名称测试异常: {str(e)}")


def test_large_batch():
    """测试大批量数据"""
    print("\n" + "=" * 60)
    print("测试大批量数据")
    print("=" * 60)
    
    # 生成大批量测试数据
    large_data = []
    for i in range(10):
        large_data.append({
            "company_code": f"COMP{i+1:03d}",
            "chi_name": f"批量测试公司{i+1}",
            "chi_name_abbr": f"测试{i+1}",
            "eng_name": f"Batch Test Company {i+1}"
        })
    
    print(f"生成 {len(large_data)} 条测试数据")
    
    try:
        # 只检查模式
        result = CompanyUpdateService.batch_update_companies(large_data, check_only=True)
        print(f"大批量检查结果:")
        print(f"  - 总数量: {result.get('total_count', 0)}")
        print(f"  - 有效数量: {result.get('valid_count', 0)}")
        print(f"  - 错误数量: {result.get('error_count', 0)}")
        print(f"  - 消息: {result.get('message', '')}")
        
        if result.get('error_count', 0) > 0:
            print(f"  - 前5个错误:")
            for error in result.get('errors', [])[:5]:
                print(f"    * {error}")
    
    except Exception as e:
        print(f"大批量测试异常: {str(e)}")


if __name__ == "__main__":
    print("公司数据批量修改服务测试")
    print("注意：以下测试需要实际的数据库数据")
    print("请修改测试数据中的企业编号为实际存在的值")
    
    # 测试错误情况
    test_error_cases()
    
    # 测试大批量数据
    test_large_batch()
    
    # 测试服务层功能（需要实际数据）
    print("\n如需测试实际更新功能，请取消注释以下行并修改测试数据:")
    print("# test_batch_update_service()")
    
    # test_batch_update_service()  # 取消注释以运行实际测试
