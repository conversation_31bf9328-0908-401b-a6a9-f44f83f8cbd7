#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 11:30:00
# <AUTHOR> Assistant
# @File         : test_company_update.py
# @Description  : 测试公司数据修改功能
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from service_data_manage.service.data_manage_service import CompanyUpdateService
from utils.log_util import LogUtil


def test_company_update_service():
    """测试公司数据修改服务功能"""
    try:
        print("=" * 60)
        print("开始测试公司数据修改服务功能")
        print("=" * 60)
        
        # 初始化日志
        LogUtil.init(process_name="test_company_update")
        
        # 测试用的企业编号（请根据实际数据修改）
        test_company_code = "COMP001"  # 请替换为实际存在的企业编号
        
        # 1. 测试获取公司信息
        print("\n1. 测试获取公司信息...")
        print("-" * 40)
        try:
            company_info = CompanyUpdateService.get_company_info(test_company_code)
            print(f"获取公司信息成功:")
            for key, value in company_info.items():
                print(f"  - {key}: {value}")
        except Exception as e:
            print(f"获取公司信息失败: {str(e)}")
            print("请确保测试用的企业编号存在于数据库中")
            return
        
        # 2. 测试检查名称重复
        print("\n2. 测试检查名称重复...")
        print("-" * 40)
        
        # 测试重复名称
        original_name = company_info.get('chi_name', '')
        if original_name:
            is_duplicate = CompanyUpdateService.check_chi_name_duplicate(original_name, test_company_code)
            print(f"检查原名称 '{original_name}' 是否重复（排除自己）: {is_duplicate}")
            
            is_duplicate_include_self = CompanyUpdateService.check_chi_name_duplicate(original_name)
            print(f"检查原名称 '{original_name}' 是否重复（包含自己）: {is_duplicate_include_self}")
        
        # 测试新名称
        test_new_name = "测试公司名称_" + str(hash(test_company_code))[-6:]
        is_duplicate_new = CompanyUpdateService.check_chi_name_duplicate(test_new_name)
        print(f"检查新名称 '{test_new_name}' 是否重复: {is_duplicate_new}")
        
        # 3. 测试修改公司信息
        print("\n3. 测试修改公司信息...")
        print("-" * 40)
        
        try:
            # 准备测试数据
            new_chi_name = test_new_name
            new_chi_name_abbr = "测试简称"
            new_eng_name = "Test Company Name"
            
            print(f"准备修改:")
            print(f"  - 企业编号: {test_company_code}")
            print(f"  - 原中文名称: {original_name}")
            print(f"  - 新中文名称: {new_chi_name}")
            print(f"  - 新企业别称: {new_chi_name_abbr}")
            print(f"  - 新英文全称: {new_eng_name}")
            
            # 执行修改
            update_result = CompanyUpdateService.update_company_info(
                company_code=test_company_code,
                chi_name=new_chi_name,
                chi_name_abbr=new_chi_name_abbr,
                eng_name=new_eng_name
            )
            
            print(f"\n修改结果:")
            for key, value in update_result.items():
                print(f"  - {key}: {value}")
            
            # 4. 验证修改结果
            print("\n4. 验证修改结果...")
            print("-" * 40)
            
            updated_info = CompanyUpdateService.get_company_info(test_company_code)
            print(f"修改后的公司信息:")
            for key, value in updated_info.items():
                print(f"  - {key}: {value}")
            
            # 5. 恢复原始数据（可选）
            print("\n5. 恢复原始数据...")
            print("-" * 40)
            
            restore_result = CompanyUpdateService.update_company_info(
                company_code=test_company_code,
                chi_name=original_name,
                chi_name_abbr=company_info.get('chi_name_abbr'),
                eng_name=company_info.get('eng_name')
            )
            
            print(f"恢复结果:")
            for key, value in restore_result.items():
                print(f"  - {key}: {value}")
            
        except Exception as e:
            print(f"修改公司信息失败: {str(e)}")
            import traceback
            traceback.print_exc()
        
        print("\n" + "=" * 60)
        print("测试完成！")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


def test_pre_name_logic():
    """测试曾用名逻辑"""
    print("\n" + "=" * 60)
    print("测试曾用名逻辑")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        {
            "old_pre_name": "",
            "old_chi_name": "原公司名称",
            "new_chi_name": "新公司名称",
            "expected": "原公司名称"
        },
        {
            "old_pre_name": "曾用名1,曾用名2",
            "old_chi_name": "原公司名称",
            "new_chi_name": "新公司名称",
            "expected": "曾用名1,曾用名2,原公司名称"
        },
        {
            "old_pre_name": "曾用名1,原公司名称,曾用名2",
            "old_chi_name": "原公司名称",
            "new_chi_name": "新公司名称",
            "expected": "曾用名1,曾用名2,原公司名称"
        },
        {
            "old_pre_name": "曾用名1,曾用名2",
            "old_chi_name": "原公司名称",
            "new_chi_name": "原公司名称",  # 名称没有变化
            "expected": "曾用名1,曾用名2"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"  原曾用名: '{case['old_pre_name']}'")
        print(f"  原中文名: '{case['old_chi_name']}'")
        print(f"  新中文名: '{case['new_chi_name']}'")
        
        result = CompanyUpdateService._update_pre_name(
            case['old_pre_name'],
            case['old_chi_name'],
            case['new_chi_name']
        )
        
        print(f"  实际结果: '{result}'")
        print(f"  期望结果: '{case['expected']}'")
        print(f"  测试结果: {'✓ 通过' if result == case['expected'] else '✗ 失败'}")


def test_error_cases():
    """测试错误情况"""
    print("\n" + "=" * 60)
    print("测试错误情况")
    print("=" * 60)
    
    # 初始化日志
    LogUtil.init(process_name="test_company_update_errors")
    
    # 1. 测试空企业编号
    print("\n1. 测试空企业编号...")
    try:
        CompanyUpdateService.update_company_info("", "测试名称")
        print("✗ 应该抛出异常但没有")
    except ValueError as e:
        print(f"✓ 正确捕获异常: {str(e)}")
    
    # 2. 测试空中文名称
    print("\n2. 测试空中文名称...")
    try:
        CompanyUpdateService.update_company_info("COMP001", "")
        print("✗ 应该抛出异常但没有")
    except ValueError as e:
        print(f"✓ 正确捕获异常: {str(e)}")
    
    # 3. 测试不存在的企业编号
    print("\n3. 测试不存在的企业编号...")
    try:
        CompanyUpdateService.update_company_info("NONEXISTENT", "测试名称")
        print("✗ 应该抛出异常但没有")
    except ValueError as e:
        print(f"✓ 正确捕获异常: {str(e)}")


if __name__ == "__main__":
    # 测试曾用名逻辑
    test_pre_name_logic()
    
    # 测试错误情况
    test_error_cases()
    
    # 测试服务层功能（需要实际数据）
    print("\n注意：以下测试需要实际的数据库数据")
    print("请修改 test_company_code 为实际存在的企业编号")
    # test_company_update_service()
