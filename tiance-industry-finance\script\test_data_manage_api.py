#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : test_data_manage_api.py
# @Description  : 测试数据管理API接口
"""

import requests
import json
import time


def test_data_manage_api():
    """测试数据管理API接口"""
    
    # 配置服务器地址
    base_url = "http://localhost:9029/data_manage"
    
    print("=" * 60)
    print("开始测试数据管理API接口")
    print("=" * 60)
    
    # 测试1: 公司数据统计查询（POST）
    print("\n1. 测试公司数据统计查询 POST /data_manage/company_data_stats")
    print("-" * 50)
    try:
        url = f"{base_url}/company_data_stats"
        response = requests.post(url, json={}, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    time.sleep(1)
    
    # 测试2: 公司数据统计查询（GET）
    print("\n2. 测试公司数据统计查询 GET /data_manage/company_data_stats_simple")
    print("-" * 50)
    try:
        url = f"{base_url}/company_data_stats_simple"
        response = requests.get(url, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    time.sleep(1)
    
    # 测试3: 获取公司信息
    print("\n3. 测试获取公司信息 GET /data_manage/company_info/{company_code}")
    print("-" * 50)
    try:
        test_company_code = "COMP001"  # 请替换为实际存在的企业编号
        url = f"{base_url}/company_info/{test_company_code}"
        response = requests.get(url, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    time.sleep(1)
    
    # 测试4: 检查公司名称重复
    print("\n4. 测试检查公司名称重复 POST /data_manage/company_name_check")
    print("-" * 50)
    try:
        url = f"{base_url}/company_name_check"
        params = {
            "chi_name": "测试公司名称",
            "exclude_company_code": "COMP001"
        }
        response = requests.post(url, params=params, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"请求参数: {params}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    time.sleep(1)
    
    # 测试5: 单个公司修改
    print("\n5. 测试单个公司修改 POST /data_manage/company_update")
    print("-" * 50)
    try:
        url = f"{base_url}/company_update"
        test_data = {
            "company_code": "COMP001",  # 请替换为实际存在的企业编号
            "chi_name": f"测试修改名称_{int(time.time())}",
            "chi_name_abbr": "测试简称",
            "eng_name": "Test Updated Company Name"
        }
        
        response = requests.post(url, json=test_data, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(test_data, indent=2, ensure_ascii=False)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    time.sleep(1)
    
    # 测试6: 批量公司修改
    print("\n6. 测试批量公司修改 POST /data_manage/company_update")
    print("-" * 50)
    try:
        url = f"{base_url}/company_update"
        batch_data = {
            "companies": [
                {
                    "company_code": "COMP001",  # 请替换为实际存在的企业编号
                    "chi_name": f"批量测试A_{int(time.time())}",
                    "chi_name_abbr": "批量A",
                    "eng_name": "Batch Test A"
                },
                {
                    "company_code": "COMP002",  # 请替换为实际存在的企业编号
                    "chi_name": f"批量测试B_{int(time.time())}",
                    "chi_name_abbr": "批量B",
                    "eng_name": "Batch Test B"
                }
            ]
        }
        
        response = requests.post(url, json=batch_data, timeout=30)
        
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(batch_data, indent=2, ensure_ascii=False)}")
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("响应内容:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"请求失败: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {str(e)}")
    
    print("\n" + "=" * 60)
    print("数据管理API接口测试完成")
    print("=" * 60)


def generate_curl_commands():
    """生成curl测试命令"""
    base_url = "http://localhost:9029/data_manage"
    
    print("\n" + "=" * 60)
    print("curl 测试命令")
    print("=" * 60)
    
    print("\n1. 公司数据统计查询（POST）")
    print(f'curl -X POST "{base_url}/company_data_stats" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{}\'')
    
    print("\n2. 公司数据统计查询（GET）")
    print(f'curl -X GET "{base_url}/company_data_stats_simple"')
    
    print("\n3. 获取公司信息")
    print(f'curl -X GET "{base_url}/company_info/COMP001"')
    
    print("\n4. 检查公司名称重复")
    print(f'curl -X POST "{base_url}/company_name_check?chi_name=测试公司&exclude_company_code=COMP001"')
    
    print("\n5. 单个公司修改")
    print(f'curl -X POST "{base_url}/company_update" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{"company_code": "COMP001", "chi_name": "新公司名称", "chi_name_abbr": "新简称", "eng_name": "New Company Name"}\'')
    
    print("\n6. 批量公司修改")
    print(f'curl -X POST "{base_url}/company_update" \\')
    print('     -H "Content-Type: application/json" \\')
    print('     -d \'{"companies": [{"company_code": "COMP001", "chi_name": "批量测试A"}, {"company_code": "COMP002", "chi_name": "批量测试B"}]}\'')


def check_server_status():
    """检查服务器状态"""
    base_url = "http://localhost:9029"
    
    print("检查服务器状态...")
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        if response.status_code == 200:
            print("✓ 服务器运行正常")
            return True
        else:
            print(f"✗ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"✗ 无法连接到服务器: {str(e)}")
        print("请确保服务器已启动并运行在正确的端口上")
        return False


if __name__ == "__main__":
    print("数据管理API测试工具")
    print("请确保服务器已启动")
    
    # 检查服务器状态
    if check_server_status():
        print("\n注意：以下测试需要实际的数据库数据")
        print("请修改测试代码中的企业编号为实际存在的值")
        
        # 取消注释以运行实际测试
        # test_data_manage_api()
        
        print("\n如需运行实际测试，请取消注释 test_data_manage_api() 函数调用")
    else:
        print("\n服务器未运行，显示curl测试命令供参考：")
        generate_curl_commands()
        
        print("\n启动服务器的命令:")
        print("python main.py")
