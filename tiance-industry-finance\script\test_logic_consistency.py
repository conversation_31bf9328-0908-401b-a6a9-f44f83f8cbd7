#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 14:00:00
# <AUTHOR> Assistant
# @File         : test_logic_consistency.py
# @Description  : 测试 update_company_info 和 batch_update_companies 的检查逻辑一致性
"""

import sys
import os
import ast

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def analyze_method_calls(method_node, method_name):
    """分析方法中的函数调用"""
    calls = []
    for node in ast.walk(method_node):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute):
                if (isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == "CompanyUpdateService"):
                    calls.append(node.func.attr)
    
    print(f"\n📊 {method_name} 方法中调用的 CompanyUpdateService 方法:")
    unique_calls = set(calls)
    for call in unique_calls:
        count = calls.count(call)
        print(f"  - {call}: {count} 次")
    
    return unique_calls


def check_logic_consistency():
    """检查逻辑一致性"""
    
    service_file = "service_data_manage/service/data_manage_service.py"
    
    if not os.path.exists(service_file):
        print(f"❌ 文件不存在: {service_file}")
        return False
    
    print("=" * 60)
    print("检查逻辑一致性")
    print("=" * 60)
    
    with open(service_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    try:
        tree = ast.parse(content)
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    
    # 查找 CompanyUpdateService 类
    company_update_service = None
    for node in ast.walk(tree):
        if isinstance(node, ast.ClassDef) and node.name == "CompanyUpdateService":
            company_update_service = node
            break
    
    if not company_update_service:
        print("❌ 未找到 CompanyUpdateService 类")
        return False
    
    print("✅ 找到 CompanyUpdateService 类")
    
    # 查找方法
    methods = {}
    for node in company_update_service.body:
        if isinstance(node, ast.FunctionDef):
            methods[node.name] = node
    
    # 分析 update_company_info 方法
    if "update_company_info" not in methods:
        print("❌ 未找到 update_company_info 方法")
        return False
    
    update_calls = analyze_method_calls(methods["update_company_info"], "update_company_info")
    
    # 分析 batch_update_companies 方法
    if "batch_update_companies" not in methods:
        print("❌ 未找到 batch_update_companies 方法")
        return False
    
    batch_calls = analyze_method_calls(methods["batch_update_companies"], "batch_update_companies")
    
    # 检查一致性
    print(f"\n🔍 检查逻辑一致性:")
    
    # 检查是否都调用了相同的验证方法
    validation_methods = {"get_company_info", "check_chi_name_duplicate"}
    
    update_validation = update_calls & validation_methods
    batch_validation = batch_calls & validation_methods
    
    print(f"  - update_company_info 使用的验证方法: {update_validation}")
    print(f"  - batch_update_companies 使用的验证方法: {batch_validation}")
    
    if update_validation == batch_validation:
        print("✅ 两个方法使用了相同的验证方法，逻辑一致")
        consistency_score = 1.0
    else:
        missing_in_update = batch_validation - update_validation
        missing_in_batch = update_validation - batch_validation
        
        if missing_in_update:
            print(f"⚠️  update_company_info 缺少验证方法: {missing_in_update}")
        if missing_in_batch:
            print(f"⚠️  batch_update_companies 缺少验证方法: {missing_in_batch}")
        
        consistency_score = len(update_validation & batch_validation) / len(update_validation | batch_validation)
        print(f"⚠️  逻辑一致性得分: {consistency_score:.2f}")
    
    # 检查是否 batch 方法复用了 update 方法
    if "update_company_info" in batch_calls:
        print("✅ batch_update_companies 复用了 update_company_info 方法")
        reuse_score = 1.0
    else:
        print("❌ batch_update_companies 没有复用 update_company_info 方法")
        reuse_score = 0.0
    
    # 检查是否有重复的数据库操作
    print(f"\n🔍 检查代码重复:")
    
    # 检查 update_company_info 中的 SQLUtil 调用
    update_sqlutil_calls = []
    for node in ast.walk(methods["update_company_info"]):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute):
                if (isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == "SQLUtil"):
                    update_sqlutil_calls.append(node.func.attr)
    
    # 检查 batch_update_companies 中的 SQLUtil 调用
    batch_sqlutil_calls = []
    for node in ast.walk(methods["batch_update_companies"]):
        if isinstance(node, ast.Call):
            if isinstance(node.func, ast.Attribute):
                if (isinstance(node.func.value, ast.Name) and 
                    node.func.value.id == "SQLUtil"):
                    batch_sqlutil_calls.append(node.func.attr)
    
    print(f"  - update_company_info 中的 SQLUtil 调用: {set(update_sqlutil_calls)}")
    print(f"  - batch_update_companies 中的 SQLUtil 调用: {set(batch_sqlutil_calls)}")
    
    # 计算重复度
    if not batch_sqlutil_calls:
        print("✅ batch_update_companies 没有直接的数据库操作，很好地复用了现有方法")
        duplication_score = 1.0
    else:
        common_calls = set(update_sqlutil_calls) & set(batch_sqlutil_calls)
        if common_calls:
            print(f"⚠️  发现重复的数据库操作: {common_calls}")
            duplication_score = 0.5
        else:
            print("✅ 没有发现重复的数据库操作")
            duplication_score = 1.0
    
    # 总体评分
    overall_score = (consistency_score + reuse_score + duplication_score) / 3
    
    print(f"\n📊 总体评分:")
    print(f"  - 逻辑一致性: {consistency_score:.2f}")
    print(f"  - 方法复用: {reuse_score:.2f}")
    print(f"  - 避免重复: {duplication_score:.2f}")
    print(f"  - 总体得分: {overall_score:.2f}")
    
    if overall_score >= 0.9:
        print("🎉 优秀！代码重构效果很好")
        return True
    elif overall_score >= 0.7:
        print("✅ 良好！代码重构基本达到目标")
        return True
    else:
        print("⚠️  需要改进！代码重构还有优化空间")
        return False


def test_method_signatures():
    """测试方法签名一致性"""
    print(f"\n🔍 检查方法签名:")
    
    try:
        from service_data_manage.service.data_manage_service import CompanyUpdateService
        import inspect
        
        # 获取方法签名
        update_sig = inspect.signature(CompanyUpdateService.update_company_info)
        batch_sig = inspect.signature(CompanyUpdateService.batch_update_companies)
        get_info_sig = inspect.signature(CompanyUpdateService.get_company_info)
        check_dup_sig = inspect.signature(CompanyUpdateService.check_chi_name_duplicate)
        
        print(f"  - update_company_info: {update_sig}")
        print(f"  - batch_update_companies: {batch_sig}")
        print(f"  - get_company_info: {get_info_sig}")
        print(f"  - check_chi_name_duplicate: {check_dup_sig}")
        
        # 检查参数一致性
        update_params = list(update_sig.parameters.keys())
        expected_params = ['company_code', 'chi_name', 'chi_name_abbr', 'eng_name']
        
        if update_params == expected_params:
            print("✅ update_company_info 方法签名正确")
        else:
            print(f"⚠️  update_company_info 方法签名不符合预期")
            print(f"    期望: {expected_params}")
            print(f"    实际: {update_params}")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法签名检查失败: {str(e)}")
        return False


def main():
    """主函数"""
    print("检查逻辑一致性测试工具")
    
    if not os.path.exists("service_data_manage/service/data_manage_service.py"):
        print("❌ 请在项目根目录运行此脚本")
        return False
    
    tests = [
        ("代码逻辑一致性", check_logic_consistency),
        ("方法签名一致性", test_method_signatures),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 通过")
        else:
            print(f"❌ {test_name} 失败")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    print("=" * 60)
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 重构成果:")
        print("- update_company_info 和 batch_update_companies 使用一致的检查逻辑")
        print("- batch_update_companies 复用了 update_company_info 接口")
        print("- 避免了代码重复，提高了维护性")
        print("- 保持了验证逻辑的一致性")
    else:
        print("⚠️  部分测试失败，需要进一步优化")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
