import requests
import pandas as pd

from utils.log_util import LogUtil

from utils.mongodb_util import MongodbUtil
from api.routes.search_related_companies import search_related_companies
from utils.time_util import TimeUtil
from entity.request_entity import SearchRelatedCompaniesRequest
import asyncio

if __name__ == '__main__':
    # 读取Excel文件
    df = pd.read_excel('D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/江苏低空经济-补充公司.xlsx', engine='openpyxl')
    result_dict = {}
    items = []
    start_col_idx = 0  # 第3列
    end_col_idx = 5

    current_column_name = df.columns[4]

    for index, row in df.iterrows():
            value = row[current_column_name]  # 获取当前列的值

            if pd.notna(value):  # 如果值不为空
                    val_str = str(value)

                    first_col_value = str(row[df.columns[0]])  # 第一列
                    second_col_value = str(row[df.columns[1]])  # 第二列
                    tenth_col_value = str(row[df.columns[2]])
                    tenth_col_value1 = str(row[df.columns[3]])
                    tenth_col_value2 = str(row[df.columns[5]]) # 第十列

                    result = {
                        'chain':'低空经济',
                        'chain_end_position': val_str,
                        'source_list': [{'_id':tenth_col_value1,'title':f"{tenth_col_value}:{second_col_value}",'type':first_col_value,'is_listed':'否'}],
                        'abb': tenth_col_value2
                    }
                    items.append(result)

                    # 组合数据
    # print(items)

    LogUtil.init(process_name="module_industry_chain_extension")
    # 初始化数据库连接
    MongodbUtil.connect()
    label_extract_perform_history = MongodbUtil.query_doc_by_id('label_extract_perform_history','76520ff97178457fbead2ff5724a0ed8')
    chain = label_extract_perform_history['chain_structure']
    companies = label_extract_perform_history['company']
    new_chain =[]
    chain_keys = list(chain.keys())
    for key in chain_keys:
        new_chain.append(key.split('|')[-1])
    for item in items:
        v = item.get('chain_end_position')
        if v is not None:
            if v in new_chain:
                index = new_chain.index(v)
                chain_position =chain_keys[index]
                chain[chain_position]['company'].append(item['abb'])
                chain[chain_position]['company'] = list(set(chain[chain_position]['company']))
            else:
                chain['低空经济']['company'].append(item['abb'])
                chain['低空经济']['company'] = list(set(chain['低空经济']['company']))
        else:
            print(f"chain_end_position is None {item}")
        companies.append(item)
    companies1 =[{'abb': '汉鲲智能', 'name': '中山汉鲲智能科技有限公司', 'is_listed': '否', 'is_special': '否', 'is_high_tech': '否', 'stock_code': '-', 'province': '-', 'city': '-', 'industry_position': '', 'stream_type': '上游', 'source_list': [{'_id': 'fb344c3ee7134dafbf4cbc23e23ee404', 'type': 'research_report', 'title': '2024和君：低空经济发展研究报告.pdf', 'file_url': '/research_report/2025/4/14/2024和君：低空经济发展研究报告.pdf'}], 'chain_name': '低空经济', 'chain_end_position': '基础零部件，研发设计，EDA，元器件，低空飞行器制造，PLM，材料，CAX'},{'abb': '宁德时代', 'name': '宁德时代新能源科技股份有限公司', 'is_listed': '是', 'is_special': '否', 'is_high_tech': '否', 'stock_code': '300750.SZ', 'province': '福建省', 'city': '宁德市', 'industry_position': '龙头企业', 'stream_type': '上游', 'source_list': [{'_id': 'fb344c3ee7134dafbf4cbc23e23ee404', 'type': 'research_report', 'title': '2024和君：低空经济发展研究报告.pdf', 'file_url': '/research_report/2025/4/14/2024和君：低空经济发展研究报告.pdf'}, {'_id': '2f76821f2abd4390a65d9597778e5967', 'type': 'research_report', 'title': '202404月19日更新-低空经济系列研究（4）：低空经济产业链标的全景图.pdf', 'file_url': '/research_report/2025/4/14/202404月19日更新-低空经济系列研究（4）：低空经济产业链标的全景图.pdf'}, {'_id': '2f76821f2abd4390a65d9597778e5967', 'type': 'research_report', 'title': '202404月19日更新-低空经济系列研究（4）：低空经济产业链标的全景图.pdf', 'file_url': '/research_report/2025/4/14/202404月19日更新-低空经济系列研究（4）：低空经济产业链标的全景图.pdf'}, {'_id': '2f76821f2abd4390a65d9597778e5967', 'type': 'research_report', 'title': '202404月19日更新-低空经济系列研究（4）：低空经济产业链标的全景图.pdf', 'file_url': '/research_report/2025/4/14/202404月19日更新-低空经济系列研究（4）：低空经济产业链标的全景图.pdf'}, {'_id': 'c6804f7363d04f7c809955a66a965dc6', 'type': 'research_report', 'title': '来觅低空经济-2024年前三季度投融市场报告.pdf', 'file_url': '/research_report/2025/4/14/来觅低空经济-2024年前三季度投融市场报告.pdf'}, {'_id': 'c6804f7363d04f7c809955a66a965dc6', 'type': 'research_report', 'title': '来觅低空经济-2024年前三季度投融市场报告.pdf', 'file_url': '/research_report/2025/4/14/来觅低空经济-2024年前三季度投融市场报告.pdf'}, {'_id': '5cf4a5a89e7040afa7fc834ee450165e', 'type': 'research_report', 'title': '2024国信证券：低空经济主题投资研究.pdf', 'file_url': '/research_report/2025/4/14/2024国信证券：低空经济主题投资研究.pdf'}, {'_id': '76e97997af6e4129b240782fe2e1dedd', 'type': 'research_report', 'title': '“新质生产力”系列（五）聚势而飞：低空经济主题投资研究-国信证券-2024.pdf', 'file_url': '/research_report/2025/5/13/“新质生产力”系列（五）聚势而飞：低空经济主题投资研究-国信证券-2024.pdf'}, {'_id': '76e97997af6e4129b240782fe2e1dedd', 'type': 'research_report', 'title': '“新质生产力”系列（五）聚势而飞：低空经济主题投资研究-国信证券-2024.pdf', 'file_url': '/research_report/2025/5/13/“新质生产力”系列（五）聚势而飞：低空经济主题投资研究-国信证券-2024.pdf'}, {'_id': '7871aa6f26534c20aa9a430d53278478', 'type': 'research_report', 'title': '低空经济发力，关注建筑板块规划_工程机遇-长江证券-2024年.pdf', 'file_url': '/research_report/2025/5/13/低空经济发力，关注建筑板块规划_工程机遇-长江证券-2024年.pdf'}, {'_id': 'e5d8831395b647cda592a28bbd918f79', 'type': 'research_report', 'title': '低空经济2024年前三季度投融市场报告-来觅-2024.pdf', 'file_url': '/research_report/2025/5/13/低空经济2024年前三季度投融市场报告-来觅-2024.pdf'}, {'_id': 'e5d8831395b647cda592a28bbd918f79', 'type': 'research_report', 'title': '低空经济2024年前三季度投融市场报告-来觅-2024.pdf', 'file_url': '/research_report/2025/5/13/低空经济2024年前三季度投融市场报告-来觅-2024.pdf'}, {'_id': 'e2d22e70a4184321bdb5b4ca990cc44c', 'type': 'research_report', 'title': '低空经济的先导产业，飞行汽车商业化渐进-国信证券-2024.pdf', 'file_url': '/research_report/2025/5/13/低空经济的先导产业，飞行汽车商业化渐进-国信证券-2024.pdf'}, {'_id': 'e2d22e70a4184321bdb5b4ca990cc44c', 'type': 'research_report', 'title': '低空经济的先导产业，飞行汽车商业化渐进-国信证券-2024.pdf', 'file_url': '/research_report/2025/5/13/低空经济的先导产业，飞行汽车商业化渐进-国信证券-2024.pdf'}], 'chain_name': 'evtol，无人机，低空经济', 'chain_end_position': '直升机，时的科技，旅游，电池制造商，物流，飞行保障，复合材料，复合材料供应商，通用航空公司，系统，传感器，元器件，直升机运营&低空标准，航空器，运营，中国民用航空局，研发，eVTOL，零部件制造，低空飞行服务，亿航智能，低空飞行，电机，Joby，整机，技术支持，机载系统，无人机运营，动力电池，发动机，电池，无人机，峰飞航空，出行服务，航空发动机，导航，陀螺'}]
    request_res = asyncio.run(search_related_companies(SearchRelatedCompaniesRequest(key_companies=companies1)))
    perform_data = request_res.data


    headers = ['关联方企业全称', '是否上市', '产业地位',
              '股票代码','所属省','所属市','上下游标识（中心客群）','关系扩展',
               '中心客群', '来源标题','来源链接','发布时间']
    rows = []
    for details in perform_data['result']:
        name=details.get('name','')
        if name == ' ':
            pass
        else:
            is_listed = details.get('is_listed', '')
            industry_position = details.get('industry_position', '')
            stock_code = details.get('stock_code', '')
            province = details.get('province', '')
            city = details.get('city', '')
            stream_type = details.get('stream_type', '')
            related_type = details.get('related_type', '')
            key_company = details.get('key_company', '')
            sources = details.get('source_list', [])
            source_title = sources[0].get('source_title', '')
            source_url = sources[0].get('source_url', '')
            source_time = source_url.split('/')[-2]



            row = [name,is_listed,industry_position,stock_code,province,city,stream_type,related_type,key_company,source_title, source_url, source_time]
            rows.append(row)
    df = pd.DataFrame(rows, columns=headers)
    output_file = 'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/中心客群.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')

