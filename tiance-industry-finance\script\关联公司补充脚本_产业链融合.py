import requests
import pandas as pd

from utils.log_util import LogUtil

from utils.mongodb_util import MongodbUtil
from api.routes.search_related_companies import search_related_companies
from configs.collection_config import CollectionConfig
from entity.request_entity import SearchRelatedCompaniesRequest
import asyncio

if __name__ == '__main__':
    MongodbUtil.connect()
    # 读取Excel文件
    df = pd.read_excel('D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/file/江苏低空经济-补充公司.xlsx', engine='openpyxl')
    result_dict = {}
    items = []
    start_col_idx = 0  # 第3列
    end_col_idx = 5
    chain_name = '直升机'
    mongo_id_merge = '5a9795ea064749c79e3eb16c171eef81'
    mongo_id_raw = '5cc367f15f494d48821c39532d7e05e8'
    current_column_name = df.columns[4]

    for index, row in df.iterrows():
            value = row[current_column_name]  # 获取当前列的值

            if pd.notna(value):  # 如果值不为空
                    val_str = str(value)

                    first_col_value = str(row[df.columns[0]])  # 第一列
                    second_col_value = str(row[df.columns[1]])  # 第二列
                    tenth_col_value = str(row[df.columns[2]])
                    tenth_col_value1 = str(row[df.columns[3]])
                    tenth_col_value2 = str(row[df.columns[5]]) #
                    tenth_col_value3 = str(row[df.columns[6]]).replace("（", "(").replace("）", ")")
                    company_basic = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one(
                        {"$or": [{"公司简称": tenth_col_value3}, {"公司名称": tenth_col_value3}]})
                    is_listed = ''
                    province =''
                    city = ''
                    stock_code = ''
                    if company_basic:
                        is_listed = company_basic.get("是否上市", '否')
                        # 上市企业
                        province = company_basic.get("所属省", '否')
                        city = company_basic.get("所属市", '否')
                        stock_code = company_basic.get("股票代码", '否')

                    result = {
                        'chain':f'{chain_name}',
                        'chain_end_position': val_str,
                        'source_list': [{'_id':tenth_col_value1,'title':f"{tenth_col_value}:{second_col_value}",'type':first_col_value,'is_listed':'否'}],
                        'abb': tenth_col_value2,
                        'name': tenth_col_value3,
                        'is_listed':is_listed,
                        'province':province,
                        'city':city,
                        'stock_code':stock_code,
                    }
                    items.append(result)

                    # 组合数据
    # print(items)

    LogUtil.init(process_name="module_industry_chain_extension")
    # 初始化数据库连接

    label_extract_perform_history = MongodbUtil.query_doc_by_id('label_extract_perform_history',mongo_id_raw)
    label_merge_perform_history = MongodbUtil.query_doc_by_id('label_merge_perform_history', mongo_id_merge)
    chain = label_merge_perform_history['chain_structure_llm_merge']
    companies = label_extract_perform_history['company']
    new_chain =[]
    chain_keys = list(chain.keys())
    for key in chain_keys:
        new_chain.append(key.split('|')[-1])
    for item in items:
        v = item.get('chain_end_position')
        if v is not None:
            if v in new_chain:
                indices = [index for index, value in enumerate(new_chain) if value == v]
                # index = new_chain.index(v)
                for index in indices:
                    chain_position =chain_keys[index]
                    chain[chain_position]['company'].append(item['abb'])
                    chain[chain_position]['company'] = list(set(chain[chain_position]['company']))
            else:
                chain[f'{chain_name}']['company'].append(item['abb'])
                chain[f'{chain_name}']['company'] = list(set(chain[f'{chain_name}']['company']))
        else:
            print(f"chain_end_position is None {item}")
        companies.append(item)
    request_res = asyncio.run(search_related_companies(SearchRelatedCompaniesRequest(key_companies=companies)))
    perform_data = request_res.data


    headers = ['关联方企业全称', '是否上市', '产业地位',
              '股票代码','所属省','所属市','上下游标识（中心客群）','关系扩展',
               '中心客群', '来源标题','来源链接','发布时间']
    rows = []
    for details in perform_data['result']:
        name=details.get('name','')
        if name == ' ':
            pass
        elif name == '':
            pass
        else:
            is_listed = details.get('is_listed', '')
            industry_position = details.get('industry_position', '')
            stock_code = details.get('stock_code', '')
            province = details.get('province', '')
            city = details.get('city', '')
            stream_type = details.get('stream_type', '')
            related_type = details.get('related_type', '')
            key_company = details.get('key_company', '')
            sources = details.get('source_list', [])
            source_title = sources[0].get('source_title', '')
            source_url = sources[0].get('source_url', '')
            parts = source_url.split('/')
            if len(parts) >= 2:
                source_time = parts[-2]
            else:
                source_time = ''

            row = [name,is_listed,industry_position,stock_code,province,city,stream_type,related_type,key_company,source_title, source_url, source_time]
            rows.append(row)
    df = pd.DataFrame(rows, columns=headers)
    output_file = f'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/file/{chain_name}中心客群关联企业.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')

