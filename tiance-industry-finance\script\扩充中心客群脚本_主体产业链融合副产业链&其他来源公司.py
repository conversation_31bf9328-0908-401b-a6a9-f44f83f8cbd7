import requests
import pandas as pd

from utils.log_util import LogUtil
from configs.collection_config import CollectionConfig
from utils.mongodb_util import MongodbUtil
from api.routes.search_related_companies import search_related_companies
from utils.time_util import TimeUtil
from entity.request_entity import SearchRelatedCompaniesRequest
import asyncio

if __name__ == '__main__':
    LogUtil.init(process_name="module_industry_chain_extension")
    # 初始化数据库连接
    MongodbUtil.connect()
    # 读取Excel文件
    label_extract_perform_history_B = MongodbUtil.query_doc_by_id('label_merge_perform_history',
                                                                '77b51a6065b44cfbb9168a57a7923f34')
    chain_B = label_extract_perform_history_B['chain_structure']
    companies_B = label_extract_perform_history_B['company']
    new_chain_B = []
    chain_keys_B = list(chain_B.keys())
    items=[]
    for item in chain_B:
        value = item.split('|')[-1]
        for i in companies_B:
            if chain_B[item]['company'] == i['abb']:
                is_listed = i.get('is_listed', '否')
                stock_code = i.get('stock_code','')
                province = i.get('province','')
                city = i.get('city','')
                industry_position = i.get('industry_position','')
                result = {
                    'chain': '低空经济',
                    'chain_end_position': value,
                    'abb': chain_B[item]['company'],
                    'is_listed': is_listed,
                    'stock_code': stock_code,
                    'province': province,
                    'city': city,
                    'industry_position': industry_position
                }
                items.append(result)
                break
            elif chain_B[item]['company'] == i['name']:
                is_listed = i.get('is_listed', '否')
                stock_code = i.get('stock_code', '')
                province = i.get('province', '')
                city = i.get('city', '')
                industry_position = i.get('industry_position', '')
                result = {
                    'chain': '低空经济',
                    'chain_end_position': value,
                    'abb': chain_B[item]['company'],
                    'is_listed': is_listed,
                    'stock_code': stock_code,
                    'province': province,
                    'city': city,
                    'industry_position': industry_position
                }
                items.append(result)
                break
            else:
                result = {
                    'chain': '低空经济',
                    'chain_end_position': value,
                    'abb': chain_B[item]['company'],
                    'is_listed': '否',
                    'stock_code': '',
                    'province': '',
                    'city': '',
                    'industry_position': ''
                }
                items.append(result)
                break




                    # 组合数据
    # print(items)


    label_extract_perform_history = MongodbUtil.query_doc_by_id('label_merge_perform_history','89e11667322d4b1ea6d772d00a7f6485')
    chain = label_extract_perform_history['chain_structure']
    companies = label_extract_perform_history['company']
    new_chain =[]
    chain_keys = list(chain.keys())
    for key in chain_keys:
        new_chain.append(key.split('|')[-1])
    for item in items:
        v = item.get('chain_end_position')
        if v is not None:
            if v in new_chain:
                indices = [index for index, value in enumerate(new_chain) if value == v]
                # index = new_chain.index(v)
                for index in indices:
                    chain_position =chain_keys[index]
                    print(f'\n{chain_position}:'+str(len(chain[chain_position]['company'])))
                    chain[chain_position]['company']=chain[chain_position]['company']+item['abb']
                    chain[chain_position]['company'] = list(set(chain[chain_position]['company']))
                    print(f'\n{chain_position}更新后:' + str(len(chain[chain_position]['company'])))
            else:
                print(f'未找到合适节点，公司放置低空经济，低空经济:' + str(len(chain['低空经济']['company'])))
                chain['低空经济']['company']=chain['低空经济']['company']+item['abb']
                chain['低空经济']['company'] = list(set(chain['低空经济']['company']))
                print(f'未找到合适节点，公司放置低空经济，去重后:' + str(len(chain['低空经济']['company'])))
        else:
            print(f"chain_end_position is None {item}")
        print(f'\n低空经济'+str(len(chain['低空经济']['company'])))
        companies.append(item)

    #读取外部数据
    df = pd.read_excel(
        'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/file/江苏低空经济-补充公司.xlsx',
        engine='openpyxl')
    result_dict = {}
    items = []
    start_col_idx = 0  # 第3列
    end_col_idx = 5
    chain_name = '低空经济'
    current_column_name = df.columns[4]

    for index, row in df.iterrows():
        value = row[current_column_name]  # 获取当前列的值

        if pd.notna(value):  # 如果值不为空
            val_str = str(value)

            first_col_value = str(row[df.columns[0]])  # 第一列
            second_col_value = str(row[df.columns[1]])  # 第二列
            tenth_col_value = str(row[df.columns[2]])
            tenth_col_value1 = str(row[df.columns[3]])
            tenth_col_value2 = str(row[df.columns[5]])
            tenth_col_value3 = str(row[df.columns[6]]).replace("（", "(").replace("）", ")")
            company_basic = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one(
                {"$or": [{"公司简称": tenth_col_value3}, {"公司名称": tenth_col_value3}]})
            is_listed = ''
            province = ''
            city = ''
            stock_code = ''
            if company_basic:
                is_listed = company_basic.get("是否上市", '否')
                # 上市企业
                province = company_basic.get("所属省", '否')
                city = company_basic.get("所属市", '否')
                stock_code = company_basic.get("股票代码", '否')
            # 第十列

            result = {
                'chain': f'{chain_name}',
                'chain_end_position': val_str,
                'source_list': [
                    {'_id': tenth_col_value1, 'title': f"{tenth_col_value}:{second_col_value}", 'type': first_col_value,
                     'is_listed': '否'}],
                'abb': tenth_col_value2,
                'name': tenth_col_value3,
                'is_listed':is_listed,
                'province':province,
                'city':city,
                'stock_code':stock_code,
            }
            items.append(result)

    for item in items:
        v = item.get('chain_end_position')
        if v is not None:
            if v in new_chain:
                indices = [index for index, value in enumerate(new_chain) if value == v]
                # index = new_chain.index(v)
                for index in indices:
                    chain_position =chain_keys[index]
                    print(f'额外数据--\n{chain_position}:'+str(len(chain[chain_position]['company'])))
                    chain[chain_position]['company'].append(item['abb'])
                    chain[chain_position]['company'] = list(set(chain[chain_position]['company']))
                    print(f'额外数据--\n{chain_position}更新后:' + str(len(chain[chain_position]['company'])))
            else:
                print(f'额外数据--未找到合适节点，公司放置低空经济，低空经济:' + str(len(chain['低空经济']['company'])))
                chain['低空经济']['company'].append(item['abb'])
                chain['低空经济']['company'] = list(set(chain['低空经济']['company']))
                print(f'额外数据--未找到合适节点，公司放置低空经济，去重后:' + str(len(chain['低空经济']['company'])))
        else:
            print(f"chain_end_position is None {item}")
        print(f'\n低空经济'+str(len(chain['低空经济']['company'])))
        companies.append(item)

    # request_res = asyncio.run(search_related_companies(SearchRelatedCompaniesRequest(key_companies=companies)))
    # perform_data = request_res.data


    headers = ['产业链类型', '上中下游', '一级产业链环节',
              '二级产业链环节','三级产业链环节','四级产业链环节','五级产业链环节','六级产业链环节','原文公司全称','公司全称',
               '来源', '来源标题','是否上市','产业地位','股票代码','所属省','所属市']
    rows = []
    for label, details in chain.items():
        if len(details['company']) == 0:
            fill_value = ''
            extended_list = label.split('|')
            num_to_add = max(0, 8 - len(extended_list))
            v = extended_list + [fill_value] * num_to_add
            row = [v[0], v[1], v[2], v[3], v[4], v[5], v[6],v[7],'', '', '', '', '', '',
                   '', '', '']
            rows.append(row)
        else:
            for i in details['company']:

                extended_list = label.split('|')
                # 填充值
                fill_value = ''

                # 计算需要添加的填充值数量
                num_to_add = max(0, 8 - len(extended_list))
                company_name = i
                for index, co in enumerate(companies):
                    if company_name == co.get('abb',''):
                        province = co.get('province','')
                        name = co.get('name','')
                        city = co.get('city', '')
                        industry_position = co.get('industry_position','')
                        sources = co.get('source_list','')
                        is_listed = co.get('is_listed', '否')
                        stock_code = co.get('stock_code', '')
                        source=''
                        title =''
                        for s in sources:
                            if s.get('type','') == 'research_report':
                                source = '研报'
                            else:
                                source = s.get('type', '')
                            title = title+s.get('title','')+','

                        # 扩展列表
                        v = extended_list + [fill_value] * num_to_add
                        row = [v[0], v[1], v[2], v[3], v[4], v[5], v[6],v[7], company_name,name,source,title, is_listed, industry_position,stock_code,province, city]
                        rows.append(row)
                        break
                    else:
                        if company_name == co.get('name',''):
                            province = co.get('province','')
                            name = co.get('abb','')
                            city = co.get('city', '')
                            industry_position = co.get('industry_position','')
                            stock_code = co.get('stock_code', '')
                            is_listed = co.get('is_listed', '否')
                            sources = co.get('source_list','')
                            source = ''
                            title = ''
                            # for s in sources:
                            #     if s.get('type', '') == 'research_report':
                            #         source = source + '研报,'
                            #     else:
                            #         source = source + s.get('type', '') + ','
                            #     title = title + s.get('title', '') + ','

                            for s in sources:
                                if s.get('type', '') == 'research_report':
                                    source = '研报'
                                else:
                                    source = s.get('type', '')
                                title =s.get('title', '')
                            # 扩展列表
                            v = extended_list + [fill_value] * num_to_add
                            row = [v[0], v[1], v[2], v[3], v[4], v[5], v[6],v[7], name,company_name,source,title,is_listed, industry_position,stock_code,province, city]
                            rows.append(row)
                            break
    df = pd.DataFrame(rows, columns=headers)
    output_file = 'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/file/低空经济_扩充额外数据源客群.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')
    print('文件生成成功')

