import requests
import pandas as pd

from utils.log_util import LogUtil
from configs.collection_config import CollectionConfig
from utils.mongodb_util import MongodbUtil
from api.routes.search_related_companies import search_related_companies
from utils.time_util import TimeUtil
from entity.request_entity import SearchRelatedCompaniesRequest
import asyncio

if __name__ == '__main__':
    MongodbUtil.connect()
    # 读取Excel文件
    df = pd.read_excel('D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/file/江苏低空经济-补充公司.xlsx', engine='openpyxl')
    result_dict = {}
    items = []
    start_col_idx = 0  # 第3列
    end_col_idx = 5
    chain_name = '直升机'
    mongo_id_merge = '5a9795ea064749c79e3eb16c171eef81'
    mongo_id_raw = '5cc367f15f494d48821c39532d7e05e8'
    current_column_name = df.columns[4]

    for index, row in df.iterrows():
            value = row[current_column_name]  # 获取当前列的值

            if pd.notna(value):  # 如果值不为空
                    val_str = str(value)

                    first_col_value = str(row[df.columns[0]])  # 第一列
                    second_col_value = str(row[df.columns[1]])  # 第二列
                    tenth_col_value = str(row[df.columns[2]])
                    tenth_col_value1 = str(row[df.columns[3]])
                    tenth_col_value2 = str(row[df.columns[5]]) #
                    tenth_col_value3 = str(row[df.columns[6]]).replace("（", "(").replace("）", ")")
                    company_basic = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one(
                        {"$or": [{"公司简称": tenth_col_value3}, {"公司名称": tenth_col_value3}]})
                    is_listed = ''
                    province =''
                    city = ''
                    stock_code = ''
                    if company_basic:
                        is_listed = company_basic.get("是否上市", '否')
                        # 上市企业
                        province = company_basic.get("所属省", '否')
                        city = company_basic.get("所属市", '否')
                        stock_code = company_basic.get("股票代码", '否')

                    result = {
                        'chain':f'{chain_name}',
                        'chain_end_position': val_str,
                        'source_list': [{'_id':tenth_col_value1,'title':f"{tenth_col_value}:{second_col_value}",'type':first_col_value,'is_listed':'否'}],
                        'abb': tenth_col_value2,
                        'name': tenth_col_value3,
                        'is_listed':is_listed,
                        'province':province,
                        'city':city,
                        'stock_code':stock_code,
                    }
                    items.append(result)

                    # 组合数据
    # print(items)

    LogUtil.init(process_name="module_industry_chain_extension")
    # 初始化数据库连接

    label_extract_perform_history = MongodbUtil.query_doc_by_id('label_extract_perform_history',mongo_id_raw)
    label_merge_perform_history = MongodbUtil.query_doc_by_id('label_merge_perform_history', mongo_id_merge)
    chain = label_merge_perform_history['chain_structure_llm_merge']
    companies = label_extract_perform_history['company']
    new_chain =[]
    chain_keys = list(chain.keys())
    for key in chain_keys:
        new_chain.append(key.split('|')[-1])
    for item in items:
        v = item.get('chain_end_position')
        if v is not None:
            if v in new_chain:
                indices = [index for index, value in enumerate(new_chain) if value == v]
                # index = new_chain.index(v)
                for index in indices:
                    chain_position =chain_keys[index]
                    chain[chain_position]['company'].append(item['abb'])
                    chain[chain_position]['company'] = list(set(chain[chain_position]['company']))
            else:
                chain[f'{chain_name}']['company'].append(item['abb'])
                chain[f'{chain_name}']['company'] = list(set(chain[f'{chain_name}']['company']))
        else:
            print(f"chain_end_position is None {item}")
        companies.append(item)


    headers = ['产业链类型', '上中下游', '一级产业链环节',
              '二级产业链环节','三级产业链环节','四级产业链环节','五级产业链环节','六级产业链环节','原文公司全称','公司全称',
               '来源', '来源标题','是否上市','产业地位','股票代码','所属省','所属市']
    rows = []
    for label, details in chain.items():
        if len(details['company']) == 0:
            fill_value = ''
            extended_list = label.split('|')
            num_to_add = max(0, 8 - len(extended_list))
            v = extended_list + [fill_value] * num_to_add
            row = [v[0], v[1], v[2], v[3], v[4], v[5], v[6],v[7],'', '', '', '', '', '',
                   '', '', '']
            rows.append(row)
        else:
            for i in details['company']:
                extended_list = label.split('|')
                # 填充值
                fill_value = ''

                # 计算需要添加的填充值数量
                num_to_add = max(0, 8 - len(extended_list))
                company_name = i
                for index, co in enumerate(companies):
                    if company_name == co.get('abb',''):
                        province = co.get('province','')
                        name = co.get('name','')
                        city = co.get('city', '')
                        industry_position = co.get('industry_position', '')
                        stock_code = co.get('stock_code', '')
                        is_listed = co.get('is_listed', '否')
                        sources = co.get('source_list','')
                        source=''
                        title =''
                        for s in sources:
                            if s.get('type','') == 'research_report':
                                source = '研报'
                            else:
                                source = s.get('type', '')
                            title = title+s.get('title','')+','

                        # 扩展列表
                        v = extended_list + [fill_value] * num_to_add
                        row = [v[0], v[1], v[2], v[3], v[4], v[5], v[6],v[7], company_name,name,source,title, is_listed, industry_position,stock_code,province, city]
                        rows.append(row)
                        break
                    else:
                        if company_name == co.get('name',''):
                            province = co.get('province','')
                            name = co.get('abb','')
                            city = co.get('city', '')
                            industry_position = co.get('industry_position','')
                            stock_code = co.get('stock_code', '')
                            is_listed = co.get('is_listed', '否')
                            sources = co.get('source_list','')
                            source = ''
                            title = ''
                            # for s in sources:
                            #     if s.get('type', '') == 'research_report':
                            #         source = source + '研报,'
                            #     else:
                            #         source = source + s.get('type', '') + ','
                            #     title = title + s.get('title', '') + ','

                            for s in sources:
                                if s.get('type', '') == 'research_report':
                                    source = '研报'
                                else:
                                    source = s.get('type', '')
                                title =s.get('title', '')
                            # 扩展列表
                            v = extended_list + [fill_value] * num_to_add
                            row = [v[0], v[1], v[2], v[3], v[4], v[5], v[6],v[7], name,company_name,source,title,is_listed, industry_position,stock_code,province, city]
                            rows.append(row)
                            break
    df = pd.DataFrame(rows, columns=headers)
    output_file = f'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/file/{chain_name}中心客群补充_产业链已融合.xlsx'
    df.to_excel(output_file, index=False, engine='openpyxl')
    print(f"产业链{chain_name}中心客群补充完成")

