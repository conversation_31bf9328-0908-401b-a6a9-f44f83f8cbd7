from utils.text_embed_util import TextEmbedService
from utils.milvus_util import MilvusUtil
from utils.llm_model_util import Llm_Service,MessageConverter,UserMessage
from utils.reranker_util import QueryReranker
from configs.model_config import ModelConfig
from configs.collection_config import CollectionConfig
import uuid
import re
from utils.mongodb_util import MongodbUtil
from service.filter_company_rules_service import common_clean_process
# from configs.collection_config import CollectionConfig
import json

filter_keywords = ["供应商","客户"]

reranker = QueryReranker()


class AnnualReportCompanyAnalysis():
    def __init__(self, milvus_client, text_embedding_service,collection_name):
        self.milvus_util = milvus_client
        self.embutil = text_embedding_service
        
        self.llm_service = Llm_Service()
        self.pdf_name_to_filename = {
            "中国石油": "中国石油天然气股份有限公司2023年度报告",
            "南钢股份":"南钢股份-南京钢铁股份有限公司2023年年度报告",
            "天奇股份":"天奇股份-2023年年度报告",
            "中国石化":"中国石化2023年年度报告"
        }
        self.collection_name = collection_name
    
    def common_rag(self,query,pdf_filename,keywords):
        queryemb = self.embutil.text_embedding([query])
        filter = f"file_title == '{pdf_filename}'"
        K = 8
        docs = self.milvus_util.search_by_vector(collection_name=self.collection_name, vector=queryemb, limit=K,expr = filter)[0]
        chunk_contents = [doc['entity']['chunk_content_father'].replace('|',' ').replace('-',' ') for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]
        if(len(chunk_contents) >0 ):
            # 重排
            reranked_results = reranker.rerank_query(query,chunk_contents)
            # 获取重排序结果中的 top-k 索引
            top_k = 5  # 获取排名前 5 的文档
            top_k_indices = reranker.get_top_x_indices(reranked_results, top_k)
            # 根据索引获取 top-k 的文档内容
            chunk_contents = reranker.get_top_x_corpus(chunk_contents, top_k_indices)
            # 根据索引获取 top-k 的文档内容
            chunk_contents_info = reranker.get_top_x_corpus(docs, top_k_indices)
        filter_keywords = []
        for keyword in keywords:
            filter_keywords.append(f"chunk_content_son like \"%{keyword}%\" ")
        
        filter_keyword = "||".join(filter_keywords)
        # keyword
        docs2 = self.milvus_util.search_by_filter(collection_name=self.collection_name, filter =f"file_title == '{pdf_filename}'" +f"&& ({filter_keyword})")

        result_test = []
        for i, content in enumerate(docs2):
            result_test.append(content['chunk_content_father'].replace('|',' ').replace('-',' ').replace('\n',' '))
            # page_num 0520 更新
            # page_num= int(content["chunk_type"].split(':')[-1])
            # print(content)
            # print(self.collection_name)
            page_num = int(json.loads(content["file_flag"].replace('\'','\"'))["块码"])
            
            print(f"keyword2 hit in {page_num}")
            for j in range(1,3):
                page_num_str = str({"块码": page_num+j})
                page_num_str =page_num_str.replace('\'','\\\'')
                # print(page_num_str)
                filter = f"file_flag == '{page_num_str}' && file_title == '{pdf_filename}'"

                pages = self.milvus_util.search_by_filter(collection_name=self.collection_name, filter = filter)
                if len(pages) >0:
                    pages = pages[0]
                else:
                    break

                if pages:
                    # print("pages",pages)
                    print("has pages1")
                    # print(pages['chunk_content_father'].replace('|',' ').replace('-',' ') )
                    # result_test.append(pages['chunk_content_father'] )
                    result_test.append(pages['chunk_content_father'].replace('|',' ').replace('-',' '))
            break
        documents = ""
        content_list = []
        is_hit = False
        if len(result_test) >0:
        #     chunk_contents = result_test
            print("hit replace vector search by keyword")
            content_list = result_test
            documents = "".join(result_test)
            is_hit = True
        else :
            for i, content in enumerate(chunk_contents):
                    documents += f"\n------------------第{i+1}份数据------------------\n{content}"
    
                    content_list.append(content)
        # print(documents)
        return documents,is_hit


    # 需要处理的关系
    # 母公司,子公司,合营企业/联营企业,其他关联方,采购商品/接受劳务,出售商品/提供劳务,
    # 租赁/出租方,担保/担保方,担保/被担保方,资金拆借的资金拆入,资金拆出, 资产转让,债务重组
    def company_parentand(self,company_name,pdf_filename):
        query = f"{company_name}公司在 **本企业的母公司情况**查找母公司"
        queryemb = self.embutil.text_embedding([query])
        
 
        filter = f"file_title == '{pdf_filename}'"
        K = 8
        docs = self.milvus_util.search_by_vector(collection_name=self.collection_name, vector=queryemb, limit=K,expr = filter)[0]
        chunk_contents = [doc['entity']['chunk_content_father'] for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]
        
        
        documents = ""
        content_list = []
        for i, content in enumerate(chunk_contents):
                documents += f"\n------------------第{i+1}份数据------------------\n{content}"
                content_list.append(content)
              


        question = f'这份数据中，提取<{company_name}>这家公司的相关母公司信息'
        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                母公司的定义:
                1. **直接持股** 超过百分之50%的公司
                2. 能够对子公司的经营决策等重大事项产生控制作用的公司
                3. 一个子公司只能有一个直接持股的母公司，联合控制的公司一般通过合资公司控股子公司，若有遇到，请返回对应联合合资公司
                4. 返回应该为公司，而不是人,若没有，则不需要返回
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |可能母公司A|可能母公司B|
                
                样例2：
                |可能母公司A|
              
                样例3：
                ||
                
                输出内容为可能母公司名称，为数据中的原名，按可能性顺序从大到小依次分割，每个母公司使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)
        # print(response)
        rows = response.strip().split('\n')
        parentand_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                parentand_dict["母公司"] = [columns[0]]
                parentand_dict["is_keyword_hit"] = False
        return parentand_dict


    def company_subsidiary(self,company_name,pdf_filename):
        query = f"{company_name}公司 从**在其他主体中的权益**中**在子公司中的权益** 或 **财务报表**中 **子公司情况** 相关子公司情况"
        # query = "在子公司中的权益"
        queryemb = self.embutil.text_embedding([query])
        

        filter = f"file_title == '{pdf_filename}'"
        K = 8
        docs = self.milvus_util.search_by_vector(collection_name=self.collection_name, vector=queryemb, limit=K,expr = filter)[0]
        chunk_contents = [doc['entity']['chunk_content_father'].replace('|',' ').replace('-',' ') for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]
        # chunk_contents2 = [doc['entity']['chunk_content_son']for doc in docs if 'entity' in doc and 'chunk_content_son' in doc['entity']]
        # print(chunk_contents)
        if(len(chunk_contents) >0 ):
            # 重排
            reranked_results = reranker.rerank_query(query,chunk_contents)
            # 获取重排序结果中的 top-k 索引
            top_k = 5  # 获取排名前 5 的文档
            top_k_indices = reranker.get_top_x_indices(reranked_results, top_k)
            # 根据索引获取 top-k 的文档内容
            chunk_contents = reranker.get_top_x_corpus(chunk_contents, top_k_indices)
            # 根据索引获取 top-k 的文档内容
            chunk_contents_info = reranker.get_top_x_corpus(docs, top_k_indices)



        # author hwh
        # 如果有遇到特定的标题，+5页 
        # 考虑使用llm自动判断是否需要翻页
        is_hit = False
        result_test = []
        for i, content in enumerate(chunk_contents):
            if "子公司中的权益" in content or "子公司情况" in content:
                is_hit = True
                result_test.append(chunk_contents[i])
                #页码
                # print(chunk_contents_info[i]['entity']["chunk_type"])
                # 0520 更新为新milvus
                # page_num= int(chunk_contents_info[i]['entity']["chunk_type"].split(': ')[-1])
                page_num = int(json.loads(chunk_contents_info[i]['entity']["file_flag"].replace('\'','\"'))["块码"])
                print(f"keyword hit in {page_num}")
                for j in range(1,5):
                    page_num_str = str({"块码": page_num+j})
                    page_num_str =page_num_str.replace('\'','\\\'')
                    filter = f"file_flag == '{page_num_str}' && file_title == '{pdf_filename}'"
                    pages_origin = list(self.milvus_util.search_by_filter(collection_name=self.collection_name, filter = filter))
                    pages = None
                    if len(pages_origin) > 0:
                        # print("test")
                        pages = pages_origin[0]
                    # else:
                    #     print("test2")
                    if pages:
                        print("has pages2")
                        # print(pages['chunk_content_father'].replace('|',' ').replace('-',' '))
                        result_test.append(pages['chunk_content_father'].replace('|',' ').replace('-',' ') )
                break
                # print(result_test)
        
        
        
        documents = ""
        content_list = []
        if len(result_test) >0:
        #     chunk_contents = result_test
            print("hit replace vector search by keyword")
            content_list = result_test
            documents = "".join(result_test)
        else :
            for i, content in enumerate(chunk_contents):
                    documents += f"\n------------------第{i+1}份数据------------------\n{content}"
    
                    content_list.append(content)
        print("----------子公司情况----------")
        
        # print(documents)        
   
        question = f'从这份数据中，提取<{company_name}>这家公司的相关子公司信息'
        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                相关子公司信息优先从 从**在其他主体中的权益**中**在子公司中的权益** 和 **财务报表**中 **子公司情况** 抽取

                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |子公司A|子公司B|
                
                样例2：
                |子公司A|
            
                样例3：
                ||
            
                
                输出内容为子公司名称，每个子公司使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME,max_tokens=2000)
        
        rows = response.strip().split('\n')

        subsidiary_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                subsidiary_dict["子公司"] = columns
                subsidiary_dict["is_keyword_hit"] = is_hit
        
        return subsidiary_dict


    def company_cooperative_enterprise(self,company_name,pdf_filename):
    
        query = f"{company_name}的主要联营企业和合营企业情况"
        queryemb = self.embutil.text_embedding([query])
        
        filter = f"file_title == '{pdf_filename}'"
        K = 8
        docs = self.milvus_util.search_by_vector(collection_name=self.collection_name, vector=queryemb, limit=K,expr = filter)[0]
        chunk_contents = [doc['entity']['chunk_content_father'].replace('|',' ').replace('-',' ')  for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]
        chunk_contents = list(set(chunk_contents))
        # print(chunk_contents)
        if(len(chunk_contents) >0 ):
            # 重排
            reranked_results = reranker.rerank_query(query,chunk_contents)
            # 获取重排序结果中的 top-k 索引
            top_k = 3  # 获取排名前 5 的文档
            top_k_indices = reranker.get_top_x_indices(reranked_results, top_k)
            # 根据索引获取 top-k 的文档内容
            chunk_contents = reranker.get_top_x_corpus(chunk_contents, top_k_indices)
            # 根据索引获取 top-k 的文档内容
            chunk_contents_info = reranker.get_top_x_corpus(docs, top_k_indices)
        
        is_hit = False
        result_test = []
        for i, content in enumerate(chunk_contents):
            if "本企业合营和联营企业情况" in content :
                is_hit = True
                result_test.append(chunk_contents[i])
                #页码
                # print(chunk_contents_info[i]['entity']["chunk_type"])
                page_num = int(json.loads(chunk_contents_info[i]['entity']["file_flag"].replace('\'','\"'))["块码"])
                print(f"keyword hit in {page_num}")
                for j in range(1,2):
                    page_num_str = str({"块码": page_num+j})
                    page_num_str =page_num_str.replace('\'','\\\'')
                    filter = f"file_flag == '{page_num_str}' && file_title == '{pdf_filename}'"
                    pages_origin = list(self.milvus_util.search_by_filter(collection_name=self.collection_name, filter = filter))
                    pages = None
                    if len(pages_origin) > 0:
                        # print("test")
                        pages = pages_origin[0]
                    # else:
                    #     # print("test2")
                    if pages:
                        print("has pages3")
                        result_test.append(pages['chunk_content_father'].replace('|',' ').replace('-',' ') )
                break

        documents = ""
        content_list = []
        if len(result_test) >0:
        #     chunk_contents = result_test
            print("hit replace vector search by keyword")
            content_list = result_test
            documents = "".join(result_test)
        else :
            for i, content in enumerate(chunk_contents):
                    documents += f"\n------------------第{i+1}份数据------------------\n{content}"
    
                    content_list.append(content)
        print("----------联营企业和合营企业----------")        
        # print(documents) 
        question = f'从这份数据中，提取{company_name}的联营企业和合营企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                相关信息优先从**本企业合营和联营企业情况**抽取,
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |联营企业或合营企业A|联营企业或合营企业B|
                
                样例2：
                |联营企业或合营企业A|
            
                样例3：
                ||
            
                输出内容为联营企业或合营企业名称，每个联营企业或合营企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        cooperative_enterprise_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                cooperative_enterprise_dict["联营企业和合营企业"] = list(set(columns))
                cooperative_enterprise_dict["is_keyword_hit"] = is_hit
        
        return cooperative_enterprise_dict
    
    
    def company_other_related(self,company_name,pdf_filename):
        # query = f"{company_name}的其他关联方企业情况"
        # print("test")
        query = f"{company_name}的 其他关联方情况 不存在控制关系的主要关联方 其他关联方名称"
        
        queryemb = self.embutil.text_embedding([query])
        

        keywords=["其他关联方情况","不存在控制关系的主要关联方"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)

        print("----------其他关联方----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的其他关联方企业的信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词,如果没有，请输出||
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |其他关联方企业A|其他关联方企业B|
                
                样例2：
                |其他关联方企业A|
            
                样例3：
                ||
            
                
                输出内容为其他关联方企业的企业名称，每个其他关联方企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')

        other_related_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                other_related_dict["其他关联方"] = columns
                other_related_dict["is_keyword_hit"] = is_hit

        return other_related_dict
    
    
    def company_purchase_goods(self,company_name,pdf_filename):
        query = f"{company_name}主要采购商品或接受劳务的企业情况"
        queryemb = self.embutil.text_embedding([query])
        
        keywords=["购销商品、提供和接受劳务的关联交易"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)

        
        print("----------采购商品或接受劳务----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的采购商品或接受劳务的相关企业信息'
        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词,如果没有，请输出||
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |采购商品或接受劳务A|采购商品或接受劳务B|
                
                样例2：
                |采购商品或接受劳务A|
            
                样例3：
                ||
            
                输出内容为采购商品或接受劳务的企业名称，每个采购商品或接受劳务的企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                不要输出代词，如其他关联方
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        purchase_goods_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                purchase_goods_dict["采购商品或接受劳务"] = columns
                purchase_goods_dict["is_keyword_hit"] = is_hit
        
        return purchase_goods_dict


    def company_sell_goods(self,company_name,pdf_filename):
        query = f"{company_name}主要出售商品或提供劳务的企业情况"
        queryemb = self.embutil.text_embedding([query])
        

        
        keywords=["出售商品/提供劳务情况表"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------出售商品或提供劳务----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的采购商品或接受劳务的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |出售商品或提供劳务企业A|出售商品或提供劳务企业B|
                
                样例2：
                |出售商品或提供劳务企业A|
            
                样例3：
                ||
            
                
                输出内容为出售商品或提供劳务的企业名称，每个出售商品或提供劳务的企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                不要输出代词，如其他关联方
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')

        sell_goods_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                sell_goods_dict["出售商品或提供劳务"] = columns
                sell_goods_dict["is_keyword_hit"] = is_hit
        return sell_goods_dict


    def company_lessor(self,company_name,pdf_filename):
        query = f"{company_name}主要租赁或出租方的企业情况"
        queryemb = self.embutil.text_embedding([query])
        
        keywords=["关联租赁情况"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------主要租赁或出租方----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的主要租赁或出租方的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词

                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |主要租赁或出租方A|主要租赁或出租方B|
                
                样例2：
                |主要租赁或出租方A|
            
                样例3：
                ||
                
                输出内容为主要租赁或出租方的企业名称，每个主要租赁或出租方的企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        lessor_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                lessor_dict["主要租赁或出租方"] = columns
                lessor_dict["is_keyword_hit"] = is_hit
        return lessor_dict

    
    def company_guarantee(self,company_name,pdf_filename):
        query = f"{company_name}主要担保方的企业情况"
        queryemb = self.embutil.text_embedding([query])
        

        keywords=["本公司作为担保方"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------主要担保方----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的担保方的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |担保方企业A|担保方企业B|
                
                样例2：
                |担保方企业A|
            
                样例3：
                ||
            
                输出内容为担保方的企业名称，每个担保方的企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """

        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        guarantee_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                guarantee_dict["担保方"] = columns
                guarantee_dict["is_keyword_hit"]=is_hit
        return guarantee_dict
    
    
    def company_guaranteed(self,company_name,pdf_filename):
        query = f"{company_name}主要被担保方的企业情况"
        queryemb = self.embutil.text_embedding([query])
        
        keywords=["本公司作为被担保方"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------主要被担保或被担保方----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的主要被担保方的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |被担保方企业A|被担保方企业B|
                
                样例2：
                |被担保方企业A|
            
                样例3：
                ||
                
                输出内容为主要被担保或被担保方的企业名称，每个主要被担保或被担保方的企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        guaranteed_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                guaranteed_dict["被担保方"] = columns
                guaranteed_dict["is_keyword_hit"] = is_hit
        return guaranteed_dict
    
    
    def company_funds_borrowed(self,company_name,pdf_filename):
        query = f"{company_name}属于资金拆借的资金拆入的企业情况"
        queryemb = self.embutil.text_embedding([query])
        

        keywords=["关联方资金拆借"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------资金拆借的资金拆入----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的资金拆借的资金拆入的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |资金拆入企业A|资金拆入企业B|
                
                样例2：
                |资金拆入企业A|
            
                样例3：
                ||
            
                输出内容为资金拆入企业的企业名称，每个资金拆入企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        funds_borrowed_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                funds_borrowed_dict["资金拆入"] = columns
                funds_borrowed_dict["is_keyword_hit"] = is_hit
        
        return funds_borrowed_dict
    
       
    def company_funds_withdrawal(self,company_name,pdf_filename):
        query = f"{company_name}的属于资金拆借的资金拆出的企业情况"
        queryemb = self.embutil.text_embedding([query])
        

        keywords=["关联方资金拆借"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------资金拆借的资金拆出----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的资金拆借的资金拆出的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |资金拆出企业A|资金拆出企业B|
                
                样例2：
                |资金拆出企业A|
            
                样例3：
                ||
            
                输出内容为资金拆出企业的企业名称，每个资金拆出企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        funds_withdrawal_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                funds_withdrawal_dict["资金拆出企业"] = columns
                funds_withdrawal_dict["is_keyword_hit"] = is_hit
        
        return funds_withdrawal_dict


    def company_asset_transfer(self,company_name,pdf_filename):
        query = f"{company_name}资产转让的企业情况"
        queryemb = self.embutil.text_embedding([query])
        

        keywords=["关联方资产转让"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------资产转让----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的资产转让的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从 {" ".join(keywords)} 抽取，请注意，只抽取公司名称，忽略代词
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |资产转让企业A|资产转让企业B|
                
                样例2：
                |资产转让企业A|
            
                样例3：
                ||
                
                输出内容为资产转让企业的企业名称，每个资产转让企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        asset_transfer_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                asset_transfer_dict["资产转让"] = columns
                asset_transfer_dict["is_keyword_hit"] = is_hit
        return asset_transfer_dict
    
    
    def company_debt_restructuring(self,company_name,pdf_filename):
        query = f"{company_name}债务重组的企业情况"
        queryemb = self.embutil.text_embedding([query])
        

        keywords=["关联方资产转让、债务重组情况"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)
        print("----------债务重组----------")        
        # print(documents)

        question = f'从这份数据中，提取<{company_name}>这家公司的债务重组的相关企业信息'

        for_llm= f"""
                你是一名优异的年报内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                
                数据：{documents}
                需求：{question}
                
                输出样例如下
                样例1：
                |债务重组企业A|债务重组企业B|
                
                样例2：
                |债务重组企业A|
            
                样例3：
                ||
            
                输出内容为债务重组企业的企业名称，每个债务重组企业使用|分割，
                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """
    
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        debt_restructuring_dict = {}
        for i,row in enumerate(rows):
            columns = row.strip('|').split('|')
            if i == 0:
                for keyword in filter_keywords:
                    for column in columns:
                        # 过滤关键词
                        if keyword in column:
                            columns = []
                            break
                debt_restructuring_dict["债务重组"] = columns
                debt_restructuring_dict["is_keyword_hit"] = is_hit
        return debt_restructuring_dict

class AnnualReportService(object):
    @staticmethod
    def common_build_ext(company_name, pdf_filename, year, industry_chain,collection,mongodb_id="",is_cached=True,):
        # 开始存储数据
        # MongodbUtil.connect()
        if is_cached:
            result = MongodbUtil.query_docs_by_condition(CollectionConfig.ANNUAL_REPORT_HISTORY,
                                                        {"company_name": company_name, "year": year})
            result = list(result)
            if len(result) > 0:
                print("has data")
                response = result[0]
                return response
        # 暂时实例化一些类
        milvus_util = MilvusUtil()
        embutil = TextEmbedService()
        com_analysis = AnnualReportCompanyAnalysis(milvus_util, embutil,collection_name=collection)
        # 母公司
        parentand_result = com_analysis.company_parentand(company_name, pdf_filename)

        # 子公司
        subsidiary_result = com_analysis.company_subsidiary(company_name, pdf_filename)
        
        # 合营联营企业
        cooperative_enterprise_result = com_analysis.company_cooperative_enterprise(company_name, pdf_filename)

        # 其他关联方企业
        other_related_result = com_analysis.company_other_related(company_name, pdf_filename)

        # 采购商品或接受劳务
        purchase_goods_result = com_analysis.company_purchase_goods(company_name, pdf_filename)

        # 出售商品或提供劳务
        sell_goods_result = com_analysis.company_sell_goods(company_name, pdf_filename)

        # 租赁或者出租方
        lessor_result = com_analysis.company_lessor(company_name, pdf_filename)

        # 担保或者担保方
        guarantee_result = com_analysis.company_guarantee(company_name, pdf_filename)

        # 被担保或者被担保方
        guaranteed_result = com_analysis.company_guaranteed(company_name, pdf_filename)

        # 资金拆借的资金拆入
        funds_borrowed_result = com_analysis.company_funds_borrowed(company_name, pdf_filename)

        # 资金拆借的资金拆出
        funds_withdrawal_result = com_analysis.company_funds_withdrawal(company_name, pdf_filename)

        # 资产转让
        asset_transfer_result = com_analysis.company_asset_transfer(company_name, pdf_filename)

        # 债务重组
        debt_restructuring_result = com_analysis.company_debt_restructuring(company_name, pdf_filename)

        results_list = []
        results_list.append(parentand_result)
        results_list.append(subsidiary_result)
        results_list.append(cooperative_enterprise_result)
        results_list.append(other_related_result)
        results_list.append(purchase_goods_result)
        results_list.append(sell_goods_result)
        results_list.append(lessor_result)
        results_list.append(guarantee_result)
        results_list.append(guaranteed_result)
        results_list.append(funds_borrowed_result)
        results_list.append(funds_withdrawal_result)
        results_list.append(asset_transfer_result)
        results_list.append(debt_restructuring_result)
        for result in results_list:
            for key, value in result.items():
                if key == "is_keyword_hit":
                    continue
                # print(key,value)
                # value = common_clean_process(company_list=value,is_filter=False)
                result[key] = value
        # print("results_list",results_list)
        if mongodb_id == "":
            search_condition = {'source_type': {'$regex': "年报"},
                                'title': {"$regex": ".*" + re.escape(pdf_filename) + ".*", "$options": "i"}}
            
            id_list = []
            doc_list = MongodbUtil.query_docs_by_condition(collection_name=CollectionConfig.NOTICE_ALL,
                                                        search_condition=search_condition)
            for item in doc_list: 
                id_list.append(item["_id"])
            mongodb_id = id_list[0]

        doc_info = MongodbUtil.query_doc_by_id(collection_name=CollectionConfig.NOTICE_ALL, doc_id=mongodb_id)

        # 公司名称
        company_name = company_name

        # 年报url
        if "source_url" in doc_info :
            file_url = doc_info['source_url']
        # 2025  0520 source_notice  变更为 notice_label_info
        elif "file" in doc_info:
            file_url = doc_info["file"]["file_url"]
        else:
            file_url = doc_info["file"]["file_url"]
            print(doc_info)
        # 年报文件发布时间
        document_path=""
        if "document_path" in doc_info:
            document_path = doc_info['document_path']
        elif "minio" in doc_info:
            document_path = doc_info['minio']["minio_document_path"]
        parts = document_path.split('/')
        file_time_list = parts[2:5]
        ful_file_time_list = []
        for i in file_time_list:
            if len(i) == 1:
                ful_i = "0" + i
                ful_file_time_list.append(ful_i)
            else:
                ful_file_time_list.append(i)
        file_time = '-'.join(ful_file_time_list)

        # 文件mongodbid
        source_id = mongodb_id

        final_list = []

        # 结构化
        for data_dict in results_list:
            is_hit = data_dict["is_keyword_hit"]
            for classes, company_list in data_dict.items():
                if classes == 'is_keyword_hit':
                    # 这里是判断是否keyword命中
                    is_hit = company_list
                    continue
                for i, value in enumerate(company_list):
                    # print(company_list)
                    # 存在简称，如比亚迪
                    # if len(value)>=4:
                    result_dict = {
                        "industry_chain": industry_chain,
                        "links": "",
                        "company_name": company_name,
                        "released_time": file_time,
                        "source_id": source_id,
                        "affiliate": value,
                        "relation_type": classes,
                        "affiliate_register_address": "",
                        "affiliate_business_address": "",
                        "year": "",
                        "ranking": "",
                        "product": "",
                        "notes": "",
                        "is_keyword_hit": is_hit
                    }
                    final_list.append(result_dict)
        save_data = {"公告url": file_url, "公司数量": len(final_list), "年报关联公司汇总": results_list,
                     "待入库具体信息": final_list, "company_name": company_name, "file_time": file_time,
                     "source_id": source_id}
        save_data["_id"] = uuid.uuid1().hex
        save_data["year"] = year
        result = MongodbUtil.query_docs_by_condition(CollectionConfig.ANNUAL_REPORT_HISTORY,
                                                     {"company_name": company_name, "year": year})
        if len(list(result)) > 0:
            print("has data")
            # print(len(list(result)))
        else:
            result = MongodbUtil.insert_one(collection_name=CollectionConfig.ANNUAL_REPORT_HISTORY,
                                            doc_content=save_data)
        # result = MongodbUtil.insert_one(collection_name=CollectionConfig.ANNUAL_REPORT_HISTORY, doc_content=save_data)

        # print(result)
        response = save_data
        return response
    
if __name__ =='__main__':
    pdf_name_to_filename = {
            "中国石油": "中国石油天然气股份有限公司2023年度报告",
            "南钢股份":"南钢股份-南京钢铁股份有限公司2023年年度报告",
            "天奇股份":"天奇股份-2023年年度报告",
            "中国石化":"中国石化2023年年度报告"
        }
   
    # company_name = "南钢股份"
    # company_name = "天奇股份"
    # company_name = "中国石油"
    # company_name = "国风新材"
    # pdf_filename = "国风新材-2022年年度报告"
    company_name = "江特电机"
    pdf_filename = "江特电机-2019年年度报告全文（更正后）"
    milvus_util = MilvusUtil()
    embutil = TextEmbedService()
    com_analysis = AnnualReportCompanyAnalysis(milvus_util,embutil)

    # for name in pdf_name_to_filename:
    #     # filename = pdf_name_to_filename[name]
    #     # result = com_analysis.get_annualreport_data(name,filename)
    #     print("testing =============")
    #     print(name)
    #     filename = pdf_name_to_filename[name]
    #     print(filename)
    #     parentand_result = com_analysis.company_lessor(name)
    #     print(parentand_result)
    
    # # 母公司
    # parentand_result = com_analysis.company_parentand(company_name)
    # print(parentand_result)
    
    # # 子公司
    subsidiary_result = com_analysis.company_subsidiary(company_name,pdf_filename)
    print(subsidiary_result)
    
    # # 合营联营企业
    # cooperative_enterprise_result = com_analysis.company_cooperative_enterprise(company_name)
    # print(cooperative_enterprise_result)
    
    # # 其他关联方企业
    # other_related_result = com_analysis.company_other_related(company_name)
    # print(other_related_result)
    
    # 采购商品或接受劳务
    # purchase_goods_result = com_analysis.company_purchase_goods(company_name,pdf_filename)
    # print(purchase_goods_result)
    
    # 出售商品或提供劳务
    # sell_goods_result = com_analysis.company_sell_goods(company_name,pdf_filename)
    # print(sell_goods_result)
    
    # # 租赁或者出租方
    # lessor_result = com_analysis.company_lessor(company_name)
    # print(lessor_result)
    
    # # 担保或者担保方
    # guarantee_result = com_analysis.company_guarantee(company_name)
    # print(guarantee_result)
    
    # # 被担保或者被担保方
    # guaranteed_result = com_analysis.company_guaranteed(company_name)
    # print(guaranteed_result)
    
    # # 资金拆借的资金拆入
    # funds_borrowed_result = com_analysis.company_funds_borrowed(company_name)
    # print(funds_borrowed_result)
    
    # # 资金拆借的资金拆出
    # funds_withdrawal_result = com_analysis.company_funds_withdrawal(company_name)
    # print(funds_withdrawal_result)

    # # 资产转让
    # asset_transfer_result = com_analysis.company_asset_transfer(company_name)
    # print(asset_transfer_result)
    
    # # 债务重组
    # debt_restructuring_result = com_analysis.company_debt_restructuring(company_name)
    # print(debt_restructuring_result)
    