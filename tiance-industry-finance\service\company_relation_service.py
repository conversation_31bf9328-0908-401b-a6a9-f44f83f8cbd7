from utils.text_embed_util import TextEmbedService
from utils.milvus_util import MilvusUtil
from utils.llm_model_util import Llm_Service,MessageConverter,UserMessage
from configs.model_config import ModelConfig
from utils.reranker_util import QueryReranker
from configs.collection_config import CollectionConfig
reranker = QueryReranker()


class CompanyAnalysis():
    def __init__(self, milvus_client, text_embedding_service):
        self.milvus_util = milvus_client
        self.embutil = text_embedding_service

        self.llm_service = Llm_Service()
        # self.pdf_name_to_filename = {
        #     "机器人": "机器人-首次公开发行股票并在创业板上市招股说明书",
        #     "东方国信": "东方国信-首次公开发行股票并在创业板上市招股说明书",
        #     "三环集团": "三环集团-首次公开发行股票并在创业板上市招股说明书",
        #     "晶瑞股份": "晶瑞股份-首次公开发行股票并在创业板上市招股说明书",
        #     "中大力德": "中大力德-首次公开发行股票招股说明书",
        #     "先导股份": "先导股份-首次公开发行股票并在创业板上市招股说明书"
        # }
    def common_rerank(self,docs,query):
        if 'entity' in docs[0]:
            chunk_contents = [doc['entity']['chunk_content_father'].replace('|',' ').replace('-',' ') for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]
            chunk_contents_son = [doc['entity']['chunk_content_son'].replace('|',' ').replace('-',' ') for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]

        else:
            chunk_contents = [doc['chunk_content_father'].replace('|',' ').replace('-',' ') for doc in docs ]
            chunk_contents_son = [doc['chunk_content_son'].replace('|',' ').replace('-',' ') for doc in docs ]
        # print("--------重排序----------")
        # print("\n-----data------\n".join(chunk_contents_son))
        # 重排
        if len(chunk_contents_son) >0:
            reranked_results = reranker.rerank_query(query,chunk_contents_son)
            # 获取重排序结果中的 top-k 索引
            top_k = 5  # 获取排名前 5 的文档
            top_k_indices = reranker.get_top_x_indices(reranked_results, top_k)
            # 根据索引获取 top-k 的文档内容
            chunk_contents = reranker.get_top_x_corpus(chunk_contents, top_k_indices)
            # 根据索引获取 top-k 的文档内容
            chunk_contents_info = reranker.get_top_x_corpus(docs, top_k_indices)
        else:
            chunk_contents_info=[]

        # print("--------重排序----------")
        # print("\n-----data 后------\n".join(chunk_contents))
        return chunk_contents, chunk_contents_info

    def common_rag(self,query,pdf_filename,keywords):
        queryemb = self.embutil.text_embedding([query])
        filter = f"file_title == '{pdf_filename}'"
        K = 20
        docs = self.milvus_util.search_by_vector(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS, vector=queryemb, limit=K,filter = filter)[0]
        if len(docs)>0:
            chunk_contents,chunk_contents_info = self.common_rerank(docs,query)
        else:
            chunk_contents = []
            chunk_contents_info = []

        filter_keywords = []
        for keyword in keywords:
            filter_keywords.append(f"chunk_content_son like \"%{keyword}%\" ")

        filter_keyword = "||".join(filter_keywords)
        # keyword
        docs2 = self.milvus_util.search_by_filter(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS, filter =f"file_title == '{pdf_filename}'" +f"&& ({filter_keyword})")
        # print(docs2)
        chunk_contents2=[]
        chunk_contents_info2=[]
        if len(docs2)>0:
            chunk_contents2,chunk_contents_info2 = self.common_rerank(docs2,query)
        result_test = []
        chunk_contents_info2 = chunk_contents_info2[:1]
        is_hit = False

        for i, content in enumerate(chunk_contents_info2):
            is_hit = True
            result_test.append(f"\n------------------第{i+1}份数据------------------\n"+content['chunk_content_father'].replace('|',' ').replace('-',' '))
            page_num= int(content["chunk_type"].split(':')[-1])
            print(f"keyword2 hit in {page_num}")
            for j in range(1,3):
                filter = f"chunk_type == 'page: {page_num+j}' && file_title == '{pdf_filename}'"
                pages = self.milvus_util.search_by_filter(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS, filter = filter)
                if len(pages) >0:
                    pages = pages[0]
                else:
                    break
                if pages:
                    # # print(pages)
                    # print("keyword hit")
                    # print(pages['chunk_content_father'].replace('|',' ').replace('-',' ') )
                    result_test.append(pages['chunk_content_father'].replace('|',' ').replace('-',' ') )
            # break
        documents = ""
        content_list = []
        if len(result_test) >0:
        #     chunk_contents = result_test
            print("hit replace vector search by keyword")
            content_list = result_test
            documents = "".join(result_test)
        else :
            for i, content in enumerate(chunk_contents):
                    documents += f"\n------------------第{i+1}份数据------------------\n{content}"

                    content_list.append(content)
        return documents,is_hit

    def company_supplier(self,company_name,pdf_filename):
        query = f"{company_name}公司的前 名供应商"
        queryemb = self.embutil.text_embedding([query])
        # pdf_filename = self.pdf_name_to_filename.get(company_name)

        # filter = f"file_title == '{pdf_filename}'"
        # K = 5
        # docs = self.milvus_util.search_by_vector(collection_name="industry_finance_prospectus", vector=queryemb, limit=K,filter = filter)[0]
        # chunk_contents = [doc['entity']['chunk_content'] for doc in docs if 'entity' in doc and 'chunk_content' in doc['entity']]

        # documents = ""
        # content_list = []
        # for i, content in enumerate(chunk_contents):
        #         documents += f"\n------------------第{i+1}份数据------------------\n{content}"
        #         content_list.append(content)
        keywords=["前%名供应商"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)

        print("----------供应商信息----------")
        # print(documents)

        question = '你需要从这份数据中，提取公司的历年的主要供应商，并排序输出'

        for_llm= f"""
                你是一名优异的招股说明书内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                需要输出所有主要供应商
                输出的供应商名字需要为公司，并且使用数据中原有的字段
                优先从{keywords}中抽取

                抽取数据：{documents}
                需求：{question}

                输出样例如下 使用|分割各个公司，如果没有，则输出||
                |2020年|供应商A|供应商B|供应商C|供应商D|供应商E|
                |2021年|供应商A|供应商B|供应商C|供应商F|供应商D|
                |2022年1-6月|供应商G|供应商C|供应商B|供应商F|供应商E|供应商A|供应商B|供应商C|供应商F|供应商D|

                样例2（没有内容）
                ||


                必须严格按照输出样例格式输出，严禁输出任何其他内容，没有内容则输出||。
                """

        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')

        supplier_dict = {}
        for row in rows:
            columns = row.strip('|').split('|')
            year = columns[0]
            if year =="":
                continue
            
            suppliers = columns[1:]
            # print(suppliers)
            if "供应商" in suppliers:
                print("failed to find keyword")
                continue 
            is_null = False
            for supplier in suppliers:
                if "供应商" in supplier:
                    is_null = True
                    break
            if is_null == True:
                print("failed to find keyword")
                continue
            supplier_dict[year] = suppliers
        supplier_dict["is_keyword_hit"] = is_hit
        # print("----------供应商信息----------")
        # print(supplier_dict)
        return supplier_dict

 
    def company_same_industry(self,company_name,pdf_filename):

        query = f"{company_name}公司的同行业上市公司名单"
        queryemb = self.embutil.text_embedding([query])
        # pdf_filename = self.pdf_name_to_filename.get(company_name)
        
        keywords=["同行业上市公司", "同行业企业", "同行业相关企业"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)

        # filter = f"file_title == '{pdf_filename}'"
        # # filter = "chunk_type == 'page:1'"

        # K = 5
        # # docs = self.milvus_util.search_by_vector(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS, vector=queryemb, limit=K,filter = filter)[0]

        # # chunk_contents = [doc['entity']['chunk_content_father'] for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]

        # # documents = ""
        # # content_list = []
        # # for i, content in enumerate(chunk_contents):
        # #         documents += f"\n------------------第{i+1}份数据------------------\n{content}"
        # #         content_list.append(content)
        # # print(documents)

        # # 使用正则匹配来查询

        # docs = self.milvus_util.search_by_filter(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS, filter=filter)
        # # print(len(docs))
        # all_page = []
        # page_num = ""
        # for page in docs:
        #     if page_num != page['chunk_type']:
        #         # print(page_num)
        #         all_page.append(page["chunk_content_father"])
        #         page_num = page['chunk_type']


        # # print(len(all_page))

        # keywords = ["同行业上市公司", "同行业企业", "同行业相关企业"]

        # filtered_list = [s for s in all_page if any(keyword in s for keyword in keywords)]
        # # print(len(filtered_list))
        # # 检查过滤后的列表长度
        # if len(filtered_list) < 2*K:
        #     # 如果长度小于10，将所有元素拼接成字符串
        #     documents = '\n\n----------数据----------\n\n'.join(filtered_list)
        # else:
        #     # 如果长度大于或等于10，将前10个元素拼接成字符串
        #     documents = '\n\n----------数据----------\n\n'.join(filtered_list[:10])

        # print(documents)

        question = '你需要从这份数据中，提取提到的同行业上市公司'
        for_llm= f"""
                你是一名优异的招股说明书内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从{keywords}中抽取
                仅抽取同行业公司名称

                数据：{documents}
                需求：{question}

                输出样例如下，使用|分割各个公司，如果没有同行业公司，则输出||
                样例1:
                |同行业公司A|同行业公司B|
                样例2:
                |同行业公司a|同行业公司b|同行业公司c|同行业公司d|
                样例3:
                ||


                必须严格按照输出样例格式输出，严禁输出任何其他内容。
                """

        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)
        same_industry_list = response.strip('|').split('|')

        # print("----------同行业上市公司----------")
        # print(same_industry_list)
        # is_hit
            
        return {"same_industry_list":same_industry_list,"is_keyword_hit":is_hit}




    def company_client(self,company_name,pdf_filename):

        # query = f"{company_name}公司报告期各期前五名销售客户的名称、销售金额及占主营业务收入的比例有哪些"
        query = f"{company_name}公司报告期 主要销售客户的名称"
        queryemb = self.embutil.text_embedding([query])
        # pdf_filename = self.pdf_name_to_filename.get(company_name)

        keywords=["前%客户"]
        documents,is_hit = self.common_rag(query=query,pdf_filename=pdf_filename,keywords=keywords)


        # filter = f"file_title == '{pdf_filename}'"
        # K = 5
        # docs = self.milvus_util.search_by_vector(collection_name=CollectionConfig.PROSPECTUS_REPORT_MILVUS, vector=queryemb, limit=K,filter = filter)[0]
        # chunk_contents = [doc['entity']['chunk_content_father'] for doc in docs if 'entity' in doc and 'chunk_content_father' in doc['entity']]

        # documents = ""
        # content_list = []
        # for i, content in enumerate(chunk_contents):
        #         documents += f"\n------------------第{i+1}份数据------------------\n{content}"
        #         content_list.append(content)\

        # print(documents)

        question = '你需要从这些数据中，提取公司的报告期的所有主要客户情况，并排序输出'

        for_llm= f"""
                你是一名优异的招股说明书内容分析大师，擅长分析文本信息来完成相关需求。
                现在，你需要根据相关的数据，完成需求。
                优先从{keywords}中抽取
                抽取主要的销售客户，客户为公司，如果有公司全称，抽取公司全称
                如果存在子公司，则输出子公司

                抽取数据：{documents}
                需求：{question}

                输出样例如下 如果没有客户，则输出 || :
                |2020年|客户A|客户B|客户C|客户D|客户E|客户G|客户H|客户I|客户F|客户E|
                |2021年|客户A|客户B|客户C|客户F|客户D|客户F|客户E|
                |2022年1-6月|客户G|客户C|客户B|客户F|客户E|

                样例2（没有内容）：
                ||

                必须严格按照输出样例格式输出,不要输出样例，严禁输出任何其他内容。,如果没有内容或者没有客户，则输出为||
                """
        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)

        rows = response.strip().split('\n')
        client_dict = {}

        for row in rows:
            columns = row.strip('|').split('|')
            year = columns[0]
            if year == "":
                continue
            clients = columns[1:]
            if "客户" in clients:
                print("failed to find keyword")
                continue 
            is_null = False
            for client in clients:
                if "客户" in client:
                    is_null = True
                    break
            if is_null == True:
                print("failed to find keyword")
                continue
            client_dict[year] = clients
        # print("----------客户信息----------")
        # print(client_dict)
        client_dict["is_keyword_hit"] =is_hit
        return client_dict



if __name__ =='__main__':

    pdf_name_to_filename = {
            "机器人": "机器人-首次公开发行股票并在创业板上市招股说明书",# 没大问题
            "东方国信": "东方国信-首次公开发行股票并在创业板上市招股说明书",# 客户有问题，显示的是欠钱客户
            "三环集团": "三环集团-首次公开发行股票并在创业板上市招股说明书",# 客户略微带点问题（跨页表格 只索引了一部分）
            "晶瑞股份": "晶瑞股份-首次公开发行股票并在创业板上市招股说明书",# 客户有问题，显示的是欠钱客户
            "中大力德": "中大力德-首次公开发行股票招股说明书",# 客户和同行业问题都较大
            "先导股份": "先导股份-首次公开发行股票并在创业板上市招股说明书" # 没大问题
        }
    # company_name = "机器人"
    # company_name ="东方国信"
    # company_name = "晶瑞股份"
    # company_name = "三环集团"
    # company_name = "中大力德"
    company_name = "赣锋锂业"
    pdf_filename ="赣锋锂业-首次公开发行股票招股说明书"
    milvus_util = MilvusUtil()
    embutil = TextEmbedService()
    com_analysis = CompanyAnalysis(milvus_util,embutil)

    # for key in pdf_name_to_filename:
    #     print(key)
    #     supplier_result = com_analysis.company_supplier(key)
    #     print("--------------客户分析情况------------------")
    #     print(supplier_result)


    # 供应商分析情况
    supplier_result = com_analysis.company_supplier(company_name,pdf_filename)
    print("--------------供应商分析情况------------------")
    print(supplier_result)
# 
    # 同行业分析情况
    # print(company_name)
    # same_industry_result = com_analysis.company_same_industry(company_name)
    # print("--------------同行业分析情况2-----------------")
    # print(same_industry_result)

    # # 客户分析情况
    # client_result = com_analysis.company_client(company_name)
    # print("--------------客户分析情况-----------------")
    # print(client_result)

