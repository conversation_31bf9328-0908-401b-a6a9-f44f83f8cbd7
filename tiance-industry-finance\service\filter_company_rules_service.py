﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/03/11 15:03:34
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   filter_company_rules_service.py
@Project    :   tiance-industry-finance
'''

import re

keywords = ["股东控制","联营企业"]

# 正则表达式：匹配只包含中文字符且长度 ≤ 7 的字符串或不全是中文字符的片段
rule1 = r'^[\u4e00-\u9fff]{0,7}$'
# 正则表达式：匹配不包含“公司”的字符串
rule2 = r'^(?!.*公司).*$'
rule3 = r'.*[a-zA-Z1-9@].*'             # 包含英文字符数字，@
rule4 = r'^(.*子公司).*$'
# 包含关键词
# 对关键词进行转义，以确保特殊字符不会干扰正则表达式
escaped_keywords = [re.escape(keyword) for keyword in keywords]
# 使用管道符号（|）连接所有关键词，表示“或”的关系
rule5 = '|'.join(escaped_keywords)
# print(rule5)

# 定义需要去除的特殊字符数组
special_chars = [' ', '@', '#', '$', '%', '^', '&', '*',' ']

# 将特殊字符数组转换为正则表达式模式
sepcial_token_pattern = '[' + re.escape(''.join(special_chars)) + ']'




def company_output_rules_filter_processor(company_name:str) -> bool:
    if (re.match(rule1,company_name) 
    or re.match(rule2,company_name) 
    or re.match(rule3,company_name) 
    or re.match(rule4,company_name) 
    or re.search(rule5,company_name)):
        return True
    else:
        return False


def remove_unknown_symbol(company_name:str) -> str:
    company_name = re.sub(sepcial_token_pattern, '', company_name)
    #替换为中文符号
    company_name = company_name.replace('(','（').replace(')','）')
    return company_name

def common_clean_process(company_list:list[str],is_filter=True) -> list[str]:
    result = []
    for company in company_list:
        company = remove_unknown_symbol(company)
        if is_filter and company_output_rules_filter_processor(company):
            continue
        result.append(company)
    if len(result) == 0:
        return ['']
    return result
# # 示例
# test_strings = ["张三", "李四", "中国", "山西尚太新材料研究开发有限公司", "123", "张三丰", "张三@公司"]
# filtered_strings = [s for s in test_strings if re.match(pattern3, s)]
# print(filtered_strings)  # 输出：['张三', '李四', '中国', '张三丰']




# # 示例
# test_strings = ["张三", "李四公司", "中国", "山西尚太新材料研究开发有限投资", "张三丰", "张三@公司"]
# filtered_strings = [s for s in test_strings if re.match(pattern2, s)]
# print(filtered_strings)  # 输出：['张三', '中国', '张三丰']

if __name__ =="__main__":
    test_strings = ["Chenqi Technology Limited","洛阳富川 矿业有限 公司","洛阳富川 矿业有限 子公司","华越镍钴 （印尼） 有限公司","大东坡"," 洛阳钼业","华越镍钴 （印尼） 有限公司","控股股东控制的公司","联营企业和合营企业"]
    test_strings_clean = [remove_unknown_symbol(s)  for s in test_strings if not company_output_rules_filter_processor(remove_unknown_symbol(s)) ]
    print(test_strings_clean)
    test_strings_clean2 = common_clean_process(test_strings)
    print(test_strings_clean2)
