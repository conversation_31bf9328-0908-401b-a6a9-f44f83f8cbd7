#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :kb_service.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 15:43:33
'''

from utils.milvus_util import MilvusUtil
from configs.kb_config import KbConfig
from service.text_embed_service import TextEmbedService

class KbService(object):
    """
    知识库服务引擎
    """

    def __init__(self):
        """
        初始化
        """
        # 向量数据库工具类
        self.milvus_util = MilvusUtil()
        # 文本嵌入服务
        self.text_embed_service = TextEmbedService()

    async def search_knowledge_by_question(self, collection_name: str, question: str, limit_top_k: int, expr: str = ""):
        """
        根据用户问题查询知识
        :param collection_name: 知识库名称
        :param question: 问题
        :param limit_top_k: 只返回最相似的 k 块文档
        :param expr: 过滤条件表达式
        :return:
        """
        # 文档列表
        doc_list = list()

        # 知识库是否存在
        is_exist = self.milvus_util.collection_is_exists(collection_name)
        if not is_exist:
            return doc_list

        question = "为这个句子生成表示以用于检索相关文章：" + question
        # 问题嵌入
        embed_vector = await self.text_embed_service.text_embedding([question])

        # 检索文档
        ret = self.milvus_util.search_by_vector(collection_name=collection_name, vector=embed_vector,
                                                limit=limit_top_k,
                                                expr=expr,
                                                search_params=KbConfig.MILVUS_SEARCH_PARAMS)
        for item in ret[0]:
            entity = item["entity"]
            doc_list.append(entity)
        return doc_list
