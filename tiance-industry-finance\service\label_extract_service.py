#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/4/16 10:18
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : label_extract_utils.py
# @Project : tiance-industry-finance
"""
import ast
import asyncio
import copy
import pickle
import re
from typing import List, Dict, Any

import pandas as pd

from api.routes.get_fullname_companies import get_fullname_companies
from api.routes.search_key_companies_compose import search_key_companies_compose
from configs.collection_config import CollectionConfig
from configs.run_config import RunConfig
from entity.request_entity import SearchKeyCompaniesComposeRequest, GetFullnameCompaniesRequest
from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil
from utils.tree_utils import TreeUtils


def extract_industries_from_prompt(prompt_str: str) -> List[str] | None:
    """
    从提示词中提取出产业链名称列表
    :param prompt_str: 示例：你是一个专业的$["工业机器人","新能源汽车","新材料","低空经济"]$产业链抽取助手...
    :return: ["工业机器人","新能源汽车","新材料","低空经济"]
    """
    match = re.match(r".*\$(\[.*])\$.*", prompt_str, re.DOTALL)
    if match:
        try:
            industry_list = ast.literal_eval(match.group(1))
            # TODO 去除列表元素中产业链名称两端不可见字符
            return industry_list
        except Exception as e:
            detail = f"失败详情：{str(e)}"
            LogUtil.error(msg=detail)
            LogUtil.info(f"产业链名称列表匹配异常，返回None。prompt: {prompt_str}")
            return None
    else:
        LogUtil.info(f"未能从提示词中匹配到产业链名称列表，返回None。prompt: {prompt_str}")
        return None


def valid_industries(industry_list: List[str]) -> bool:
    """
    验证industry_list中的产业链是已在MongoDB中存在对应的产业链结构
    :param industry_list:
    :return:
    """

    for industry_name in industry_list:
        query_res_iter = MongodbUtil.query_docs_by_condition(
            CollectionConfig.CHAIN_STRUCTURE,
            {
                "industry": industry_name
            }
        )
        try:
            # 尝试获取第一个元素
            next(query_res_iter)
        except StopIteration:  # 迭代器为空
            LogUtil.error(f"MongoDB不存在该产业链结构。industry_name: {industry_name}")
            return False
    return True


MAP_DOCTYPE_TO_COLLECTION_NAME = {
    "research_report": CollectionConfig.RESEARCH_REPORT_LABEL_INFO,
    "invoice": CollectionConfig.INVOICE_LABEL_INFO,
    "customs": CollectionConfig.CUSTOMS_LABEL_INFO,
    "credit_report": CollectionConfig.CREDIT_REPORT_LABEL_INFO,
}


def convert_docIdAndType_to_collectionNameAndIds(
    doc_id_and_type_list: List[Dict[str, str]]
) -> Dict[str, List[str]]:
    """
    数据转换
    :param doc_id_and_type_list: 示例：
[
  {
    "doc_type": "research_report",
    "mongodb_id": "b6abd927a86d40b891f10ade0fd130c0"
  },
  {
    "doc_type": "research_report",
    "mongodb_id": "fa1c26252598433b8ddbc157f03edbdb"
  }
]
    :return: 示例：
{
  "collection_names": [
    "research_report_info",
    "research_report_info"
  ],
  "mongodb_ids": [
    "b6abd927a86d40b891f10ade0fd130c0",
    "fa1c26252598433b8ddbc157f03edbdb"
  ]
}
    """
    collection_names_and_ids_dict = {
        "collection_names": [],
        "mongodb_ids": []
    }
    for doc_id_and_type in doc_id_and_type_list:
        collection_name = MAP_DOCTYPE_TO_COLLECTION_NAME[doc_id_and_type["doc_type"]]
        collection_names_and_ids_dict["collection_names"].append(collection_name)
        collection_names_and_ids_dict["mongodb_ids"].append(doc_id_and_type["mongodb_id"])

    return collection_names_and_ids_dict


async def extract_raw_label_data_single_industry(
    industry: str,
    collection_names: List[str],
    mongodb_ids: List[str],
    model: str = RunConfig.MAX_LLM_MODEL_NAME,
    k: int = 5,
    is_use_cache: bool = False
):
    try:
        key_result = None
        LogUtil.info("2.获取中心客群名单")
        # mongodb_ids = chain_result.get("mongodb_ids") # TODO 把产业链结构中的的文档换成了用户传入的文档
        node_info = await asyncio.gather(search_key_companies_compose(
            SearchKeyCompaniesComposeRequest(
                model=model,
                k=k,
                collection_names=collection_names,
                mongodb_ids=mongodb_ids,
                industry=industry,
                chain_structure=None,  #
                epochs=1
            ),
            remove_duplicates_company_in_same_node=False,
            thread_num=32,
            is_use_cache=is_use_cache
        ))
        node_info = node_info[0]

        if node_info.code == 200:
            node_companies = node_info.data.get("result")
            # 溯源
            mongdb_id_companies = node_info.data.get("mongdb_id_companies")
            nodes_sources_dict = node_info.data.get("nodes_sources_dict")
            chain_structure_json = node_info.data.get("chain_structure_json")

            LogUtil.info("3.获取中心客群全称")
            key_info = await asyncio.gather(get_fullname_companies(
                GetFullnameCompaniesRequest(
                    industry=industry,
                    node_companies=node_companies
                ),
                is_use_cache=is_use_cache
            ))
            key_info = key_info[0]

            if key_info.code == 200:
                LogUtil.info("4.中心客群数据组装")
                new_node_companies = key_info.data.get("result")  # 有产业链结构的中心客群
                key_companies = key_info.data.get("all_key_companies")  # 无产业链结构的中心客群列表
                # filter_companies = key_info.data.get("filter_companies")  # 被过滤的中心客群
                key_result = {
                    "industry": industry,
                    "node_companies": new_node_companies,
                    "key_companies": key_companies,
                    # "filter_companies": filter_companies,
                    "mongdb_id_companies": mongdb_id_companies,
                    "nodes_sources_dict": nodes_sources_dict,
                    "chain_structure_json": chain_structure_json
                }

        LogUtil.info(f"key_result: {key_result}")
        return key_result

    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        return None


def merge_multi_industries_raw_data(
    raw_data_multi_industries: List[Dict[str, Any]]
) -> Dict[str, Any]:
    """
    合并多条产业链提取出来的标签原始数据
    :param raw_data_multi_industries:
    :return:
    """

    # with open('raw_data_multi_industries.pickle', 'rb') as f:
    #     raw_data_multi_industries = pickle.load(f)

    merged_multi_industries_raw_data = {
        "industry": [],
        "node_companies": {},
        "key_companies": [],
        "mongdb_id_companies": {},
        "nodes_sources_dict": {},
        "chain_structure_json": {}
    }

    for raw_data_single_industry in raw_data_multi_industries:
        merged_multi_industries_raw_data["industry"].append(raw_data_single_industry["industry"])
        merged_multi_industries_raw_data["node_companies"].update(raw_data_single_industry["node_companies"])
        merged_multi_industries_raw_data["key_companies"].extend(raw_data_single_industry["key_companies"])
        merged_multi_industries_raw_data["nodes_sources_dict"].update(raw_data_single_industry["nodes_sources_dict"])
        merged_multi_industries_raw_data["chain_structure_json"].update(
            raw_data_single_industry["chain_structure_json"])

        # mongdb_id_companies 为追加合并，不能update，否则会直接覆盖
        for key in raw_data_single_industry["mongdb_id_companies"]:
            if key in merged_multi_industries_raw_data["mongdb_id_companies"]:  # 有则追加到对应数组
                if isinstance(merged_multi_industries_raw_data["mongdb_id_companies"][key], list) and isinstance(
                    raw_data_single_industry["mongdb_id_companies"][key], list):
                    # 如果两个键对应的值都是列表，追加合并
                    merged_multi_industries_raw_data["mongdb_id_companies"][key].extend(
                        raw_data_single_industry["mongdb_id_companies"][key])
                else:
                    raise ValueError(f"不同产业链的原始数据合并'mongdb_id_companies'字段时异常。\n"
                                     f"merged_multi_industries_raw_data['mongdb_id_companies']: {merged_multi_industries_raw_data['mongdb_id_companies']}\n"
                                     f"raw_data_single_industry['mongdb_id_companies'][key]: {raw_data_single_industry['mongdb_id_companies'][key]}")

            else:  # 没有则直接赋值数组
                merged_multi_industries_raw_data["mongdb_id_companies"][key] = \
                    copy.deepcopy(raw_data_single_industry["mongdb_id_companies"][key])  # 拷贝，防止修改到原始数据

    # 修改'mongdb_id_companies'键为'mongodb_id_companies'，以兼容后续流程
    old_key = 'mongdb_id_companies'
    new_key = 'mongodb_id_companies'
    # 删除旧键值对
    value = merged_multi_industries_raw_data.pop(old_key, None)  # 使用 pop 方法删除旧键并获取值
    # 添加新键值对
    merged_multi_industries_raw_data[new_key] = value

    return merged_multi_industries_raw_data


def get_company_abb_to_mongo_ids(
    label_raw_data: Dict[str, Any]
) -> Dict[str, List[str]]:
    """
    从标签提取原始数据中获取企业简称到 MongoDB id的映射，
    可能对应多个MongoDB id，所以value为list
    :param label_raw_data: 标签提取原始数据
    :return:
    """
    mongo_ids_of_company_abb = {}
    mongodb_id_companies = label_raw_data["mongodb_id_companies"]
    for mongodb_id in mongodb_id_companies:
        company_list = mongodb_id_companies[mongodb_id]
        for company in company_list:
            if mongo_ids_of_company_abb.get(company) is None:
                mongo_ids_of_company_abb[company] = []
            mongo_ids_of_company_abb[company].append(mongodb_id)
    return mongo_ids_of_company_abb


def get_mongodb_id_node(
    label_raw_data: Dict[str, Any]
) -> Dict[str, List[str]]:
    """
    :param label_raw_data: 标签提取原始数据
    :return:
    """
    mongodb_id_node = {}
    mongo_ids_of_company_abb = get_company_abb_to_mongo_ids(label_raw_data)
    node_companies = label_raw_data["node_companies"]
    for industry_node in node_companies:
        for company_info in node_companies[industry_node]:
            company_abb = company_info.get("abb")
            source_mongo_id_list = mongo_ids_of_company_abb.get(company_abb)  # 一个公司可能被多个研报提到

            # 填入结果
            for source_mongo_id in source_mongo_id_list:
                if mongodb_id_node.get(source_mongo_id) is None:
                    mongodb_id_node[source_mongo_id] = set()  # 使用集合去重
                mongodb_id_node[source_mongo_id].add(industry_node)

    # 产业链节点以字典序排序
    for source_mongo_id in mongodb_id_node:
        mongodb_id_node[source_mongo_id] = sorted(list(mongodb_id_node[source_mongo_id]))

    return mongodb_id_node


def get_mongodb_id_info(
    collection_names_and_ids_dict: Dict[str, List[str]]
) -> Dict[str, Dict[str, str]]:
    """
    :param collection_names_and_ids_dict: convert_docIdAndType_to_collectionNameAndIds() 函数输出
    :return:
    """
    mongodb_id_info = {}

    collection_names = collection_names_and_ids_dict["collection_names"]
    mongodb_ids = collection_names_and_ids_dict["mongodb_ids"]

    for collection_name, mongodb_id in zip(collection_names, mongodb_ids):
        queried_data = MongodbUtil.query_doc_by_id(collection_name, mongodb_id)
        mongodb_id_info[mongodb_id] = {
            "_id": mongodb_id,
            "type": queried_data["data_type"],
            "title": queried_data["file_title"],
            "file_url": queried_data["minio_document_path"]
        }

    return mongodb_id_info


def is_excel_cell_empty(cell_obj: Any) -> bool:
    """
    检查Excel表的单元格内容是否为空。nan也为空
    :param cell_obj:
    :return:
    """
    if cell_obj is None:
        return True
    cell_obj_str = str(cell_obj).strip()
    if cell_obj_str is None or cell_obj_str == "" or cell_obj_str.lower() == "nan":  # 判断cell_obj_str是否为空
        return True
    return False


def get_node_products(
    label_raw_data: Dict[str, Any]
) -> Dict[str, Dict[str, Any]]:
    """
    :param
    :return:
    """
    node_products = {}
    nodes_sources_dict = label_raw_data["nodes_sources_dict"]  # TODO 用溯源字典
    mongodb_id_info = label_raw_data["mongodb_id_info"]
    node_companies = label_raw_data["node_companies"]
    for industry_node_str in nodes_sources_dict:
        try:
            # industry_node_str 内容示例："工业机器人|上游|零部件|核心零部件|控制器|硬件"
            split_industry_node_str = industry_node_str.split("|")
            front_node = split_industry_node_str[0]
            end_node = split_industry_node_str[-1]

            query_condition = {
                "$and": [
                    {"产业类型": front_node},
                    {
                        "$or": [
                            {"产业链1级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链2级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链3级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链4级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链5级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链6级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链7级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链8级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链9级环节": {"$regex": end_node, "$options": "i"}},
                            {"产业链10级环节": {"$regex": end_node, "$options": "i"}}
                        ]
                    }
                ]
            }
            query_res_iter = MongodbUtil.query_docs_by_condition(CollectionConfig.INDUSTRY_CHAIN_RELATED_PRODUCTS,
                                                                 query_condition)
            query_res_list = []
            for query_res in query_res_iter:
                query_res_list.append(query_res)

            # 过滤掉 query_res_list 中不是末级节点的元素
            query_res_list_filtered = []
            for query_res in query_res_list:
                # 获取 query_res 中的产业链末级节点名称
                query_res_end_node = ""
                for i in range(1, 100):
                    industry_level_col_name = f"产业链{i}级环节"
                    cur_node_name = str(query_res.get(industry_level_col_name))
                    if is_excel_cell_empty(cur_node_name):
                        break
                    cur_node_name = cur_node_name.strip()
                    query_res_end_node = cur_node_name
                if query_res_end_node == end_node.strip():  # 判断该条数据的末级节点是不是待查询产业的末级节点（首级节点已经匹配上了）
                    query_res_list_filtered.append(query_res)

            # 根据产业链节点查询关联的产品信息
            product_list = []
            if len(query_res_list_filtered) > 0:
                # TODO query_res_list_filtered 最后还有多个怎么办? 暂时只用其中一个
                """ query_res_list_filtered 多个示例：
                [{'_id': ObjectId('6800d825bbb7ca7088c914f3'), '上中下游': '上游', '产业类型': '工业机器人', '产业链1级环节': '减速器', '产业链2级环节': nan, '产业链3级环节': nan, '产业链4级环节': nan, '关联产品/商品（多个逗号隔开）': '谐波减速器(HD-20)，行星减速器(型号:PL-50)，精密行星摆线减速器', '数据来源': '2023年中国工业机器人行业概览：AI 浪潮下加速发展，上游新材料产业重塑-头猎 (工业机器人)'}, 
                {'_id': ObjectId('6800d825bbb7ca7088c91505'), '上中下游': '上游', '产业类型': '工业机器人', '产业链1级环节': '减速器', '产业链2级环节': nan, '产业链3级环节': nan, '产业链4级环节': nan, '关联产品/商品（多个逗号隔开）': '谐波减速器(HD-20)，行星减速器(型号:PL-50)，精密行星摆线减速器', '数据来源': '2023年中国工业机器人行业研究报告-艾瑞资讯 (工业机器人)'}, 
                {'_id': ObjectId('6800d825bbb7ca7088c9150f'), '上中下游': '上游', '产业类型': '工业机器人', '产业链1级环节': '减速器', '产业链2级环节': nan, '产业链3级环节': nan, '产业链4级环节': nan, '关联产品/商品（多个逗号隔开）': '谐波减速器(HD-20)，行星减速器(型号:PL-50)，精密行星摆线减速器', '数据来源': '2023中国工业机器人应用与趋势研究报告-亿欧智库 (工业机器人)'}]
                """
                related_products_str = query_res_list_filtered[0]["关联产品/商品（多个逗号隔开）"]

                if not is_excel_cell_empty(related_products_str):
                    # 把关联产品按逗号、分号、句号等符号分割为字符串数组：
                    # 使用正则表达式分割字符串
                    # \s* 表示匹配任意数量的空白字符（包括空格、制表符等）
                    # [,.:;!?] 表示匹配逗号、句号、冒号、分号、感叹号、问号
                    # \s* 在符号前后匹配任意数量的空白字符
                    split_related_products_list = re.split(r'\s*[,，.。;；!！?？]+\s*', related_products_str)

                    # 去除空字符串（如果有的话）
                    split_related_products_list = [item for item in split_related_products_list if item]
                    for split_related_products in split_related_products_list:
                        product_list.append({
                            "product_abb": split_related_products,
                            "company_abb": ""
                        })
                else:
                    LogUtil.info(
                        f"未查询到产业链环节 '{industry_node_str}' 对应的产品，下面将仅拼装该产业链节点对应的source_list")
            else:
                LogUtil.info(
                    f"未查询到产业链环节 '{industry_node_str}' 对应的产品，下面将仅拼装该产业链节点对应的source_list")

            # 根据产业链节点查询挂链公司全称
            company_fullname_list = [company_info.get("name") for company_info in node_companies.get(industry_node_str)]

            # 溯源：产业链节点-研报的source_list
            source_list = []
            node_source_ids = nodes_sources_dict[industry_node_str].get("source_id_list")
            for node_source_id in node_source_ids:
                mongodb_doc_info = mongodb_id_info.get(node_source_id)
                source_list.append({
                    "_id": node_source_id,
                    "type": mongodb_doc_info.get("type"),
                    "title": mongodb_doc_info.get("title"),
                    "file_url": mongodb_doc_info.get("file_url")
                })

            node_products[industry_node_str] = {
                "company": company_fullname_list,
                "product": product_list,
                "source_list": source_list
            }
        except Exception as e:
            detail = f"失败详情：{str(e)}"
            LogUtil.error(msg=detail)
            LogUtil.info(f"获取 产业-产品表 中的某个产业节点信息异常，已跳过。industry_node_str: {industry_node_str}")
            continue
    return node_products


def query_company_info(company_abb):
    """

    :param company_abb:
    :return:
    """
    res_company_info = {
        "abb": company_abb,
        "name": "",
        "is_listed": "否",
        "is_special": "否",
        "is_high_tech": "否",
        "stock_code": "",
        "province": "",
        "city": "",
        "industry_position": ""
    }

    try:
        company_basic = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one(
            {
                "$or": [
                    {"公司简称": company_abb},
                    {"公司名称": company_abb},
                    {"曾用名": {"$regex": re.escape(company_abb + "(")}}
                ]
            }
        )

        if company_basic:
            res_company_info["name"] = company_basic.get("公司名称")
            # 上市企业
            if company_basic.get("是否上市") == "是":
                res_company_info["is_listed"] = "是"
        else:
            company_brand = MongodbUtil.coll(CollectionConfig.BRAND_INFO).find_one(
                {"$or": [{"品牌/产品名称": company_abb}, {"公司全称": company_abb}]})
            if company_brand:
                res_company_info["name"] = company_brand.get("公司全称")

        fullname = res_company_info["name"]
        basic_info = MongodbUtil.coll(CollectionConfig.BASIC_INFO).find_one({"公司名称": fullname})
        if basic_info:
            res_company_info["stock_code"] = basic_info.get("股票代码") if isinstance(
                basic_info.get("股票代码"), str) else "-"
            res_company_info["province"] = basic_info.get("所属省") if isinstance(basic_info.get("所属省"),
                                                                                  str) else "-"
            res_company_info["city"] = basic_info.get("所属市") if isinstance(basic_info.get("所属市"),
                                                                              str) else "-"
        industry_position_info = MongodbUtil.coll(CollectionConfig.INDUSTRY_POSITION_INFO).find_one(
            {"企业名称": fullname})
        if industry_position_info:
            res_company_info["industry_position"] = industry_position_info.get("产业地位")
    except Exception as e:
        detail = f"失败详情：{str(e)}"
        LogUtil.error(msg=detail)
        LogUtil.info(f"公司信息查询异常，返回空数据。company_abb: {company_abb}")
        return res_company_info

    return res_company_info


def get_products(
    label_raw_data: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    从海关、发票、授信报告这些非研报文档提取出 公司-产品 ，并将提取出的公司补充到label_raw_data的各种公司溯源数据结构中
    :param label_raw_data:
    :return:
    """
    # TODO 从海关、发票、授信报告这些非研报文档提取出 公司-产品 ，并后续把这些公司加到展示数据的公司表
    products = []

    mongodb_id_info = label_raw_data['mongodb_id_info']
    for mongodb_id, mongodb_doc_info in mongodb_id_info.items():
        if mongodb_doc_info.get("type") not in ["invoice", "customs", "credit_report"]:
            # 跳过海关、发票、授信报告这些非研报文档
            continue

        # 查询文档中的 公司-产品
        query_res = MongodbUtil.query_one_doc_by_condition(
            CollectionConfig.EXTRACTED_COMPANY_AND_RELATED_PRODUCT,
            {
                "mongodb_id": mongodb_id
            }
        )
        if query_res is None:
            LogUtil.info(f"未在 '{CollectionConfig.EXTRACTED_COMPANY_AND_RELATED_PRODUCT}' 集合中"
                         f"找到该文档中关联的公司及其产品，跳过。mongodb_id: {mongodb_id}")
            continue

        company_abb = query_res.get("company_name")
        company_info = query_company_info(company_abb)
        company_name = company_info.get("name")

        # 补充进 label_raw_data["key_companies"]
        key_companies = label_raw_data["key_companies"]
        is_exist = False
        for key_company in key_companies:
            if key_company.get("abb") == company_abb or key_company.get("name") == company_name:
                is_exist = True
                break
        if not is_exist:
            key_companies.append(company_info)

        # 补充进 label_raw_data["mongodb_id_companies"]
        mongodb_id_companies = label_raw_data["mongodb_id_companies"]
        companies = mongodb_id_companies.get(mongodb_id)
        if companies:
            companies.append(company_abb)
            mongodb_id_companies[mongodb_id] = list(set(companies))
        else:
            mongodb_id_companies[mongodb_id] = [company_abb]

        # 最后加入 products
        products.append({
            "company_abb": company_abb,  # 从海关、发票、授信报告那里抽出来的简称（其实基本上是全称）,不是一开始从研报出来的真正的简称了
            "company_name": company_name,
            "product_abb": str(query_res.get("products")).replace(" ", ""),
            "source_list": [mongodb_doc_info]
        })

    return products


def get_company_abb_to_industry_nodes(
    label_raw_data: Dict[str, Any]
) -> Dict[str, List[str]]:
    """
    从标签提取原始数据中获取企业简称到 产业链环节 的映射，
    可能对应多个产业链环节，所以value为list，
    同时去除节点包含关系，例如：
    ['新能源汽车', '新能源汽车|中游', '新能源汽车|中游|零部件', '新能源汽车|中游|智能化供应商', '新能源汽车|中游|智能化供应商|智能座舱']
    将转换为：
    ['新能源汽车|中游|零部件', '新能源汽车|中游|智能化供应商|智能座舱']
    :param label_raw_data: 标签提取原始数据
    :return:
    """
    industry_nodes_of_company_abb = {}
    node_companies = label_raw_data["node_companies"]
    for industry_node in node_companies:
        company_list = node_companies[industry_node]
        for company in company_list:
            company_abb = company["abb"]
            if industry_nodes_of_company_abb.get(company_abb) is None:
                industry_nodes_of_company_abb[company_abb] = []
            industry_nodes_of_company_abb[company_abb].append(industry_node)

    # 去除节点包含关系
    for company_abb in industry_nodes_of_company_abb:
        industry_nodes_raw = industry_nodes_of_company_abb[company_abb]
        industry_nodes_copy = copy.deepcopy(industry_nodes_raw)
        for industry_node1 in industry_nodes_copy:
            for industry_node2 in industry_nodes_copy:
                if industry_node1 == industry_node2:
                    continue
                if industry_node1.find(industry_node2) != -1:
                    # 节点1包含了节点2，则去除原列表的节点2
                    try:
                        industry_nodes_raw.remove(industry_node2)
                    except ValueError as e:
                        # 已经被移除过了
                        pass

    return industry_nodes_of_company_abb


def fill_complete_chain_structure(
    industry_node_dict: Dict[str, Dict[str, Any]],
):
    """
    把以产业链环节为key的字典中的产业链环节补齐为数据库中该产业完整产业链图谱的所有节点，
    补充的item其value为空列表，
    industry_node_dict中涉及多个产业类型也能处理
    :param industry_node_dict: 示例：
    {
        "新能源汽车|上游|矿产资源|钴元素矿": [...],
        "新能源汽车|上游|矿产资源|钴矿": [...],
    }
    :return:示例：
    {
        "新能源汽车": [],
        "新能源汽车|上游": [],
        "新能源汽车|上游|矿产资源": []
        "新能源汽车|上游|矿产资源|钴元素矿": [...],
        "新能源汽车|上游|矿产资源|钴矿": [...],
        ...
    }
    """
    if len(industry_node_dict) == 0:
        return

    industry_node_dict_keys = industry_node_dict.keys()
    industry_types = set()  # 可能涉及多个产业类型：
    for industry_node_str in industry_node_dict_keys:
        industry_types.add(industry_node_str.split("|")[0])
    for industry_type in industry_types:
        query_res = MongodbUtil.query_one_doc_by_condition(
            CollectionConfig.CHAIN_STRUCTURE,
            {"industry": industry_type}
        )
        if query_res is None:
            LogUtil.info(f"产业链图谱表中未查询到该产业类型，跳过该产业类型。industry_type: {industry_type}")
            continue

        chain_structure = query_res.get("chain_structure")
        source_mongodb_ids = query_res.get("mongodb_ids")
        query_source_info_iter = MongodbUtil.query_docs_by_condition(
            CollectionConfig.RESEARCH_REPORT_INFO,
            {
                "_id": {
                    "$in": source_mongodb_ids
                }
            }
        )

        source_list = []
        for query_source_info in query_source_info_iter:
            source_list.append({
                '_id': query_source_info["_id"],
                'type': 'research_report',
                'title': query_source_info["title"],
                'file_url': query_source_info["source_url"]
            })

        # 调用递归函数得到以'|'连接的节点名称列表
        nodes = TreeUtils.extract_nodes(chain_structure)
        for node in nodes:
            if industry_node_dict.get(node) is None:
                # 不存在该节点，则添加该节点
                industry_node_dict[node] = {
                    "company": [],
                    "product": [],
                    "source_list": source_list
                }


def label_rawData_to_performData(
    label_raw_data: Dict[str, Any],
):
    """

    :param label_raw_data:
    :return: 
    """

    # 一、此时公司信息还缺一些字段，先补充完整
    # 1.获取公司溯源信息
    company_abb_to_mongo_ids_dict = get_company_abb_to_mongo_ids(label_raw_data)
    companies = copy.deepcopy(label_raw_data["key_companies"])
    for company in companies:
        company_abb = company["abb"]
        source_mongo_id_list = company_abb_to_mongo_ids_dict[company_abb]
        for source_mongo_id in source_mongo_id_list:
            mongo_info = label_raw_data["mongodb_id_info"][source_mongo_id]
            if company.get("source_list") is None:
                company["source_list"] = []
            company["source_list"].append(mongo_info)

    # 2. 获取公司所在的 产业链链末级环节 和 产业类型 ，可能有多个
    industry_nodes_of_company_abb_dict = get_company_abb_to_industry_nodes(label_raw_data)
    for company in companies:
        company_abb = company["abb"]
        company["chain_name"] = "-"
        company["chain_end_position"] = "-"
        industry_nodes = industry_nodes_of_company_abb_dict.get(company_abb)
        if industry_nodes:
            front_industry_node_name = set()
            end_industry_node_name = set()
            for industry_node in industry_nodes:
                split_industry_node = industry_node.split("|")
                front_industry_node_name.add(split_industry_node[0])
                end_industry_node_name.add(split_industry_node[-1])
            front_industry_node_name = "，".join(front_industry_node_name)
            end_industry_node_name = "，".join(end_industry_node_name)
            company["chain_name"] = front_industry_node_name
            company["chain_end_position"] = end_industry_node_name

    # # 二、把产业链环节的key补全为产业链图谱的所有节点
    # fill_complete_chain_structure(
    #     label_raw_data["node_products"]
    # )

    # 组装结果
    perform_data = {
        "raw_label_extrac_data_id": label_raw_data.get("_id"),
        "chain_structure": label_raw_data.get("node_products"),
        "company": companies,
        "product": label_raw_data.get("products"),
        "chain_structure_json": label_raw_data.get("chain_structure_json")
    }
    return perform_data


def save_perform_data_to_excel(perform_data,s_type):
    # perform_data 中的产业信息为字典，需要先转换为列表，以供pandas导出为Excel
    chain_structure = perform_data["chain_structure"]
    chain_structure_list_for_show = []
    for chain_structure_name in chain_structure:
        chain_structure_content = chain_structure[chain_structure_name]
        product = chain_structure_content["product"]
        source_list = chain_structure_content["source_list"]

        chain_structure_item = {}
        chain_structure_name_split_list = chain_structure_name.split("|")
        for i, chain_structure_name_split in enumerate(chain_structure_name_split_list):
            if i == 0:
                chain_structure_item.update({"产业类型": chain_structure_name_split})
            if i == 1:
                chain_structure_item.update({"上中下游": chain_structure_name_split})
            if i > 1:
                chain_structure_item.update({f"产业链{i - 1}级环节": chain_structure_name_split})
        chain_structure_item.update({
            "关联产品/商品(多个逗号隔开)": "，".join(
                [product_item_dict.get("product_abb") for product_item_dict in product]
            )
        })
        chain_structure_item.update({
            "数据来源": "、".join(
                [source_item_dict.get("title") for source_item_dict in source_list]
            )
        })
        chain_structure_list_for_show.append(chain_structure_item)

    # 将字典转换为 DataFrame
    df_chain_structure = pd.DataFrame(chain_structure_list_for_show)
    df_company = pd.DataFrame(perform_data["company"])
    df_product = pd.DataFrame(perform_data["product"])

    df_chain_structure.to_excel(
        f'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/{s_type}标签提取展示数据_产业.xlsx')
    df_company.to_excel(
        f'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/{s_type}标签提取展示数据_公司.xlsx')
    df_product.to_excel(
        f'D:/Documents/WeChat Files/wxid_oiewe2rzx0vc21/FileStorage/File/2025-05/产业链环节待跑相似/{s_type}标签提取展示数据_产品商品业务.xlsx')

    LogUtil.info("标签提取接口返回数据已保存为Excel表格")
