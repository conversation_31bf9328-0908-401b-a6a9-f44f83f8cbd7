﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/04/14 11:22:46
<AUTHOR>   WEIHA<PERSON> HONG 
@Email    :   <EMAIL>
@File    :   label_merge_service.py
@Project    :   tiance-industry-finance
'''

from service.llm_service import Llm_Service
from entity.message_entity import SystemMessage, UserMessage, MessageConverter
from utils.text_utils import TextUtil
from utils.log_util import LogUtil
import json
import requests
from collections import defaultdict
from configs.prompt_config import PromptConfig
from service.kb_service import KbService
from configs.collection_config import CollectionConfig
from api.routes.company_full_name_supplement import label_replace
import datetime
from service.label_to_perform_adapter import merge_and_deduplicate
from configs.prompt_config import PromptConfig
import asyncio
class LabelMergeService(object):
    """
    标签合并服务，含大模型与词库合并
    """
    def __init__(self, model,prompt,rule_type:str):
        """
        初始化
        """
        # 大语言模型客户端
        self.llm_service  = Llm_Service(model)
        self.model = model
        self.prompt = prompt
        self.kb_service = KbService()
        self.url = "http://210.43.57.13:9030/label_replace"
        if rule_type == "Frequency":
            self.rule_type = "num"
        elif rule_type == "Source":
            self.rule_type = "value"
        else :
            self.rule_type = "num"
    async def chains_merge_task(self,chain_name:str,chain:list):

        if len(chain) == 1 :
            return chain[0]
        chain_str = ""
        for i,c in enumerate(chain):
            chain_str = chain_str + f"来源{i+1}\n" + json.dumps(c,indent=4,ensure_ascii=False)  + "\n\n\n\n"

        compose_system_prompt =PromptConfig.RESEACH_REPORT_COMPOSE_SYSTEM_PROMPT_CUSTOMER.format(prompt=self.prompt,industry=chain_name) 
        # print(compose_system_prompt)
        # compose_system_prompt = self.prompt
        messages = []
        messages.append(SystemMessage(compose_system_prompt))
        user_prompt = chain_str
        messages.append(UserMessage(user_prompt))
        messages = MessageConverter.convert_messages(messages)
        model = self.model
        # llm_service = Llm_Service(model)
        LogUtil.info(f"大模型合并请求,model:{model},message:{messages}")
        answer = await self.llm_service.answer_question(messages, model, max_tokens=4096)
        LogUtil.info(f"大模型回复:{answer}")
        # print(answer)
        answer = TextUtil.remove_think(answer)
        prettify_json = TextUtil.get_json_from_text(answer)
        if prettify_json:
            try:
                prettify_json = json.loads(prettify_json.group())
            except Exception as e:
                prettify_json={}
                LogUtil.error("chain_structure的json解析异常：" + str(e))
                # prettify_json={}
            LogUtil.debug(f"大模型回复解析:{prettify_json}")
        else:
            prettify_json={}
        LogUtil.debug(f"大模型回复解析:{prettify_json}")
        return prettify_json

        
    async def llm_merge(self,label_array,is_industry=False):
        chains_list = {}
        # 产业链走新逻辑
        if is_industry == True:
            # print(type(label_array),label_array)
            if label_array is not None:
                for _id,single_report_chain in label_array.items():
                    for key,value in single_report_chain.items():
                        if key not in chains_list:
                            chains_list[key] = []
                        chains_list[key].append({key:value})

            tasks =[self.chains_merge_task(chain_name=key,chain=value) for key,value in chains_list.items()]

            tasks_results = await asyncio.gather(*tasks)
            LogUtil.info(f"return result :{tasks_results}")
            return tasks_results
        else:
            
            labels_str = "\n".join(label_array)
            is_check_dict = {}
            compose_system_prompt =PromptConfig.LABEL_COMPOSE_SYSTEM_PROMPT.format(prompt=self.prompt) 
            # print(compose_system_prompt)
            # compose_system_prompt = self.prompt
            messages = []
            messages.append(SystemMessage(compose_system_prompt))
            user_prompt = labels_str
            messages.append(UserMessage(user_prompt))
            messages = MessageConverter.convert_messages(messages)
            model = self.model
            # llm_service = Llm_Service(model)
            LogUtil.info(f"大模型合并请求,model:{model},message:{messages}")
            answer = await self.llm_service.answer_question(messages, model, max_tokens=4096)
            LogUtil.info(f"大模型回复:{answer}")
            answer = TextUtil.remove_think(answer)
            prettify_json = TextUtil.get_json_from_text(answer)
            if prettify_json:
                try:
                    # print(prettify_json.group())
                    prettify_json = json.loads(prettify_json.group())
                except Exception as e:
                    LogUtil.error("chain_structure的json解析异常：" + str(e))
                    prettify_json={}
                    # raise
            LogUtil.debug(f"大模型回复解析:{prettify_json}")
            if prettify_json == None or "result" not in prettify_json:
                LogUtil.warn(f"大模型回复解析:{prettify_json}")
                prettify_json = {"result":[]}
                
            check_result,prettify_json = self.check_llm_result(labels=label_array,llm_result=prettify_json)
            LogUtil.info(f"大模型一致性检查结果,check_result:{check_result},result:{prettify_json}")
        # print(answer)
        return prettify_json
    def check_llm_result(self,labels:list,llm_result:dict,is_industry=False):
        """
        检查llm是否有错漏或者胡编乱造
        """
        
        is_check_pass = True
        is_have = {}
        for label in labels:
            is_have[label] = False
        key_to_remove = []
        for llm_label in llm_result["result"]:
            if llm_label in is_have:
                is_have[llm_label] = True
            elif llm_label in llm_result:
                is_have[llm_label] = True
                LogUtil.error(f"llm add new label{llm_label}" )
            else:
                is_check_pass = False
                key_to_remove.append(llm_label)
                LogUtil.error(f"llm add unknown label{llm_label}" )
        for t_key in key_to_remove:
            llm_result["result"].remove(t_key)
        key_to_remove = []
        for key,value in llm_result.items():
            if key == "result":
                continue
            elif key in is_have and is_have[key]:
                t_value_to_remove = []
                for t_value in value:
                    if t_value in is_have:
                        is_have[t_value] = True
                    else:
                        is_check_pass = False
                        LogUtil.error(f"llm add unknown label{llm_label}" )
                    if t_value == key:
                        t_value_to_remove.append(t_value)
                    
                for t_value in t_value_to_remove:
                    value.remove(t_value)
            else:
                key_to_remove.append(key)
                is_check_pass = False
                LogUtil.error(f"llm add unknown label{key}" )
        for t_key in key_to_remove:
            llm_result.pop(t_key)
        for key,value in is_have.items():
            if value == False:
                is_check_pass = False
                LogUtil.error(f"llm missing label{key}" )
                llm_result["result"].append(key)
        return is_check_pass,llm_result
    
    async def merge(self,label_array:list[str],rule_type):
        # request_body = {
        #     "input_labels": label_array,
        #     "style":rule_type
        # }
        # response = requests.post(
        #     url=self.url,
        #     json=request_body
        # )
        LogUtil.info(f"label_array:{label_array}")
        response = await label_replace(input_labels=label_array,style=rule_type)
        LogUtil.info(f"rule merge response{response}")
        
        if response.code ==200:
            return response.data["result"]
        else:
            LogUtil.error(f"request failed :{response.code}")
            return []
        # if response.code =="200":
        #     return response.json()
        # else:
        #     LogUtil.error(f"request failed :{response.status_code}")
        #     return []
    async def process_labels(self,input_labels,rule_type):
        # 初始化处理列表，保存每个标签的层级结构和原始标签
        LogUtil.info(f"process_hirachy label {input_labels}")
        new_input_array = []
        for input_label in input_labels:
            input_label1 = input_label.replace('｜','|')
            new_input_array.append(input_label1)
        input_labels = new_input_array
        # print(str(input_labels))
        # print(str(new_input_array))
        # for input_label in input_labels:
        #     print(input_label)
        processed = []
        for label in input_labels:
            hierarchy = label.split('|')
            # print(hierarchy)
            processed.append({
                'original': label,
                'current_hierarchy': hierarchy.copy()
            })

        # 确定最大层级深度
        max_depth = max(len(item['current_hierarchy']) for item in processed) if processed else 0

        # 从最深层级向最浅层级处理
        for level in reversed(range(max_depth)):
            parent_map = defaultdict(list)

            # 收集同一父路径下的当前层级标签
            for item in processed:
                ch = item['current_hierarchy']
                if len(ch) > level:
                    parent_path = '|'.join(ch[:level])
                    current_value = ch[level]
                    parent_map[parent_path].append((current_value, item))

            # 处理每个父路径下的标签
            for parent_path, items in parent_map.items():
                values = [vi[0] for vi in items]
                merged_values = await self.merge(label_array=values,rule_type=rule_type)
                for idx, (_, item) in enumerate(items):
                    if len(item['current_hierarchy']) > level:
                        item['current_hierarchy'][level] = merged_values[idx]

        # 构建结果列表和映射关系
        result_map = defaultdict(list)
        for item in processed:
            merged_label = '|'.join(item['current_hierarchy'])
            result_map[merged_label].append(item['original'])

        # 生成去重后的结果列表（保留顺序）
        seen = set()
        result = []
        for item in processed:
            merged_label = '|'.join(item['current_hierarchy'])
            if merged_label not in seen:
                seen.add(merged_label)
                result.append(merged_label)

        # 构造返回的字典
        output = {"result": result}
        for k, v in result_map.items():
            output[k] = v
        return output
    async def rule_merge(self,label_array:list[str],is_industry=False):
        rule_type = self.rule_type
        rule_merge_result = {}
        result_label = []
        if is_industry==False:
            result_label = await self.merge(label_array=label_array,rule_type=rule_type)
            rule_merge_result["result"] = list(set(result_label))
            # print(response.json())
            for index,label in enumerate(label_array):
                if label == result_label[index]:
                    continue
                else:
                    if result_label[index] not in rule_merge_result:
                        rule_merge_result[result_label[index]] = []
                    if label not in rule_merge_result[result_label[index]]:
                        rule_merge_result[result_label[index]].append(label)
            
        else:
            rule_merge_result = await self.process_labels(input_labels=label_array,rule_type=rule_type)
        LogUtil.info(str(rule_merge_result))
        prettify_json = rule_merge_result
        check_result,prettify_json = self.check_llm_result(labels=label_array,llm_result=rule_merge_result)
        LogUtil.info(f"一致性检查结果,check_result:{check_result},result:{prettify_json}")
        
        return prettify_json
    
    async def ai_extend(self,label_extract_info:dict):
        node_companies = {}
        mongodb_id_node ={}
        mongodb_id_info = {}
        for item in label_extract_info["product"]:
            generate_key,source_list= await self.ai_extend_pair(product=item["product_abb"],company=item["company_abb"])
            generate_company_list = []
            for souce in source_list:
                if souce["mongodb_id"] not in mongodb_id_node:
                    mongodb_id_node[souce["mongodb_id"]] = []
                mongodb_id_node[souce["mongodb_id"]].append(generate_key)
                # if souce["mongodb_id"] not in mongodb_id_info:
                #     mongodb_id_info[souce["mongodb_id"]] = []
                mongodb_id_info[souce["mongodb_id"]] = {
                    "type":"研报",
                    "title":souce["source_file_title"],
                    "file_url":souce["source_file_url"]
                }
            for key_company in label_extract_info["key_companies"]:
                if key_company["abb"]!=item["company_abb"]:
                    continue
                company = {
                    "abb": key_company["abb"],
                    "name": key_company["name"],
                    "is_listed": key_company["is_listed"],
                    "is_special": key_company["is_special"],
                    "is_high_tech": key_company["is_high_tech"]
                }
                generate_company_list.append(company)
            if len(generate_company_list)> 0:
                node_companies[generate_key] = generate_company_list
        return node_companies,mongodb_id_node,mongodb_id_info
    async def ai_extend_perform(self,label_extract_info:dict):
        chain_structure = {}
        mongodb_id_node ={}
        mongodb_id_info = {}
        for item in label_extract_info["product"]:
            generate_key,source_list= await self.ai_extend_pair(product=item["product_abb"],company=item["company_abb"])
            if generate_key not in chain_structure:
                chain_structure[generate_key] = {}
            if "product" not in chain_structure[generate_key]:
                chain_structure[generate_key]["product"] = []
            chain_structure[generate_key]["product"].append({"product_abb":item["product_abb"],
          "company_abb": item["company_abb"]})
            if "company" not in chain_structure[generate_key]:
                chain_structure[generate_key]["company"] = []
            if "source_list" not in chain_structure[generate_key]:
                chain_structure[generate_key]["source_list"] = []
            t_source_list = []
            for source in source_list:
                t_source_list.append(
                    {
                        "_id": source["mongodb_id"],
                        "type":"研报",
                        "title":source["source_file_title"],
                        "file_url":source["source_file_url"]
                    }
                )
            chain_structure[generate_key]["source_list"] = merge_and_deduplicate(chain_structure[generate_key]["source_list"],t_source_list,"_id")
            
            # generate_company_list = []
            # for souce in source_list:
            #     if souce["mongodb_id"] not in mongodb_id_node:
            #         mongodb_id_node[souce["mongodb_id"]] = []
            #     mongodb_id_node[souce["mongodb_id"]].append(generate_key)
            #     # if souce["mongodb_id"] not in mongodb_id_info:
            #     #     mongodb_id_info[souce["mongodb_id"]] = []
            #     mongodb_id_info[souce["mongodb_id"]] = {
            #         "type":"研报",
            #         "title":souce["source_file_title"],
            #         "file_url":souce["source_file_url"]
            #     }
            # for key_company in label_extract_info["key_companies"]:
            #     if key_company["abb"]!=item["company_abb"]:
            #         continue
            #     company = {
            #         "abb": key_company["abb"],
            #         "name": key_company["name"],
            #         "is_listed": key_company["is_listed"],
            #         "is_special": key_company["is_special"],
            #         "is_high_tech": key_company["is_high_tech"]
            #     }
            #     generate_company_list.append(company)
            # if len(generate_company_list)> 0:
            #     node_companies[generate_key] = generate_company_list
        return chain_structure,mongodb_id_node,mongodb_id_info
    async def ai_extend_pair(self,product,company):
        question = f"请在{company}中的{product} 生成对应的产业链中的位置"
        doc_list = await self.kb_service.search_knowledge_by_question(collection_name=CollectionConfig.KNOWLEDGE_REPORT_ALL, question=question,limit_top_k=3)
        content_list = []
        source_list = []
        for item in doc_list:
            # 时间格式转换
            if item.get("file_time")=="tmp_file":
                item['file_time']=""
            if item.get("file_time",""):
                item['file_time'] = datetime.strptime(item['file_time'], '%Y/%m/%d').strftime('%Y-%m-%d')
            content_list.append(item["chunk_content_father"])
           
            # 可以添加"source_content": item["chunk_content"]返回源文本段用来测评
            source_list.append({
                                "mongodb_id" :item["mongodb_id"],
                                "source_name": item["file_source"], 
                                "source_file_title": item["file_title"], 
                                "source_file_url": item["file_url"], 
                                "source_content": item["chunk_content_father"]
                                 })
                          
        # 知识库信息
        knowledge = "\n\n".join(content_list)
        
        query = PromptConfig.LABEL_MERGE_AI_EXTEND_SYSTEM_PROMPT.format(knowledge=knowledge,product=product,company=company)
        # 组织消息
        messages = []
        # messages.append(SystemMessage(PromptConfig.PRODUCT_EXTENSION_SYSTEM_PROMPT.format(knowledge=knowledge)))
        messages.append(UserMessage(query))
        messages = MessageConverter.convert_messages(messages)
        
        LogUtil.info(f"大模型生成请求,model:{self.model},message:{messages}")
        answer = await self.llm_service.answer_question(messages, self.model, max_tokens=4096)
        LogUtil.info(f"大模型回复:{answer}")
        answer = TextUtil.remove_think(answer)
        prettify_json = TextUtil.get_json_from_text(answer)
        if prettify_json:
            try:
                prettify_json = json.loads(prettify_json.group())
            except Exception as e:
                LogUtil.error("chain_structure的json解析异常：" + str(e))
                # raise
        LogUtil.debug(f"大模型回复解析:{prettify_json}")
        # print(answer)
        real_result=  prettify_json["result"].replace('｜','|')
        LogUtil.debug(f"llm response {real_result}")
        prettify_json["result"] =real_result
        return prettify_json["result"],source_list
if __name__ == "__main__":
    import asyncio
    
    from utils.mongodb_util import MongodbUtil
    from utils.minio_util import MinIoUtil
    LogUtil.init(process_name="label_merge")
    # 初始化数据库连接
    MongodbUtil.connect()
    merge_service = LabelMergeService(model="qwen2.5-72B",prompt="",rule_type="Frequency")
#     asyncio.run(merge_service.rule_merge(label_array=["机器人","协作机器人","协作机器人","协助机器人","整车销售","锂矿2"],rule_type="num"))
#     input_labels = [
#     "新能源汽车",
#     "新能源汽车|上游|",
#     "新能源汽车|上游|协助机器人",
#     "新能源汽车|上游|协作机器人",
#     "新能源汽车|上游|协作型",
#     "新能源汽车|上游|协作型",
#     "新能源汽车|上游|协作机器人|协助机器人",
# ]
#     asyncio.run(merge_service.rule_merge(label_array=input_labels,rule_type="num",is_industry=True))
#     asyncio.run(merge_service.ai_extend_pair(product="问界",company="华为"))
    chain_structure = {
        "source1":{
            "工业机器人": {
                "上游": {
                  "核心零部件": {
                    "减速器": {},
                    "控制器": {},
                    "伺服电机": {},
                    "传感器": {},
                    "工业视觉": {}
                  }
                },
                "中游": {
                  "机器人本体": {
                    "直角坐标": {
                      "搬运/上下料": {},
                      "焊接/抛光打磨": {},
                      "喷涂": {},
                      "装配/拆卸": {}
                    },
                    "协助机器人": {
                      "搬运/上下料": {},
                      "焊接/抛光打磨": {},
                      "喷涂": {},
                      "装配/拆卸": {}
                    },
                    "多关节": {
                      "搬运/上下料": {},
                      "焊接/抛光打磨": {},
                      "喷涂": {},
                      "装配/拆卸": {}
                    },
                    "SCARA": {
                      "搬运/上下料": {},
                      "焊接/抛光打磨": {},
                      "喷涂": {},
                      "装配/拆卸": {}
                    },
                    "Delta": {
                      "搬运/上下料": {},
                      "焊接/抛光打磨": {},
                      "喷涂": {},
                      "装配/拆卸": {}
                    }
                  }
                },
                "下游": {
                  "集成与应用": {
                    "锂电": {},
                    "光伏": {},
                    "汽车": {}
                  }
                }
            },
        "新能源汽车":{
            "上游":{"锂矿资源":{}},
            "中游":{"大压铸":{}},
            "下游":{"KOL投放":{}}
        }
        },
        "source2":{
            "工业机器人": {
              "上游": {
                "新材料与核心零部件": {
                  "减速器": {},
                  "伺服电机": {},
                  "控制器": {},
                  "传感器": {},
                  "芯片": {},
                  "PCB": {}
                }
              },
              "中游": {
                "工业机器人制造商": {}
              },
              "下游": {
                "应用领域": {
                  "汽车制造": {},
                  "金属制品": {},
                  "电子电气": {},
                  "锂电池": {},
                  "光伏": {},
                  "食品加工": {},
                  "家用家具": {},
                  "其他": {}
                }
              },
              "新能源汽车":{
            "上游":{"锂电池":{}},
            "中游":{"汽车组装":{}},
            "下游":{"整车销售":{}}
        }
            }
        },
        
    }    
    results = asyncio.run(merge_service.llm_merge(label_array=chain_structure,is_industry=True))
    # print("results",results)
