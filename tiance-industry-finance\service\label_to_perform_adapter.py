﻿from utils.log_util import LogUtil


# 合并两个数组并去重，根据 'id' 属性判断是否重复
def merge_and_deduplicate(arr1, arr2, key):
    LogUtil.debug(f"merge before arr1: {arr1}, arr2: {arr2}")
    # 创建一个字典，以 key 为键，字典为值
    merged_dict = {item[key]: item for item in arr1}
    # 遍历第二个数组，如果 key 不存在于字典中，则添加
    for item in arr2:
        if item[key] not in merged_dict:
            merged_dict[item[key]] = item
    # 将字典的值转换为列表
    result = list(merged_dict.values())
    LogUtil.debug(f"merge after result: {result}")
    return result
def deduplicate_dicts(arr, key):
    """
    根据字典中某个键的值对数组进行去重。

    参数:
        arr (list): 包含字典的数组。
        key (str): 用于去重的键。

    返回:
        list: 去重后的数组。
    """
    seen = set()
    result = []
    for d in arr:
        if d.get(key) not in seen:
            seen.add(d.get(key))
            result.append(d)
    return result




def replace_mongodb_source(result, mongodb_dict):
    for key, value in result.items():
        if key == "result":
            continue
        for t_value in value:
            for monogodb_id, source_array in mongodb_dict.items():
                if t_value in source_array:
                    LogUtil.debug(f"t_value replace: {t_value}")
                    source_array.remove(t_value)
                    if key not in source_array:
                        LogUtil.debug(f"source_array add: {key}")
                        source_array.append(key)
    return mongodb_dict


def find_label_in_source(label: str, source_dict: dict):
    return_array = []

    for key, value in source_dict.items():
        if label in value:
            return_array.append(key)
    LogUtil.debug(f"find_label_in_source label:{label},return_array:{return_array},source_dict:{source_dict}")
    return return_array


def map_mongodb_id2info(source_array, source_info: dict):
    return_array = []
    for mongodb_id in source_array:
        if mongodb_id in source_info:
            mongodb_info = source_info[mongodb_id]
            mongodb_info["_id"] = mongodb_id
            return_array.append(mongodb_info)
        else:
            LogUtil.error(f"unable to find mongodb id:{mongodb_id} in source_info:{source_info}")
    LogUtil.debug(f"map mongodb id_list :{source_array} to mongodb info:{source_info}")
    return return_array


def get_chain_end_position(label: str, node_companies):
    # print(label,node_companies)
    node_array = []
    for key, value in node_companies.items():
        for company_item in value:
            if company_item["abb"] == label:
                node_array.append(key)
    return node_array


class Label2PerformAdapter(object):
    """
    文本工具类
    """

    @staticmethod
    def label_to_perform(label_merge_info):
        result_data = {}
        company_to_product = {}
        companyabb2companyinfo = {}
        chain_structure = {}

        # # 1.建立产品公司映射表
        # for item in label_merge_info["product"]:
        #     company_to_product[item["company_abb"]] = item
        # # 建立公司简称映射表
        # for item in label_merge_info["key_companies"]:
        #     companyabb2companyinfo[item["abb"]] = item
        # for key, value in label_merge_info["node_companies"].items():
        #     product = set()
        #     source_list = set()
        #     company_list = []
        #     source_list.update(
        #         set(find_label_in_source(label=key,
        #                                  source_dict=label_merge_info["mongodb_id_node"])))
        #     # generate prodcut list and source list
        #     for company_item in value:
        #         source_list.update(
        #             set(find_label_in_source(label=company_item["abb"],
        #                                      source_dict=label_merge_info["mongodb_id_companies"])))
        #         if company_item["abb"] in company_to_product:
        #             product_info = company_to_product[company_item["abb"]]
        #             product.add((product_info["company_abb"], product_info["product_abb"]))
        #             source_list.update(
        #                 set(find_label_in_source(label=product_info["product_abb"],
        #                                          source_dict=label_merge_info["mongodb_id_product"])))
        #
        #         else:
        #             LogUtil.error("can not found match company abb: " + company_item["abb"] + "in product")
        #     # generate source list
        #     product_list = []
        #     # LogUtil.debug(f"product:{product}")
        #     for company_abb, product_abb in product:
        #         product_list.append({"company_abb": company_abb, "product_abb": product_abb})
        #     chain_structure[key] = {}
        #     chain_structure[key]["product"] = product_list
        #     chain_structure[key]["source_list"] = map_mongodb_id2info(source_array=source_list,
        #                                                               source_info=label_merge_info["mongodb_id_info"])
        #     chain_structure[key]["company"] = value
        #     # print(f"test"+str(label_merge_info["merge_filter_industry"]))
        #     # if "merge_filter_industry" not in label_merge_info
        #     if key not in label_merge_info["merge_filter_industry"]:
        #         label_merge_info["merge_filter_industry"][key] = {}
        #     chain_structure[key]["merge_filter_industry"] = label_merge_info["merge_filter_industry"][key]
        # LogUtil.debug(f"genereate chain_structure:{chain_structure}")
        # result_data["chain_structure"] = chain_structure

        # 2. 更新company
        company_list = []
        for company_item in label_merge_info["key_companies"]:
            source_list = find_label_in_source(label=company_item["abb"],
                                               source_dict=label_merge_info["mongodb_id_companies"])
            source_list_info = map_mongodb_id2info(source_array=source_list,
                                                   source_info=label_merge_info["mongodb_id_info"])
            company_item["source_list"] = source_list_info
            chain_nodes = get_chain_end_position(label=company_item["abb"],
                                                 node_companies=label_merge_info["node_companies"])
            # print(chain_nodes)
            if len(chain_nodes) == 0:
                continue
            chain_end_position = []
            for node in chain_nodes:
                if node.split('|')[-1] in ["上游", "中游", "下游"]:
                    chain_end_position.append(node.split('|')[0])
                else:
                    chain_end_position.append(node.split('|')[-1])
            chain_name = [node.split('|')[0] for node in chain_nodes]
            company_item["chain_end_position"] = " ".join(chain_end_position)
            company_item["chain_name"] = " ".join(chain_name)
            company_list.append(company_item)
        LogUtil.debug(f"genereate company_list:{company_list}")
        result_data["company"] = company_list

        # 3. 更新product
        product_list = []
        for product_item in label_merge_info["product"]:
            product_item["company_name"] = companyabb2companyinfo[product_item["company_abb"]]["name"]
            source_list = find_label_in_source(label=product_item["product_abb"],
                                               source_dict=label_merge_info["mongodb_id_product"])
            source_list_info = map_mongodb_id2info(source_array=source_list,
                                                   source_info=label_merge_info["mongodb_id_info"])
            product_item["source_list"] = source_list_info
            product_list.append(product_item)
        # MongodbUtil.insert_one(collection_name=CollectionConfig.LABEL_MERGE, doc_content=result_data)
        LogUtil.debug(f"genereate product_list:{product_list}")
        result_data["product"] = product_list
        return result_data
