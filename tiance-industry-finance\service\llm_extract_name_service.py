﻿from utils.text_embed_util import TextEmbedService
from utils.milvus_util import MilvusUtil
from utils.llm_model_util import Llm_Service,MessageConverter,UserMessage
from configs.model_config import ModelConfig
from utils.reranker_util import QueryReranker
from configs.collection_config import CollectionConfig
reranker = QueryReranker()


class LlmHelper():
    def __init__(self):

        self.llm_service = Llm_Service()
        # self.pdf_name_to_filename = {
        #     "机器人": "机器人-首次公开发行股票并在创业板上市招股说明书",
        #     "东方国信": "东方国信-首次公开发行股票并在创业板上市招股说明书",
        #     "三环集团": "三环集团-首次公开发行股票并在创业板上市招股说明书",
        #     "晶瑞股份": "晶瑞股份-首次公开发行股票并在创业板上市招股说明书",
        #     "中大力德": "中大力德-首次公开发行股票招股说明书",
        #     "先导股份": "先导股份-首次公开发行股票并在创业板上市招股说明书"
        # }

    def extract_name(self,pdf_filename):

        # question = '你需要从这个文件名称名称中抽取出公司'

        for_llm= f"""
                你是一个金融行业专家

                抽取数据：{pdf_filename}
                需求：抽取出文件名中的公司名称

                输出样例如下 使用|分割个公司，如果没有，则输出||
                |中国银行海南分行|

                样例2（没有内容）
                ||


                必须严格按照输出样例格式输出，严禁输出任何其他内容，没有内容则输出||。
                """

        response = self.llm_service.answer_question(MessageConverter.convert_messages([UserMessage(for_llm)]),
                                                ModelConfig.MAX_LLM_MODEL_NAME)
        # print(pdf_filename)
        # print(response)
        rows = response.strip().split('\n')

        supplier_dict = {}
        for row in rows:
            columns = row.strip('|').split('|')
            company_name = columns[0]
            return company_name
        return ""



if __name__ =='__main__':
    llm_helper = LlmHelper()
    print(llm_helper.extract_name(pdf_filename="东方国信-首次公开发行股票并在创业板上市招股说明书"))

    # pdf_name_to_filename = {
    #         "机器人": "机器人-首次公开发行股票并在创业板上市招股说明书",# 没大问题
    #         "东方国信": "东方国信-首次公开发行股票并在创业板上市招股说明书",# 客户有问题，显示的是欠钱客户
    #         "三环集团": "三环集团-首次公开发行股票并在创业板上市招股说明书",# 客户略微带点问题（跨页表格 只索引了一部分）
    #         "晶瑞股份": "晶瑞股份-首次公开发行股票并在创业板上市招股说明书",# 客户有问题，显示的是欠钱客户
    #         "中大力德": "中大力德-首次公开发行股票招股说明书",# 客户和同行业问题都较大
    #         "先导股份": "先导股份-首次公开发行股票并在创业板上市招股说明书" # 没大问题
    #     }
    # # company_name = "机器人"
    # # company_name ="东方国信"
    # # company_name = "晶瑞股份"
    # # company_name = "三环集团"
    # # company_name = "中大力德"
    # company_name = "先导股份"
    # milvus_util = MilvusUtil()
    # embutil = TextEmbedService()
    # com_analysis = CompanyAnalysis(milvus_util,embutil)

    # for key in pdf_name_to_filename:
    #     print(key)
    #     supplier_result = com_analysis.company_supplier(key)
    #     print("--------------客户分析情况------------------")
    #     print(supplier_result)


    # 供应商分析情况
    # supplier_result = com_analysis.company_supplier(company_name)
    # print("--------------供应商分析情况------------------")
    # print(supplier_result)
# 
    # 同行业分析情况
    # print(company_name)
    # same_industry_result = com_analysis.company_same_industry(company_name)
    # print("--------------同行业分析情况2-----------------")
    # print(same_industry_result)

    # # 客户分析情况
    # client_result = com_analysis.company_client(company_name)
    # print("--------------客户分析情况-----------------")
    # print(client_result)

