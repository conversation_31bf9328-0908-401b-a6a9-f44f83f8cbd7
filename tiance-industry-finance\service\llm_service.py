#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :llm_service.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 17:03:08
'''

from model.llm_model import LlmModel
from configs.model_config import ModelConfig
from entity.message_entity import UserMessage, MessageConverter
from typing import List

from utils.log_util import LogUtil


class Llm_Service(object):
    """
    大模型问答服务
    """

    def __init__(self, model):
        """
        初始化
        """
        # 大语言模型客户端
        self.llm_model_client = LlmModel.get_model_client(model)

    async def answer_question(self, messages: List[dict[str, str]], model: str, max_tokens=1024):
        """
        回答问题
        :param messages: 模型对话信息，用法：MessageConverter.convert_messages([UserMessage("你好")])，
                        最终传入的参数形式为：[{"role":"user","content": "你好"}]
        :param model: 模型名称
        :param max_tokens: 最大token数量
        :return:
        """
        # 调用模型
        reply = await self.llm_model_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.1,
            top_p=0.95,
            max_tokens=max_tokens
        )
        LogUtil.info(f"reply: {reply.choices[0].message.content}")
        # 返回答案
        return reply.choices[0].message.content

    async def stream_answer_question(self, messages, model: str, max_tokens=1024):
        """
        流式回答问题
        :param messages: 模型对话信息
        :param model: 模型名称
        :param max_tokens: 最大token数量
        :return:
        """
        reply_stream = await self.llm_model_client.chat.completions.create(
            model=model,
            stream=True,
            messages=messages,
            temperature=0.6,
            top_p=0.95,
            max_tokens=max_tokens
        )
        # 返回答案流
        return reply_stream
