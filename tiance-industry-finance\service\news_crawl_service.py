import sys
from pathlib import Path
import requests
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))
from utils.chorme_driver_utils import ChromeDriver
from service.news_pageparse_service import WebPageParser
from configs.news_crawl_config import NewsCrawlConfig
from bs4 import BeautifulSoup

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:29
# <AUTHOR> <PERSON><PERSON><PERSON>an
# @File         : news_crawl_service.py
# @Description  : 资讯类数据爬取
"""



class NewsCrawler:
    def __init__(self, config):
        self.config = config

    def get_news_pdf(self,urls):
        if self.config['crawl_method'] == 'driver':
            self.driver = ChromeDriver()
        elif self.config['crawl_method'] == 'requests':
            pass

        all_data = []
        for url in urls:
            # 访问网页
            if self.config['crawl_method'] == 'driver':
                self.driver.get(url)
                parser = WebPageParser(self.driver.page_source)
            elif self.config['crawl_method'] == 'request':
                headers = {
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
                }
                response = requests.get(url,headers=headers)
                response.encoding='utf-8'
                # 使用BeautifulSoup解析HTML
                soup = BeautifulSoup(response.text, 'html.parser')
                parser = WebPageParser(str(soup))

            # 网页结构信息
            title_xpath = self.config['title_xpath']
            source_path = self.config['source_path']
            release_time_xpath = self.config['release_time_xpath']
            content_list_xpath = self.config['content_list_xpath']
            distince_xpath = self.config['distince_xpath']
            list_type = self.config['list_type']
            img_url_header = self.config['img_url_header']
            date_format = self.config['date_format']

            # 获取网页信息 和构建pdf文档
            data = parser.get_page_info(title_xpath, source_path, release_time_xpath, content_list_xpath, list_type, date_format, img_url_header, distince_xpath)
            data['url'] = url
            all_data.append(data)
        if self.config['crawl_method'] == 'driver':
            self.driver.close_driver()
        return all_data

if __name__ == "__main__":

    config = NewsCrawlConfig.KR36
    urls = ['https://36kr.com/p/1451866260367490']
    crawler = NewsCrawler(config)
    result = crawler.get_news_pdf(urls)
    print(result)
