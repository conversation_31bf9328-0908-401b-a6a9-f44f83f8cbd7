#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:29
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : news_data_processer.py
# @Description  : 资讯类数据处理
"""

from datetime import datetime
from configs.news_crawl_config import NewsCrawlConfig
import uuid

class DataProcessor:

    @staticmethod
    def process_data(data):
        now_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        release_time = data["release_time"]
        if release_time == "":
            release_time = now_time
        # date_format = "%Y-%m-%d %H:%M:%S"
        # release_time = datetime.strptime(release_time, date_format)
        year = release_time.year
        month = release_time.month
        day = release_time.day
        minio_local = NewsCrawlConfig.REMOTE_PATH + str(year) + '/' + str(month) + '/' + str(day) + '/' + data["pdf_file_name"]
        other_info={}
        if data["distinct"] is not None:
            other_info['地点'] = data["distinct"]

        process_data = {
            "_id":uuid.uuid4().hex,
            "data": {
                "data_type": "资讯",
                "data_source": data["source"],
            },
            "file": {
                "file_title": data["title"],
                "file_type": "pdf",
                "file_url": data["url"],
                "file_flag": other_info,
            },
            "time": {
                "release_time": release_time.strftime("%Y-%m-%d %H:%M:%S"),
                "crawling_time": now_time,
                "parse_time": None,
            },
            "minio": {
                "minio_name": NewsCrawlConfig.BUCKET_NAME,
                "minio_document_path": minio_local,
            },
            "milvus": {
                "milvus_db_name": None,
                "milvus_collection_name": None,
            },
            "status": 1,
        }

        return process_data
