#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :rerank_service.py
@Description  :
<AUTHOR>
@Date         :2025/02/27 11:26:36
'''

import requests
from configs.run_config import RunConfig

class Rerank_Service(object):
    """
    重排服务
    """

    def __init__(self):
        """
        初始化
        """
        # URL
        self.rerank_url = RunConfig.RERANK_MODEL_URL
        # 模型名
        self.model = RunConfig.RERANK_MODEL_NAME
    
    async def rerank(self, query, documents):
        """
        :param query: 查询语句
        :param documents: 文档列表
        :return:
        """
        url = self.rerank_url
        request_body = {
            "model": self.model,
            "documents": documents,
            "query": query,
        }
        response = requests.post(url, json=request_body)
        if response.status_code != 200:
            raise RuntimeError(
                f"Failed to rerank documents, detail: {response.json()['detail']}"
            )
        response_data = response.json()
        return response_data
