#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :text_embed_service.py
@Description  :
<AUTHOR>
@Date         :2024/11/15 15:48:11
'''

from model.embed_model import EmbedModel
from configs.model_config import ModelConfig

class TextEmbedService(object):
    """
    文本嵌入服务
    """

    def __init__(self):
        # 嵌入模型客户端
        self.embed_model_client = EmbedModel.get_model_client()

    async def text_embedding(self, sentence_list):
        """
        本文嵌入
        :param sentence_list: 句子列表
        :return:
        """
        # 解析结果
        embed_list = []
        # 进行嵌入
        embed_res = await self.embed_model_client.embeddings.create(model=ModelConfig.EMBED_MODEL_NAME, input=sentence_list)
        for embed_item in embed_res.data:
            embed_list.append(embed_item.embedding)
        return embed_list
