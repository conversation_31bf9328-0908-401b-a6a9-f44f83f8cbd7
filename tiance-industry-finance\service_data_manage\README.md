# 数据管理模块 (service_data_manage)

## 概述

数据管理模块提供公司数据的查询、统计和修改功能，支持单个和批量操作。所有功能都遵循 tiance-base 架构风格，使用 `query_by_column` 和 `update_by_id` 方法进行数据库操作。

## 目录结构

```
service_data_manage/
├── __init__.py
├── README.md
├── api/
│   ├── __init__.py
│   ├── data_manage.py          # API模块入口
│   └── routes/
│       ├── __init__.py
│       └── data_manage_route.py # API路由定义
├── entity/
│   ├── __init__.py
│   └── data_manage_entity.py   # 请求实体类
└── service/
    ├── __init__.py
    └── data_manage_service.py  # 业务逻辑服务
```

## API 接口

### 基础URL
所有接口都以 `/data_manage` 为前缀。

### 1. 公司数据统计查询

#### POST /data_manage/company_data_stats
```json
// 请求
{}

// 响应
{
    "code": 200,
    "message": "success",
    "data": {
        "total_count": 12345,
        "listed_count": 4567,
        "today_updated_count": 89,
        "query_date": "2025-01-23"
    }
}
```

#### GET /data_manage/company_data_stats_simple
无需参数的GET方式查询，返回格式同上。

### 2. 获取公司信息

#### GET /data_manage/company_info/{company_code}
```json
// 响应
{
    "code": 200,
    "message": "success",
    "data": {
        "company_code": "COMP001",
        "chi_name": "公司名称",
        "chi_name_abbr": "公司简称",
        "pre_name": "曾用名1,曾用名2",
        "eng_name": "Company Name",
        "stock_abbr": "STOCK",
        "create_time": "2025-01-01 00:00:00",
        "update_time": "2025-01-23 11:30:00",
        "status": 1
    }
}
```

### 3. 公司数据修改（支持单个和批量）

#### POST /data_manage/company_update

**单个修改：**
```json
// 请求
{
    "company_code": "COMP001",
    "chi_name": "新公司名称",
    "chi_name_abbr": "新简称",
    "eng_name": "New Company Name"
}

// 响应
{
    "code": 200,
    "message": "success",
    "data": {
        "company_code": "COMP001",
        "old_chi_name": "原公司名称",
        "new_chi_name": "新公司名称",
        "chi_name_abbr": "新简称",
        "eng_name": "New Company Name",
        "pre_name": "原公司名称,其他曾用名",
        "update_time": "2025-01-23 11:30:00",
        "message": "公司信息修改成功"
    }
}
```

**批量修改：**
```json
// 请求
{
    "companies": [
        {
            "company_code": "COMP001",
            "chi_name": "新公司名称A",
            "chi_name_abbr": "新简称A"
        },
        {
            "company_code": "COMP002", 
            "chi_name": "新公司名称B",
            "eng_name": "New Company B"
        }
    ]
}

// 成功响应
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "total_count": 2,
        "updated_count": 2,
        "updated_companies": [...],
        "message": "批量更新成功，共更新 2 家公司"
    }
}

// 错误响应（任何一个数据不符合就返回错误）
{
    "code": 200,
    "message": "success", 
    "data": {
        "success": false,
        "error_index": 1,
        "error_company_code": "COMP002",
        "error_message": "企业编号 COMP002 不存在或已被删除",
        "error_type": "existence_error",
        "total_count": 2,
        "processed_count": 1
    }
}
```

### 4. 检查公司名称重复

#### POST /data_manage/company_name_check?chi_name=测试公司&exclude_company_code=COMP001
```json
// 响应
{
    "code": 200,
    "message": "success",
    "data": {
        "is_duplicate": false,
        "chi_name": "测试公司",
        "exclude_company_code": "COMP001",
        "message": "名称 '测试公司' 可以使用"
    }
}
```

## 核心特性

### 1. 使用 update_by_id 方法
- 所有单个公司修改都使用 `SQLUtil.update_by_id()` 方法
- 支持主键直接更新，提高性能

### 2. 完美的接口复用设计
- `update_company_info` 复用 `get_company_info` 和 `check_chi_name_duplicate` 方法
- `batch_update_companies` 复用 `update_company_info` 接口进行实际更新
- 先进行批量检查，再逐个调用单个更新接口
- 避免代码重复，保持逻辑一致性
- 任何一个数据不符合要求就立即返回错误

### 3. 一致的数据验证逻辑
- 企业编号不能为空
- 中文名称不能为空
- 公司存在性检查（统一使用 `get_company_info` 方法）
- 名称重复性检查（统一使用 `check_chi_name_duplicate` 方法）
- 批量数据内部重复检查
- 单个和批量操作使用完全相同的验证逻辑

### 4. 曾用名管理
- 自动将原名称添加到曾用名
- 逗号分隔存储，自动去重
- 新名称不会出现在曾用名中

## 代码架构设计

### 方法调用关系图
```
batch_update_companies()
├── 基础验证（企业编号、中文名称非空）
├── get_company_info() ──────────┐
├── check_chi_name_duplicate() ──┤ 复用验证逻辑
├── 批量内部重复检查             │
└── update_company_info() ───────┤ 复用更新逻辑
    ├── get_company_info() ──────┤
    ├── check_chi_name_duplicate()┘
    ├── _update_pre_name()
    └── SQLUtil.update_by_id()
```

### 设计优势
1. **单一职责**: 每个方法都有明确的职责
2. **高度复用**: 避免重复代码，提高维护性
3. **逻辑一致**: 单个和批量操作使用相同的验证逻辑
4. **易于测试**: 每个方法都可以独立测试
5. **易于扩展**: 新增验证规则只需修改基础方法

## 使用示例

### Python 客户端示例
```python
import requests

base_url = "http://localhost:9029/data_manage"

# 1. 获取统计数据
response = requests.get(f"{base_url}/company_data_stats_simple")
print(response.json())

# 2. 获取公司信息
response = requests.get(f"{base_url}/company_info/COMP001")
print(response.json())

# 3. 单个修改
data = {
    "company_code": "COMP001",
    "chi_name": "新公司名称",
    "chi_name_abbr": "新简称"
}
response = requests.post(f"{base_url}/company_update", json=data)
print(response.json())

# 4. 批量修改
batch_data = {
    "companies": [
        {"company_code": "COMP001", "chi_name": "批量测试A"},
        {"company_code": "COMP002", "chi_name": "批量测试B"}
    ]
}
response = requests.post(f"{base_url}/company_update", json=batch_data)
print(response.json())
```

### curl 示例
```bash
# 获取统计数据
curl -X GET "http://localhost:9029/data_manage/company_data_stats_simple"

# 获取公司信息
curl -X GET "http://localhost:9029/data_manage/company_info/COMP001"

# 单个修改
curl -X POST "http://localhost:9029/data_manage/company_update" \
     -H "Content-Type: application/json" \
     -d '{"company_code": "COMP001", "chi_name": "新公司名称"}'

# 批量修改
curl -X POST "http://localhost:9029/data_manage/company_update" \
     -H "Content-Type: application/json" \
     -d '{"companies": [{"company_code": "COMP001", "chi_name": "批量测试A"}]}'
```

## 测试

### 运行集成测试
```bash
python script/test_api_integration.py
```

### 运行API测试
```bash
python script/test_data_manage_api.py
```

### 运行使用示例
```bash
python examples/data_manage_example.py
```

## 错误处理

所有接口都有完善的错误处理机制：

- **validation_error**: 参数验证错误
- **existence_error**: 公司不存在错误
- **duplicate_error**: 名称重复错误
- **internal_duplicate_error**: 批量数据内部重复错误
- **update_error**: 更新操作错误
- **system_error**: 系统错误

## 注意事项

1. **数据备份**: 修改前建议备份重要数据
2. **并发控制**: 高并发环境下注意数据一致性
3. **权限控制**: 生产环境需要添加适当的权限验证
4. **日志记录**: 所有修改操作都有详细的日志记录
5. **错误处理**: 完善的异常处理和错误提示

## 版本信息

- **创建时间**: 2025-01-23
- **版本**: v1.0.0
- **兼容性**: 基于 tiance-base 架构风格
- **依赖**: SQLAlchemy, FastAPI, Pydantic
