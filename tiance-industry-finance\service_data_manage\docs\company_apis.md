# 公司数据管理接口文档

## 概述

本文档详细描述了公司数据管理模块的两个核心接口：公司数据统计查询接口和公司数据修改接口。这两个接口提供了完整的公司数据查询和修改功能。

---

## 1. 公司数据统计查询接口

### 1.1 接口信息
- **接口路径**: `/data_manage/company_data_stats`
- **请求方法**: `POST`
- **功能**: 查询公司数据的总量统计信息

### 1.2 请求参数
```json
{}
```
**说明**: 无需传入任何参数，传入空对象即可。

### 1.3 响应格式
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total_count": 12345,
        "listed_count": 4567,
        "today_updated_count": 89,
        "query_date": "2025-01-23"
    }
}
```

### 1.4 响应字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| total_count | int | 公司数据总量（status=1的记录总数） |
| listed_count | int | 上市公司总数（StockAbbr不为空的记录总数） |
| today_updated_count | int | 当日更新总数（update_time >= 今日0点的记录总数） |
| query_date | string | 查询日期（YYYY-MM-DD格式） |

### 1.5 使用示例
```bash
# curl 示例
curl -X POST "http://localhost:9029/data_manage/company_data_stats" \
     -H "Content-Type: application/json" \
     -d '{}'

# 简化版 GET 接口
curl -X GET "http://localhost:9029/data_manage/company_data_stats_simple"
```

---

## 2. 公司数据修改接口

### 2.1 接口信息
- **接口路径**: `/data_manage/company_update`
- **请求方法**: `POST`
- **功能**: 支持单个和批量公司数据修改

### 2.2 单个修改请求
```json
{
    "company_code": "COMP001",
    "chi_name": "新公司名称",
    "chi_name_abbr": "新简称",
    "eng_name": "New Company Name"
}
```

### 2.3 批量修改请求
```json
{
    "companies": [
        {
            "company_code": "COMP001",
            "chi_name": "新公司名称A",
            "chi_name_abbr": "新简称A",
            "eng_name": "New Company A"
        },
        {
            "company_code": "COMP002",
            "chi_name": "新公司名称B",
            "chi_name_abbr": "新简称B",
            "eng_name": "New Company B"
        }
    ]
}
```

### 2.4 请求参数说明
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| company_code | string | 是 | 企业编号 |
| chi_name | string | 是 | 中文名称 |
| chi_name_abbr | string | 否 | 企业别称 |
| eng_name | string | 否 | 英文全称 |

### 2.5 单个修改成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "company_code": "COMP001",
        "old_chi_name": "原公司名称",
        "new_chi_name": "新公司名称",
        "chi_name_abbr": "新简称",
        "eng_name": "New Company Name",
        "pre_name": "原公司名称,其他曾用名",
        "update_time": "2025-01-23 11:30:00",
        "message": "公司信息修改成功"
    }
}
```

### 2.6 批量修改成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": true,
        "total_count": 2,
        "updated_count": 2,
        "updated_companies": [
            {
                "index": 0,
                "company_code": "COMP001",
                "old_chi_name": "原名称A",
                "new_chi_name": "新公司名称A",
                "update_time": "2025-01-23 11:30:00"
            },
            {
                "index": 1,
                "company_code": "COMP002",
                "old_chi_name": "原名称B",
                "new_chi_name": "新公司名称B",
                "update_time": "2025-01-23 11:30:00"
            }
        ],
        "message": "批量更新成功，共更新 2 家公司"
    }
}
```

### 2.7 批量修改错误响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "success": false,
        "error_index": 1,
        "error_company_code": "COMP002",
        "error_message": "企业编号 COMP002 不存在或已被删除",
        "error_type": "existence_error",
        "total_count": 2,
        "processed_count": 1
    }
}
```

### 2.8 错误类型说明
| 错误类型 | 说明 |
|----------|------|
| validation_error | 参数验证错误（企业编号或中文名称为空） |
| existence_error | 企业编号不存在或已被删除 |
| duplicate_error | 中文名称已被其他公司使用 |
| internal_duplicate_error | 批量数据内部名称重复 |
| update_error | 数据库更新操作失败 |
| system_error | 系统错误 |

### 2.9 使用示例

#### 单个修改
```bash
curl -X POST "http://localhost:9029/data_manage/company_update" \
     -H "Content-Type: application/json" \
     -d '{
         "company_code": "COMP001",
         "chi_name": "新公司名称",
         "chi_name_abbr": "新简称",
         "eng_name": "New Company Name"
     }'
```

#### 批量修改
```bash
curl -X POST "http://localhost:9029/data_manage/company_update" \
     -H "Content-Type: application/json" \
     -d '{
         "companies": [
             {
                 "company_code": "COMP001",
                 "chi_name": "批量测试A",
                 "chi_name_abbr": "测试A"
             }
         ]
     }'
```

---

## 3. 业务规则

### 3.1 数据验证规则
- **企业编号**: 不能为空，必须在数据库中存在且状态为正常
- **中文名称**: 不能为空，不能与其他公司重复
- **企业别称**: 可选字段，可以为空
- **英文全称**: 可选字段，可以为空

### 3.2 曾用名管理
- 当中文名称发生变化时，原名称自动添加到曾用名中
- 曾用名以逗号分割存储，自动去重
- 新名称不会出现在曾用名中

### 3.3 批量更新规则
- 先检查所有数据的有效性
- 任何一个数据不符合要求就立即返回错误
- 只有所有检查通过才执行更新操作
- 批量数据内部不能有重复的中文名称

### 3.4 更新规则
- 自动更新 update_time 字段
- 只更新传入的字段，未传入的字段保持不变
- 使用事务确保数据一致性

---

## 4. Python 客户端示例

```python
import requests
import json

class CompanyDataClient:
    def __init__(self, base_url="http://localhost:9029"):
        self.base_url = base_url.rstrip('/')
        self.data_manage_url = f"{self.base_url}/data_manage"
    
    def get_company_stats(self):
        """获取公司数据统计"""
        url = f"{self.data_manage_url}/company_data_stats_simple"
        response = requests.get(url, timeout=30)
        return response.json()
    
    def update_single_company(self, company_code, chi_name, chi_name_abbr=None, eng_name=None):
        """单个公司修改"""
        url = f"{self.data_manage_url}/company_update"
        data = {
            "company_code": company_code,
            "chi_name": chi_name
        }
        if chi_name_abbr:
            data["chi_name_abbr"] = chi_name_abbr
        if eng_name:
            data["eng_name"] = eng_name
        
        response = requests.post(url, json=data, timeout=30)
        return response.json()
    
    def batch_update_companies(self, companies_data):
        """批量公司修改"""
        url = f"{self.data_manage_url}/company_update"
        data = {"companies": companies_data}
        response = requests.post(url, json=data, timeout=30)
        return response.json()

# 使用示例
client = CompanyDataClient()

# 获取统计数据
stats = client.get_company_stats()
print(f"公司总数: {stats['data']['total_count']}")

# 单个修改
result = client.update_single_company("COMP001", "新公司名称", "新简称")
print(f"修改结果: {result['data']['message']}")

# 批量修改
batch_data = [
    {"company_code": "COMP001", "chi_name": "批量测试A"},
    {"company_code": "COMP002", "chi_name": "批量测试B"}
]
batch_result = client.batch_update_companies(batch_data)
print(f"批量修改结果: {batch_result['data']['message']}")
```

---

