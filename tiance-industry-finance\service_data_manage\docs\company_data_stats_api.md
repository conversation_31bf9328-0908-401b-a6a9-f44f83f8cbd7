# 公司数据统计查询接口文档

## 接口概述

公司数据统计查询接口提供公司数据的总量统计功能，包括公司总数、上市公司数量、当日更新数量等关键指标。

## 接口信息

- **接口路径**: `/data_manage/company_data_stats`
- **请求方法**: `POST`
- **接口名称**: 公司数据统计查询
- **接口版本**: v1.0.0

## 请求参数

### 请求头
```
Content-Type: application/json
```

### 请求体
```json
{}
```

**说明**: 该接口无需传入任何参数，传入空对象即可。

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total_count": 12345,
        "listed_count": 4567,
        "today_updated_count": 89,
        "query_date": "2025-01-23"
    }
}
```

### 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应状态码，200表示成功 |
| message | string | 响应消息，"success"表示成功 |
| data | object | 统计数据对象 |
| data.total_count | int | 公司数据总量（status=1的记录总数） |
| data.listed_count | int | 上市公司总数（StockAbbr不为空的记录总数） |
| data.today_updated_count | int | 当日更新总数（update_time >= 今日0点的记录总数） |
| data.query_date | string | 查询日期（YYYY-MM-DD格式） |

### 错误响应
```json
{
    "code": 500,
    "message": "false",
    "data": {
        "error": "公司数据总量查询失败：具体错误信息",
        "error_type": "company_data_stats_error"
    }
}
```

## 业务逻辑

### 统计规则

1. **公司数据总量 (total_count)**
   - 统计 `CompanyMain` 表中 `status = 1` 的记录数
   - 只统计正常状态的公司数据

2. **上市公司总数 (listed_count)**
   - 统计 `StockAbbr` 字段不为空且不为 'NULL' 的记录数
   - 需要同时满足 `status = 1` 的条件

3. **当日更新总数 (today_updated_count)**
   - 统计 `update_time` 大于等于当日0点的记录数
   - 基于服务器当前日期计算

4. **查询日期 (query_date)**
   - 返回服务器当前日期
   - 格式为 YYYY-MM-DD

### 数据来源
- 数据表: `CompanyMain`
- 查询方法: 使用 `SQLUtil.count_records_with_condition()` 进行条件统计
- 数据库连接: 自动管理连接和关闭

## 使用示例

### Python 示例
```python
import requests
import json

# 接口地址
url = "http://localhost:9029/data_manage/company_data_stats"

# 请求数据
data = {}

# 发送请求
response = requests.post(url, json=data, timeout=30)

# 处理响应
if response.status_code == 200:
    result = response.json()
    if result["code"] == 200:
        stats = result["data"]
        print(f"公司总数: {stats['total_count']}")
        print(f"上市公司数: {stats['listed_count']}")
        print(f"今日更新数: {stats['today_updated_count']}")
        print(f"查询日期: {stats['query_date']}")
    else:
        print(f"查询失败: {result['message']}")
else:
    print(f"请求失败: {response.status_code}")
```

### JavaScript 示例
```javascript
// 使用 fetch API
fetch('http://localhost:9029/data_manage/company_data_stats', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({})
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        const stats = data.data;
        console.log(`公司总数: ${stats.total_count}`);
        console.log(`上市公司数: ${stats.listed_count}`);
        console.log(`今日更新数: ${stats.today_updated_count}`);
        console.log(`查询日期: ${stats.query_date}`);
    } else {
        console.error('查询失败:', data.message);
    }
})
.catch(error => {
    console.error('请求失败:', error);
});
```

### curl 示例
```bash
curl -X POST "http://localhost:9029/data_manage/company_data_stats" \
     -H "Content-Type: application/json" \
     -d '{}'
```

## 简化版接口

### GET 方式调用
- **接口路径**: `/data_manage/company_data_stats_simple`
- **请求方法**: `GET`
- **无需参数**: 直接访问即可

```bash
# GET 方式调用
curl -X GET "http://localhost:9029/data_manage/company_data_stats_simple"
```

## 性能说明

### 查询性能
- 使用数据库索引优化查询性能
- `status` 字段建议添加索引
- `update_time` 字段建议添加索引
- `StockAbbr` 字段建议添加索引

### 响应时间
- 正常情况下响应时间 < 1秒
- 大数据量情况下可能需要 2-5秒

### 并发支持
- 支持高并发查询
- 使用连接池管理数据库连接
- 自动处理连接超时和重连

## 错误处理

### 常见错误

1. **数据库连接失败**
   ```json
   {
       "code": 500,
       "message": "false",
       "data": {
           "error": "数据库连接失败",
           "error_type": "company_data_stats_error"
       }
   }
   ```

2. **查询超时**
   ```json
   {
       "code": 500,
       "message": "false",
       "data": {
           "error": "查询超时",
           "error_type": "company_data_stats_error"
       }
   }
   ```

### 错误处理建议
- 实现重试机制（建议重试 2-3 次）
- 设置合理的超时时间（建议 30 秒）
- 记录错误日志便于排查问题

## 监控指标

### 关键指标
- 接口响应时间
- 接口成功率
- 数据库查询时间
- 并发请求数

### 告警阈值建议
- 响应时间 > 5秒
- 成功率 < 95%
- 数据库查询时间 > 3秒

## 版本历史

| 版本 | 日期 | 变更内容 |
|------|------|----------|
| v1.0.0 | 2025-01-23 | 初始版本，提供基础统计功能 |

## 注意事项

1. **数据实时性**: 统计数据基于查询时刻的数据库状态
2. **时区处理**: 当日更新统计基于服务器时区
3. **数据一致性**: 在高并发更新场景下，统计数据可能存在微小延迟
4. **权限控制**: 生产环境建议添加适当的访问权限控制

## 技术实现

### 架构层次
- **API层**: `data_manage_route.py` - 处理HTTP请求
- **Service层**: `CompanyDataStatsService.get_company_data_statistics()` - 业务逻辑
- **数据层**: `SQLUtil.count_records_with_condition()` - 数据库操作

### 依赖组件
- FastAPI: Web框架
- SQLAlchemy: ORM框架
- SQLUtil: 数据库工具类
- LogUtil: 日志工具类
