from datetime import datetime
from sqlalchemy import Column, String, Text, Integer, DateTime
from sqlalchemy.ext.declarative import declarative_base

Base = declarative_base()

class CompanyMain(Base):
    """企业主信息表 SQLAlchemy ORM 模型"""
    
    __tablename__ = "CompanyMain"
    
    CompanyCode = Column(String(100), primary_key=True, comment="企业代码")
    ChiName = Column(String(100), nullable=False, comment="中文名称")
    ChiNameAbbr = Column(String(100), nullable=True, comment="企业别称")
    PreName = Column(Text, nullable=True, comment="曾用名")
    EngName = Column(String(200), nullable=True, comment="英文全称")
    EngNameAbbr = Column(String(100), nullable=True, comment="英文简称")
    StockAbbr = Column(String(100), nullable=True, comment="股票简称")
    BrandName = Column(String(500), nullable=True, comment="品牌名称")
    LargeModelAbbr = Column(String(100), nullable=True, comment="大模型简称")
    OtherAbbr = Column(String(100), nullable=True, comment="其他简称")
    CreditCode = Column(String(255), nullable=True, comment="信用代码")
    create_time = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", comment="创建时间")
    update_time = Column(DateTime, nullable=False, server_default="CURRENT_TIMESTAMP", onupdate="CURRENT_TIMESTAMP", comment="更新时间")
    status = Column(Integer, nullable=False, default=1, comment="状态：0-删除，1-正常")
