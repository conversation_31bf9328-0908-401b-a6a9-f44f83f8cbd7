#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/01/23 12:30:00
# <AUTHOR> Assistant
# @File         : data_manage_service.py
# @Description  : 数据管理服务层
"""
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
import io
from datetime import datetime, date
from typing import List, Dict, Any

from sqlalchemy import exists

from entity.mysql_entity import CompanyMain
from utils.sql_util import SQLUtil
from utils.log_util import LogUtil
import pandas as pd
from pandas.core.frame import DataFrame

from utils.uuid_util import UuidUtil
from utils.file_content_construction_excel import write_formated_excel


class CompanyDataStatsService:
    """公司数据统计服务类"""

    @staticmethod
    def get_company_data_statistics():
        """
        获取公司数据统计信息
        :return: 包含总量、上市公司总数、当日更新总数的字典
        """
        try:
            # 连接数据库
            SQLUtil.connect()

            # 1. 查询公司数据总量（status=1的记录）
            total_count = SQLUtil.count_records_with_condition(
                CompanyMain,
                CompanyMain.status == 1
            )

            # 2. 查询上市公司总数（StockAbbr不为空且不为null的记录）
            # 使用 query_by_column 查询所有记录，然后过滤
            all_companies = SQLUtil.query_by_column(
                CompanyMain,
                "status",
                1,
                exact_match=True
            )

            listed_count = 0
            for company in all_companies:
                if (company.StockAbbr and
                    company.StockAbbr.strip() and
                    company.StockAbbr.strip().upper() != 'NULL'):
                    listed_count += 1

            # 3. 查询当日更新总数（update_time >= 今日0点的记录）
            today = date.today()
            today_start = datetime.combine(today, datetime.min.time())

            today_updated_count = 0
            for company in all_companies:
                if company.update_time >= today_start:
                    today_updated_count += 1

            result = {
                "total_count": total_count,
                "listed_count": listed_count,
                "today_updated_count": today_updated_count,
                "query_date": today.strftime("%Y-%m-%d")
            }

            LogUtil.info(f"公司数据统计查询成功: {result}")
            return result

        except Exception as e:
            error_msg = f"查询公司数据统计失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            # 关闭数据库连接
            try:
                SQLUtil.close()
            except:
                pass


class CompanyUpdateService:
    """公司数据修改服务类"""

    @staticmethod
    def update_company_info(company_code: str, chi_name: str, chi_name_abbr: str = None, eng_name: str = None):
        """
        修改公司信息
        :param company_code: 企业编号
        :param chi_name: 中文名称（必填）
        :param chi_name_abbr: 企业别称（可选）
        :param eng_name: 英文全称（可选）
        :return: 修改结果字典
        """
        try:
            # 连接数据库
            SQLUtil.connect()

            # 1. 验证输入参数
            if not company_code or not company_code.strip():
                raise ValueError("企业编号不能为空")

            if not chi_name or not chi_name.strip():
                raise ValueError("中文名称不能为空")

            company_code = company_code.strip()
            chi_name = chi_name.strip()

            # 2. 检查公司是否存在（复用 get_company_info 方法）
            CompanyUpdateService.get_company_info(company_code)

            # 3. 检查新的中文名称是否与其他公司重复（复用 check_chi_name_duplicate 方法）
            if CompanyUpdateService.check_chi_name_duplicate(chi_name, company_code):
                raise ValueError(f"中文名称 '{chi_name}' 已被其他公司使用")

            # 4. 获取现有公司数据（重新查询以获取完整的 ORM 对象）
            existing_companies = SQLUtil.query_by_column(
                CompanyMain,
                "CompanyCode",
                company_code,
                exact_match=True,
                limit=1
            )
            existing_company = existing_companies[0]

            # 4. 获取原有数据
            old_chi_name = existing_company.ChiName
            old_pre_name = existing_company.PreName or ""

            # 5. 处理曾用名逻辑
            new_pre_name = CompanyUpdateService._update_pre_name(old_pre_name, old_chi_name, chi_name)

            # 6. 准备更新数据
            update_data = {
                "ChiName": chi_name,
                "PreName": new_pre_name,
                "update_time": datetime.now()
            }

            # 处理可选字段
            if chi_name_abbr is not None:
                update_data["ChiNameAbbr"] = chi_name_abbr.strip() if chi_name_abbr else None

            if eng_name is not None:
                update_data["EngName"] = eng_name.strip() if eng_name else None

            # 7. 执行更新
            updated_company = SQLUtil.update_by_id(
                CompanyMain,
                company_code,
                update_data
            )

            if not updated_company:
                raise Exception("数据库更新失败")

            result = {
                "company_code": company_code,
                "old_chi_name": old_chi_name,
                "new_chi_name": chi_name,
                "chi_name_abbr": updated_company.ChiNameAbbr,
                "eng_name": updated_company.EngName,
                "pre_name": new_pre_name,
                "update_time": updated_company.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                "message": "公司信息修改成功"
            }

            LogUtil.info(f"公司信息修改成功: {result}")
            return result

        except ValueError as e:
            error_msg = str(e)
            LogUtil.error(f"参数验证失败: {error_msg}")
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"修改公司信息失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            # 关闭数据库连接
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def _update_pre_name(old_pre_name: str, old_chi_name: str, new_chi_name: str) -> str:
        """
        更新曾用名逻辑
        :param old_pre_name: 原有曾用名
        :param old_chi_name: 原有中文名称
        :param new_chi_name: 新的中文名称
        :return: 更新后的曾用名
        """
        # 如果新名称和旧名称相同，不需要更新曾用名
        if old_chi_name == new_chi_name:
            return old_pre_name

        # 解析现有的曾用名
        pre_names = []
        if old_pre_name:
            pre_names = [name.strip() for name in old_pre_name.split(',') if name.strip()]

        # 添加原有中文名称到曾用名中
        if old_chi_name and old_chi_name not in pre_names:
            pre_names.append(old_chi_name)

        # 去重并保持顺序
        unique_pre_names = []
        for name in pre_names:
            if name not in unique_pre_names and name != new_chi_name:
                unique_pre_names.append(name)

        # 返回逗号分隔的字符串
        return ','.join(unique_pre_names) if unique_pre_names else None

    @staticmethod
    def get_company_info(company_code: str):
        """
        获取公司信息
        :param company_code: 企业编号
        :return: 公司信息字典
        """
        try:
            SQLUtil.connect()

            if not company_code or not company_code.strip():
                raise ValueError("企业编号不能为空")

            companies = SQLUtil.query_by_column(
                CompanyMain,
                "CompanyCode",
                company_code.strip(),
                exact_match=True,
                limit=1
            )

            if not companies or companies[0].status != 1:
                raise ValueError(f"企业编号 {company_code} 不存在或已被删除")

            company = companies[0]

            result = {
                "company_code": company.CompanyCode,
                "chi_name": company.ChiName,
                "chi_name_abbr": company.ChiNameAbbr,
                "pre_name": company.PreName,
                "eng_name": company.EngName,
                "eng_name_abbr": company.EngNameAbbr,
                "stock_abbr": company.StockAbbr,
                "brand_name": company.BrandName,
                "large_model_abbr": company.LargeModelAbbr,
                "other_abbr": company.OtherAbbr,
                "credit_code": company.CreditCode,
                "create_time": company.create_time.strftime('%Y-%m-%d %H:%M:%S'),
                "update_time": company.update_time.strftime('%Y-%m-%d %H:%M:%S'),
                "status": company.status
            }

            return result

        except ValueError as e:
            error_msg = str(e)
            LogUtil.error(f"参数验证失败: {error_msg}")
            raise ValueError(error_msg)
        except Exception as e:
            error_msg = f"查询公司信息失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def check_chi_name_duplicate(chi_name: str, exclude_company_code: str = None):
        """
        检查中文名称是否重复
        :param chi_name: 中文名称
        :param exclude_company_code: 排除的企业编号
        :return: 是否重复
        """
        try:
            SQLUtil.connect()

            if not chi_name or not chi_name.strip():
                return False

            # 查询所有同名的公司
            duplicate_companies = SQLUtil.query_by_column(
                CompanyMain,
                "ChiName",
                chi_name.strip(),
                exact_match=True
            )

            # 过滤条件：状态为1且不是排除的公司
            for company in duplicate_companies:
                if company.status == 1:
                    if not exclude_company_code or company.CompanyCode != exclude_company_code.strip():
                        return True

            return False

        except Exception as e:
            LogUtil.error(f"检查名称重复失败: {str(e)}")
            return False
        finally:
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def batch_update_companies(companies_data: List[Dict[str, Any]]):
        """
        批量修改公司信息（先检查后更新，任何一个数据不符合就返回错误）
        复用 update_company_info 接口进行实际更新操作
        :param companies_data: 公司修改数据列表
        :return: 批量修改结果字典
        """
        try:
            # 1. 数据预处理和验证（遇到错误立即返回）
            validated_companies = []

            for i, company_data in enumerate(companies_data):
                try:
                    # 基础验证
                    company_code = company_data.get('company_code', '').strip()
                    chi_name = company_data.get('chi_name', '').strip()

                    if not company_code:
                        return {
                            "success": False,
                            "error_index": i,
                            "error_company_code": company_code,
                            "error_message": "企业编号不能为空",
                            "error_type": "validation_error",
                            "total_count": len(companies_data),
                            "processed_count": i
                        }

                    if not chi_name:
                        return {
                            "success": False,
                            "error_index": i,
                            "error_company_code": company_code,
                            "error_message": "中文名称不能为空",
                            "error_type": "validation_error",
                            "total_count": len(companies_data),
                            "processed_count": i
                        }

                    validated_companies.append({
                        "index": i,
                        "company_code": company_code,
                        "chi_name": chi_name,
                        "chi_name_abbr": company_data.get('chi_name_abbr'),
                        "eng_name": company_data.get('eng_name')
                    })

                except Exception as e:
                    return {
                        "success": False,
                        "error_index": i,
                        "error_company_code": company_data.get('company_code', ''),
                        "error_message": f"数据格式错误: {str(e)}",
                        "error_type": "format_error",
                        "total_count": len(companies_data),
                        "processed_count": i
                    }

            # 2. 批量检查阶段 - 复用 update_company_info 的验证逻辑
            # 检查公司存在性和名称重复性
            names_to_check = {}

            for company in validated_companies:
                company_code = company['company_code']
                chi_name = company['chi_name']

                try:
                    # 使用现有方法进行检查，复用验证逻辑

                    # 检查公司是否存在
                    CompanyUpdateService.get_company_info(company_code)

                    # 检查名称重复
                    if CompanyUpdateService.check_chi_name_duplicate(chi_name, company_code):
                        return {
                            "success": False,
                            "error_index": company['index'],
                            "error_company_code": company_code,
                            "error_message": f"中文名称 '{chi_name}' 已被其他公司使用",
                            "error_type": "duplicate_error",
                            "total_count": len(companies_data),
                            "processed_count": company['index']
                        }

                    # 检查批量数据内部的重复
                    if chi_name in names_to_check:
                        return {
                            "success": False,
                            "error_index": company['index'],
                            "error_company_code": company_code,
                            "error_message": f"中文名称 '{chi_name}' 在批量数据中重复（与索引 {names_to_check[chi_name]} 重复）",
                            "error_type": "internal_duplicate_error",
                            "total_count": len(companies_data),
                            "processed_count": company['index'],
                            "duplicate_with_index": names_to_check[chi_name]
                        }
                    else:
                        names_to_check[chi_name] = company['index']

                except ValueError as e:
                    # 捕获验证错误（包括公司不存在等）
                    return {
                        "success": False,
                        "error_index": company['index'],
                        "error_company_code": company_code,
                        "error_message": str(e),
                        "error_type": "validation_error",
                        "total_count": len(companies_data),
                        "processed_count": company['index']
                    }
                except Exception as e:
                    return {
                        "success": False,
                        "error_index": company['index'],
                        "error_company_code": company_code,
                        "error_message": f"检查失败: {str(e)}",
                        "error_type": "check_error",
                        "total_count": len(companies_data),
                        "processed_count": company['index']
                    }

            # 3. 所有检查通过，执行批量更新 - 复用 update_company_info 接口
            updated_results = []

            for company in validated_companies:
                try:
                    # 直接调用 update_company_info 进行更新
                    result = CompanyUpdateService.update_company_info(
                        company_code=company['company_code'],
                        chi_name=company['chi_name'],
                        chi_name_abbr=company['chi_name_abbr'],
                        eng_name=company['eng_name']
                    )

                    updated_results.append({
                        "index": company['index'],
                        "company_code": company['company_code'],
                        "old_chi_name": result.get('old_chi_name'),
                        "new_chi_name": result.get('new_chi_name'),
                        "update_time": result.get('update_time')
                    })

                except Exception as e:
                    # 如果更新过程中出现错误，立即返回
                    return {
                        "success": False,
                        "error_index": company['index'],
                        "error_company_code": company['company_code'],
                        "error_message": f"更新失败: {str(e)}",
                        "error_type": "update_error",
                        "total_count": len(companies_data),
                        "processed_count": company['index'],
                        "updated_count": len(updated_results)
                    }

            # 4. 返回成功结果
            return {
                "success": True,
                "total_count": len(companies_data),
                "updated_count": len(updated_results),
                "updated_companies": updated_results,
                "message": f"批量更新成功，共更新 {len(updated_results)} 家公司"
            }

        except Exception as e:
            error_msg = f"批量修改公司信息失败: {str(e)}"
            LogUtil.error(error_msg)
            return {
                "success": False,
                "error_message": error_msg,
                "error_type": "system_error",
                "total_count": len(companies_data) if companies_data else 0,
                "processed_count": 0,
                "updated_count": 0
            }


class CompanyDataUploadService:

    @staticmethod
    def validate(excel_df: DataFrame) -> bool:
        """
        对新增数据的CreditCode、ChiName字段进行查重
        :param excel_df:
        :return:
        """

        try:
            SQLUtil.connect()

            duplicate_rows_df = DataFrame()
            for i in range(len(excel_df)):
                row = excel_df.iloc[i]
                credit_code = row.get("CreditCode")
                chi_name = row.get("ChiName")

                if credit_code is None or str(credit_code).strip() == "":
                    raise ValueError(f"以下公司的'CreditCode'列不能为空，请检查还有没有类似数据。\n\n{row}")

                if chi_name is None or str(chi_name).strip() == "":
                    raise ValueError(f"以下公司的'ChiName'列不能为空，请检查还有没有类似数据。\n\n{row}")

                # 查数据库，检查CreditCode、ChiName字段数据是否重复
                if SQLUtil.get_session().query(
                    exists().where(
                        (
                            (CompanyMain.CreditCode == credit_code) |
                            (CompanyMain.ChiName == chi_name)
                        ) &
                        (CompanyMain.status == 1)
                    )
                ).scalar():
                    duplicate_rows_df = duplicate_rows_df._append(row)

            if len(duplicate_rows_df) > 0:
                raise ValueError(
                    f"以下公司的'CreditCode'或'ChiName'列与数据库中数据重复：\n\n{duplicate_rows_df.to_string()}")

            return True

        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        finally:
            try:
                SQLUtil.close()
            except Exception as e:
                raise Exception(e)

    @staticmethod
    def upload(excel_df: DataFrame) -> bool:
        """
        数据上传至 Mysql
        :param excel_df:
        :return:
        """

        try:
            SQLUtil.connect()

            data_dict_list = []
            for i in range(len(excel_df)):
                row = excel_df.iloc[i]
                column_name_list = SQLUtil.get_table_columns(CompanyMain)

                data_dict = {
                    "CompanyCode": UuidUtil.get_uuid()
                }
                for column_name in column_name_list:
                    column_value = row.get(column_name)
                    if column_value is not None:
                        data_dict[column_name] = column_value if pd.notna(column_value) else None

                data_dict_list.append(data_dict)

            SQLUtil.insert_many(CompanyMain, data_dict_list)

            return True

        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        finally:
            try:
                SQLUtil.close()
            except Exception as e:
                raise Exception(e)


class CompanyDataDownloadService:

    @staticmethod
    def remove_deleted_item(
        dict_list,
        status_key="status",
        status_deleted_value=0
    ) -> List[Dict[str, Any]]:
        """

        :param dict_list:
        :param status_key:
        :param status_deleted_value:
        :return:
        """

        new_dict_list = []
        for item in dict_list:
            status_value = item.get(status_key)
            if status_value is not None and status_value == status_deleted_value:
                continue
            new_dict_list.append(item)
        return new_dict_list

    @staticmethod
    def remove_attr(
        dict_list,
        remove_key_list=["create_time", "update_time", "status"]
    ) -> List[Dict[str, Any]]:
        """
        过滤掉指定字段
        :param dict_list:
        :param remove_key_list: 待移除的字段列表
        :return:
        """

        new_dict_list = []
        for item in dict_list:
            new_item = {}
            for key in item.keys():
                if key in remove_key_list:
                    continue
                new_item[key] = item[key]
            new_dict_list.append(new_item)
        return new_dict_list

    @staticmethod
    def filter_dict_list(dict_list) -> List[Dict[str, Any]]:

        dict_list = CompanyDataDownloadService.remove_deleted_item(dict_list)
        dict_list = CompanyDataDownloadService.remove_attr(dict_list)

        return dict_list

    @staticmethod
    def download(company_code_list: List[str]) -> io.BytesIO:
        """
        从 Mysql 下载指定公司编号的数据
        :param company_code_list: 公司编号列表
        :return:
        """

        try:
            SQLUtil.connect()

            res_models = SQLUtil.get_data_by_ids(CompanyMain, company_code_list)
            res_dict_list = SQLUtil.models_to_list(res_models)
            res_dict_list = CompanyDataDownloadService.filter_dict_list(res_dict_list)

            df = pd.DataFrame(res_dict_list)

            output = io.BytesIO()
            with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                write_formated_excel(writer, df)
            output.seek(0)

            return output

        except Exception as e:
            LogUtil.error(f"{str(e)}")
            raise Exception(e)
        finally:
            try:
                SQLUtil.close()
            except Exception as e:
                raise Exception(e)



class DuplicateCompanyError(Exception):
    """公司数据重复异常"""
    def __init__(self, detail: str):
        self.detail = detail
        super().__init__(detail)

class CompanyAddService:
    """公司数据新增服务类"""

    @staticmethod
    def add_companies(company_data_list: list[dict]) -> dict:
        """
        批量新增公司数据
        """
        try:
            SQLUtil.connect()
            results = []

            for company_data in company_data_list:
                try:
                    # 验证数据有效性
                    result = CompanyAddService.add_single_company(company_data)
                    results.append(result)
                except DuplicateCompanyError as e:
                    results.append({
                        "ChiName": company_data.get("ChiName"),
                        "CreditCode": company_data.get("CreditCode"),
                        "message": str(e),
                        "success": False
                    })
                except ValueError as e:
                    results.append({
                        "ChiName": company_data.get("ChiName"),
                        "CreditCode": company_data.get("CreditCode"),
                        "message": f"参数错误: {str(e)}",
                        "success": False
                    })

            # 计算统计信息
            success_count = sum(1 for r in results if r["success"])
            failed_count = len(results) - success_count

            return {
                "total": len(results),
                "success_count": success_count,
                "failed_count": failed_count,
                "details": results
            }

        finally:
            SQLUtil.close()

    @staticmethod
    def add_single_company(company_data: dict) -> dict:
        """
        新增单个公司数据

        Args:
            company_data: 公司数据对象

        Returns:
            dict: 新增结果信息
        """
        LogUtil.info(f"处理新增公司: {company_data['ChiName']}, 统一社会信用代码: {company_data['CreditCode']}")

        # 查重检查
        CompanyAddService._validate_company_unique(
            company_data["CreditCode"],
            company_data["ChiName"]
        )

        # 准备插入数据
        current_time = datetime.now()
        insert_data = CompanyAddService._prepare_company_data(company_data, current_time)

        # 生成CompanyCode
        company_code = CompanyAddService._generate_company_code()
        insert_data["CompanyCode"] = company_code

        # 插入数据库
        SQLUtil.insert_one(CompanyMain, insert_data)

        # 构建结果
        return {
            "CompanyCode": company_code,
            "ChiName": company_data["ChiName"],
            "CreditCode": company_data["CreditCode"],
            "message": "新增成功",
            "success": True
        }

    @staticmethod
    def _validate_company_unique(credit_code: str, chi_name: str) -> None:
        """
        验证公司数据唯一性

        Args:
            credit_code: 统一社会信用代码
            chi_name: 中文名称

        Raises:
            DuplicateCompanyError: 公司数据已存在时抛出
        """
        # 检查CreditCode是否存在
        if SQLUtil.exists(CompanyMain, CreditCode=credit_code):
            raise DuplicateCompanyError(f"统一社会信用代码已存在: {credit_code}")

        # 检查ChiName是否存在
        if SQLUtil.exists(CompanyMain, ChiName=chi_name):
            raise DuplicateCompanyError(f"公司名称已存在: {chi_name}")

    @staticmethod
    def _prepare_company_data(company_data: dict, current_time: datetime) -> dict:
        """
        准备公司数据字典

        Args:
            company_data: 公司数据对象
            current_time: 当前时间

        Returns:
            dict: 准备好的数据库插入数据
        """
        return {
            "ChiName": company_data["ChiName"],
            "ChiNameAbbr": company_data.get("ChiNameAbbr"),
            "PreName": company_data.get("PreName"),
            "EngName": company_data.get("EngName"),
            "EngNameAbbr": company_data.get("EngNameAbbr"),
            "StockAbbr": company_data.get("StockAbbr"),
            "BrandName": company_data.get("BrandName"),
            "LargeModelAbbr": company_data.get("LargeModelAbbr"),
            "OtherAbbr": company_data.get("OtherAbbr"),
            "CreditCode": company_data["CreditCode"],
            "status": 1,
            "create_time": current_time,
            "update_time": current_time
        }

    @staticmethod
    def _generate_company_code() -> str:
        """
        生成公司编码（格式：C+9位数字，如C000000001）

        Returns:
            str: 生成的公司编码
        """
        current_max = SQLUtil.get_max_value(CompanyMain, "CompanyCode", "C")
        if current_max:
            current_num = int(current_max[1:])
            new_num = current_num + 1
        else:
            new_num = 1
        return f"C{new_num:09d}"

    @staticmethod
    def convert_to_dict(data: dict) -> dict:
        """将请求数据转换为统一的字典格式"""
        return {
            "ChiName": data["ChiName"],
            "ChiNameAbbr": data.get("ChiNameAbbr"),
            "PreName": data.get("PreName"),
            "EngName": data.get("EngName"),
            "EngNameAbbr": data.get("EngNameAbbr"),
            "StockAbbr": data.get("StockAbbr"),
            "BrandName": data.get("BrandName"),
            "LargeModelAbbr": data.get("LargeModelAbbr"),
            "OtherAbbr": data.get("OtherAbbr"),
            "CreditCode": data["CreditCode"],
        }

class CompanyDelService:
    """公司数据删除服务类"""

    @staticmethod
    def delete_companies(company_list):
        """
        批量删除公司数据（逻辑删除）

        Args:
            company_list: 包含公司列表的请求对象

        Returns:
            dict: 删除结果统计和详细信息
        """
        try:
            # 连接数据库
            SQLUtil.connect()

            # 获取当前时间
            current_time = datetime.now()
            print("current company_list:", company_list)
            # 处理每个公司的删除请求
            results = []
            for company in company_list:
                result = CompanyDelService._delete_single_company(
                    company.CreditCode, current_time)
                results.append(result)

            # 检查是否所有操作都失败
            CompanyDelService._check_all_failed(results)

            return {
                "total": len(results),
                "success_count": sum(1 for r in results if r["success"]),
                "failed_count": sum(1 for r in results if not r["success"]),
                "details": results
            }

        except Exception as e:
            error_msg = f"批量删除公司数据失败: {str(e)}"
            LogUtil.error(error_msg)
            raise Exception(error_msg)
        finally:
            # 关闭数据库连接
            try:
                SQLUtil.close()
            except:
                pass

    @staticmethod
    def _delete_single_company(credit_code: str, current_time: datetime) -> dict:
        """
        删除单个公司数据

        Args:
            credit_code: 公司统一社会信用代码
            current_time: 当前时间戳

        Returns:
            dict: 删除结果信息
        """
        LogUtil.info(f"处理删除公司: 统一社会信用代码: {credit_code}")

        # 检查公司是否存在
        company_exists = SQLUtil.exists(
            CompanyMain,
            CreditCode=credit_code
        )

        if not company_exists:
            result = {
                "CreditCode": credit_code,
                "message": "删除失败，公司不存在",
                "success": False
            }
            LogUtil.warn(f"公司 {credit_code} 不存在")
            return result

        # 准备更新数据
        update_data = {
            "status": 0,  # 设置为删除状态
            "update_time": current_time  # 更新时间戳
        }

        # 执行更新
        updated_rows = SQLUtil.update(
            CompanyMain,
            {"CreditCode": credit_code, "status": 1},
            update_data
        )

        if updated_rows > 0:
            result = {
                "CreditCode": credit_code,
                "message": "删除成功",
                "success": True
            }
            LogUtil.info(f"公司 {credit_code} 删除成功")
        else:
            result = {
                "CreditCode": credit_code,
                "message": "删除失败，未找到匹配记录",
                "success": False
            }
            LogUtil.warn(f"公司 {credit_code} 删除失败")

        return result

    @staticmethod
    def _check_all_failed(results: list):
        """检查是否所有操作都失败，如果是则抛出异常"""
        all_failed = all(not item["success"] for item in results)
        if all_failed:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="所有删除操作均失败，未找到匹配的公司记录"
            )
        
## 公司数据查询服务类
class CompanySearchService(object):

    ## 按照社会统一信用代码查询
    @staticmethod
    def query_company_by_creditcode(db: Session, creditcode: str, page_num: int,page_size: int):
        return db.query(CompanyMain).filter(CompanyMain.CreditCode == creditcode,CompanyMain.status == 1).offset((page_num - 1) * page_size).limit(page_size).all()
    
    ## 查询所有公司
    @staticmethod
    def query_all_company(db: Session, page_num: int,page_size: int):
        return db.query(CompanyMain).filter(CompanyMain.status == 1).order_by(CompanyMain.CompanyCode).offset((page_num - 1) * page_size).limit(page_size).all()
    
    ## 按照公司名称查询
    @staticmethod
    def query_company_by_name(db: Session, company_name:str, page_num: int,page_size: int):
        search = company_name + "*"
        return db.query(CompanyMain).filter(
            CompanyMain.status == 1,
            text("MATCH(ChiName, ChiNameAbbr, PreName, EngName,StockAbbr) AGAINST(:search_term IN BOOLEAN MODE)")
            ).params(search_term=search)\
        .order_by(CompanyMain.CompanyCode).offset((page_num - 1) * page_size).limit(page_size).all()