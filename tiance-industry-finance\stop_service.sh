#!/bin/bash

# 查找进程的 PID
#lsof logs/service.log | awk 'NR > 1 {print $2}' | uniq | xargs kill -9
#lsof logs/tiance-base.logs | awk 'NR > 1 {print $2}' | uniq | xargs kill -9
pkill -f "uvicorn.*--port 9080"
PIDS=$(lsof logs/service.log | awk 'NR > 1 {print $2}' | uniq)
if [ -n "$PIDS" ]; then
    echo "$PIDS" | xargs kill -9
fi
PIDS2=$(lsof logs/tiance-base.logs | awk 'NR > 1 {print $2}' | uniq)
if [ -n "$PIDS2" ]; then
    echo "$PIDS2" | xargs kill -9
fi
echo "服务结束成功"
