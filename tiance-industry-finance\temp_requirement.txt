pkuseg==0.0.25
rapidfuzz==3.13.0
wordninja==2.0.0

请你依照tiance-base 的数据格式，在tiance-industry-finance 开发如下接口，需要api，service以及model分离，风格参照tiance-base
4.	公司数据修改接口--修改公司名称，只可修改ChiName、ChiNameAbbr、EngName。ChiName入参不可为空，ChiName修改后自动将原有值补充到PreName中,PreName,中的数据是以','作为分割，需要去重，自动更新updatetime。修改ChiName需要对该字段查重。
这个是数据库DDL
-- auto-generated definition
create table CompanyMain
(
    CompanyCode    varchar(100)                         not null
        primary key,
    ChiName        varchar(100)                         not null comment '中文名称',
    ChiNameAbbr    varchar(100)                         null comment '企业别称',
    PreName        text                                 null comment '曾用名',
    EngName        varchar(200)                         null comment '英文全称',
    EngNameAbbr    varchar(100)                         null comment '英文简称',
    StockAbbr      varchar(100)                         null comment '股票简称',
    BrandName      varchar(500)                         null,
    LargeModelAbbr varchar(100)                         null comment '大模型简称',
    OtherAbbr      varchar(100)                         null comment '其他简称',
    CreditCode     varchar(255) charset utf8mb4         null,
    create_time    datetime   default CURRENT_TIMESTAMP not null comment '创建时间',
    update_time    datetime   default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
    status         tinyint(1) default 1                 not null comment '状态：0-删除，1-正常'
)
    comment '企业主信息表' collate = utf8mb4_unicode_ci;

create fulltext index CompanyMain_ChiName_IDX
    on CompanyMain (ChiName, ChiNameAbbr, PreName, EngName, EngNameAbbr, StockAbbr, BrandName, LargeModelAbbr,
                    OtherAbbr);

create index idx_companycode
    on CompanyMain (CompanyCode);
