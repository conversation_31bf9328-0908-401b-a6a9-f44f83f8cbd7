#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2025/4/29 16:41
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : cache_utils.py
# @Project : tiance-industry-finance
"""
from datetime import datetime
from typing import Any

from utils.log_util import LogUtil
from utils.mongodb_util import MongodbUtil


class CacheUtils(object):

    @staticmethod
    def get_cache(
        cache_collection_name: str,
        key: Any,
    ) -> Any:
        """
        从指定缓存集合取数据
        :param cache_collection_name:
        :param key:
        :return:
        """
        try:
            query_res = MongodbUtil.query_one_doc_by_condition(
                cache_collection_name,
                {
                    "request": key
                }
            )
            if query_res is None:
                return None
            return query_res.get("cached_data")

        except Exception as e:
            detail = f"失败详情：{str(e)}"
            LogUtil.error(msg=detail)
            return None

    @staticmethod
    def save_cache(
        cache_collection_name: str,
        key: Any,
        value: Any
    ) -> bool:
        """
        缓存数据到指定Mongodb集合
        :param cache_collection_name:
        :param key:
        :param value:
        :return:
        """

        try:
            # 先清空该key对应的缓存
            MongodbUtil.del_docs_by_condition(
                cache_collection_name,
                {
                    "request": key
                }
            )

            # 缓存
            MongodbUtil.insert_one(
                cache_collection_name,
                {
                    "request": key,
                    "cached_data": value,
                    "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            )
            return True

        except Exception as e:
            detail = f"失败详情：{str(e)}"
            LogUtil.error(msg=detail)
            return False
