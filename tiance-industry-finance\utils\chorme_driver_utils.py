#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time         : 2025/05/20 17:29
# <AUTHOR> <PERSON><PERSON><PERSON><PERSON>
# @File         : chorme_driver_utils.py
# @Description  : 谷歌浏览器驱动工具
"""

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from configs.news_crawl_config import NewsCrawlConfig

class ChromeDriver:
    def __init__(self, driver_path=NewsCrawlConfig.CHORME_DRIVER_PATH):
        self.driver_path = driver_path
        self.options = self._create_options()
        self.service = Service(executable_path=self.driver_path)
        self._driver = webdriver.Chrome(service=self.service, options=self.options)

    def _create_options(self):
        options = Options()
        options.add_argument("--headless=new")  # 新版 Chrome 推荐方式
        options.add_argument("--disable-gpu")   # 避免可能的 GPU 问题
        return options

    def close_driver(self):
        if self._driver:
            self._driver.quit()

    def __getattr__(self, name):
        return getattr(self._driver, name)

# 使用示例
if __name__ == "__main__":
    chrome_driver = ChromeDriver()
    chrome_driver.get("https://www.sohu.com/a/835197749_120956897")
    print(chrome_driver.page_source)
    # 现在你可以使用chrome_driver对象来操作浏览器
    # ...
    chrome_driver.close_driver()
