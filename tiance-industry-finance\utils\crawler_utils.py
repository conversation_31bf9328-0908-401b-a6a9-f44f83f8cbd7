# -*- coding: utf-8 -*-
"""
# @Time    : 2025/5/13 10:58
# <AUTHOR> hejunjie
# @Email   : <EMAIL>
# @File    : crawler_utils.py
# @Project : tiance-industry-finance
"""
import time

import requests


def get_url_response(url, headers=None, method='GET', params=None, data=None, retry_num=3, sleep_time=None,
                     pause_time=185):
    """
    @param url:
    @param headers:
    @param method:
    @param params:
    @param data:
    @param retry_num: 重试次数
    @param sleep_time:
    @param pause_time: 遇到403错误时的暂停时间
    @return: 状态码为200的response
    """
    res_data = None
    while retry_num:
        retry_num -= 1
        try:
            if sleep_time:
                time.sleep(sleep_time)
            response = requests.request(method=method, url=url, headers=headers, params=params, data=data,
                                        # proxies=proxies,  #
                                        verify=False)
            if response.status_code == 200:
                res_data = response
                return res_data
            elif response.status_code == 403:
                # 被拦截
                time.sleep(pause_time)
            else:
                print(f'get_url_response() 未知错误代码, response.status_code: {response.status_code}, url: {url}')
        except Exception as e:
            print(f'get_url_response() 请求失败, url: {url}, 失败详情：{str(e)}')
    return res_data
