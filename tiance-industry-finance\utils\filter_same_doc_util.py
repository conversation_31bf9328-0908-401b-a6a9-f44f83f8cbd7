﻿#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@Time    :   2025/05/12 19:55:49
<AUTHOR>   W<PERSON><PERSON><PERSON> HONG 
@Email    :   <EMAIL>
@File    :   filter_same_doc_util.py
@Project    :   tiance-industry-finance
'''
def filterSameDocs(childs):
    chunks = []
    souce_test_list = []
    # print(childs)
    #通过url去重
    file_list_url = set()
    # 生成上下文列表
    for child in childs:
        # print(source_file)
        if child["file_url"] not in file_list_url :
            file_list_url.add(child["file_url"])
            chunks.append(child["chunk_content"])
            souce_test_list.append(child)
    return souce_test_list, chunks