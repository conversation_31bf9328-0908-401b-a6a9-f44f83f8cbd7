from utils.mongodb_util import MongodbUtil
import csv
from typing import Dict, List, Any, Set, Tuple

# 构造”低空经济“图谱的数据整理
# 包括构造节点、连接关系、三元组结构

def get_industry(docs: Dict[str, Any]) -> List[Dict[str, Any]]:
    '''
    功能：获取行业节点，对其进行id化存储
    参数：docs - MongoDB查询结果字典
    '''
    # 提取行业名称
    industry = docs.get('industry', '')
    if not industry:
        print("错误：文档中缺少'industry'字段")
        return []

    # 生成行业ID并存储
    industry_nodes = [
        {
            "id": "industry_1",
            "name": industry,
            "type": "industry"
        }
    ]

    # 写入节点文件
    with open("E:/data/Knowledge_Graph/industry_nodes.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["id", "name", "type"])
        writer.writeheader()
        writer.writerows(industry_nodes)

    print(f"已生成 1 个行业节点")
    return industry_nodes


def split_segment_path(path: str) -> Tuple[str, str, List[str]]:
    '''
    功能：解析环节路径，返回行业、关系和环节列表
    参数：path - 环节路径字符串("行业|关系|一级环节|二级环节|...")
    返回：(行业, 关系, [环节列表])
    '''
    parts = path.split("|")
    if len(parts) < 3:
        # 至少需要"行业|关系|环节"
        return parts[0], "", parts[1:] if len(parts) > 2 else []

    industry = parts[0]
    relation = parts[1]
    segments = parts[2:]

    return industry, relation, segments


def get_segment(docs: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    '''
    功能：获取所有产业链环节的列表--构造节点文件
         获取行业节点to环节节点的关系连接--构造关系文件
    参数：docs - MongoDB查询结果字典
    '''
    # 提取行业名称
    industry = docs.get('industry', '')
    if not industry:
        print("错误：文档中缺少'industry'字段")
        return [], []

    # 环节注册表：{环节名称: 环节ID}
    segment_registry: Dict[str, str] = {}

    # 环节节点和关系
    segment_nodes: List[Dict[str, Any]] = []
    relations: List[Dict[str, Any]] = []

    # 行业节点
    industry_nodes = get_industry(docs)
    industry_id = industry_nodes[0]["id"] if industry_nodes else "industry_1"

    # 环节ID计数器
    segment_id_counter = 1

    # 处理每个环节路径
    node_companies = docs.get('node_companies', {})
    for segment_path in node_companies.keys():
        # 解析环节路径
        path_industry, relation_type, segments = split_segment_path(segment_path)

        if not segments:
            continue

        parent_id = industry_id
        parent_name = industry

        # 构建环节层级
        for level, segment_name in enumerate(segments, start=1):
            # 检查环节是否已存在
            if segment_name in segment_registry:
                segment_id = segment_registry[segment_name]
            else:
                # 创建新环节
                segment_id = f"segment_{segment_id_counter}"
                segment_id_counter += 1

                # 注册新环节
                segment_registry[segment_name] = segment_id

                # 添加环节节点
                segment_nodes.append({
                    "id": segment_id,
                    "name": segment_name,
                    "level": level,
                    "type": "segment"
                })

            # 添加行业到一级环节的关系
            if level == 1:
                # 避免重复添加关系
                if not any(
                        r["source"] == industry_id and
                        r["target"] == segment_id and
                        r["relation"] == relation_type
                        for r in relations
                ):
                    relations.append({
                        "source": industry_id,
                        "target": segment_id,
                        "type": "industry_to_segment",
                        "relation": relation_type
                    })
            else:
                # 添加上级环节到当前环节的组成关系
                # 避免重复添加关系
                if not any(
                        r["source"] == parent_id and
                        r["target"] == segment_id and
                        r["type"] == "segment_composition"
                        for r in relations
                ):
                    relations.append({
                        "source": parent_id,
                        "target": segment_id,
                        "type": "segment_composition",
                        "relation": "组成"
                    })

            # 更新父环节信息
            parent_id = segment_id
            parent_name = segment_name

    # 写入环节节点文件
    with open("E:/data/Knowledge_Graph/segment_nodes.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["id", "name", "level", "type"])
        writer.writeheader()
        writer.writerows(segment_nodes)

    # # 写入关系文件
    # with open("E:/data/Knowledge_Graph/industry_segment_relations.csv", "w", newline="", encoding="utf-8") as f:
    #     writer = csv.DictWriter(f, fieldnames=["source", "target", "type", "relation"])
    #     writer.writeheader()
    #     writer.writerows(relations)

    # 根据relation字段将关系写入不同的CSV文件
    write_relations_by_relation_type(relations, "E:/data/Knowledge_Graph/industry_segment_relations")

    print(f"已生成 {len(segment_nodes)} 个环节节点和 {len(relations)} 个关系")
    return segment_nodes, relations


def write_relations_by_relation_type(relations: List[Dict[str, Any]], base_path: str) -> None:
    '''
    功能：根据relation字段将关系写入不同的CSV文件
    参数：
        relations - 关系列表
        base_path - 文件保存的基础路径
    '''
    # 按relation字段分组关系
    relation_groups: Dict[str, List[Dict[str, Any]]] = {}

    for relation in relations:
        rel_type = relation.get("relation", "unknown")
        if rel_type not in relation_groups:
            relation_groups[rel_type] = []
        relation_groups[rel_type].append(relation)

    # 为每个relation类型创建单独的CSV文件
    for rel_type, rel_list in relation_groups.items():
        # 替换文件名中的非法字符
        safe_rel_type = rel_type.replace("/", "_").replace("|", "_")
        file_path = f"{base_path}_{safe_rel_type}.csv"

        with open(file_path, "w", newline="", encoding="utf-8") as f:
            writer = csv.DictWriter(f, fieldnames=["source", "target", "type", "relation"])
            writer.writeheader()
            writer.writerows(rel_list)

        print(f"已写入 {len(rel_list)} 条 {rel_type} 关系到 {file_path}")



def get_companies(docs: Dict[str, Any]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
    '''
    功能：获取所有企业的列表--构造节点文件
         获取环节节点to企业的关系连接--构造关系文件
    参数：docs - MongoDB查询结果字典
    '''
    # 提取所有企业信息
    company_nodes = []
    segment_to_company_relations = []

    # 先获取环节节点映射
    segment_nodes, _ = get_segment(docs)

    # 构建环节名称到ID的映射（考虑到可能有同名但不同层级的环节，使用完整路径作为键）
    segment_map = {}
    for node in segment_nodes:
        segment_map[node["name"]] = node["id"]

    company_id_counter = 1
    node_companies = docs.get('node_companies', {})

    for segment_path, companies in node_companies.items():
        # 解析环节路径
        _, _, segments = split_segment_path(segment_path)

        if not segments:
            continue

        # 使用最深层级的环节作为企业的关联环节
        deepest_segment = segments[-1]
        segment_id = segment_map.get(deepest_segment, f"segment_{deepest_segment}")

        # 处理每个企业
        for company_info in companies:
            company_id = f"company_{company_id_counter}"
            company_id_counter += 1

            # 添加企业节点
            company_nodes.append({
                "id": company_id,
                "name": company_info.get("name", ""),
                "abb": company_info.get("abb", ""),
                "is_listed": company_info.get("is_listed", ""),
                "is_special": company_info.get("is_special", ""),
                "is_high_tech": company_info.get("is_high_tech", ""),
                "type": "company"
            })

            # 添加环节到企业的关系
            segment_to_company_relations.append({
                "source": segment_id,
                "target": company_id,
                "type": "segment_to_company",
                "relation": "供应"
            })

    # 写入企业节点文件
    with open("E:/data/Knowledge_Graph/company_nodes.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["id", "name", "abb", "is_listed", "is_special", "is_high_tech", "type"])
        writer.writeheader()
        writer.writerows(company_nodes)

    # 写入关系文件
    with open("E:/data/Knowledge_Graph/segment_company_relations.csv", "w", newline="", encoding="utf-8") as f:
        writer = csv.DictWriter(f, fieldnames=["source", "target", "type", "relation"])
        writer.writeheader()
        writer.writerows(segment_to_company_relations)

    print(f"已生成 {len(company_nodes)} 个企业节点和 {len(segment_to_company_relations)} 个关系")
    return company_nodes, segment_to_company_relations

if __name__ == '__main__':
    MongodbUtil.connect()
    docs = MongodbUtil.query_doc_by_id(
        collection_name="key_companies",
        doc_id="cda34acb6fb246a9bd02abf880a85a62"
    )
    print(docs)

    # 执行数据处理
    get_industry(docs)
    get_segment(docs)
    get_companies(docs)



