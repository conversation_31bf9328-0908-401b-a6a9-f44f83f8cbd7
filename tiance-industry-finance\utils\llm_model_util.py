#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :llm_model_util.py
@Description  :大模型服务工具
<AUTHOR>
@Date         :2024/12/3 17:04:31
'''

import openai
from configs.model_config import ModelConfig
from typing import List, Dict
import base64
from PIL import Image
# 2025 0520 替换为prompt config
# from prompt_set.all_prompt import Default_Prompt as Prompts
from configs.prompt_config import PromptConfig
from io import BytesIO

class LlmModel(object):
    """
    大语言模型
    """
    @staticmethod
    def get_model_client():
        """
        获取模型客户端-同步
        :return:
        """
        # 返回模型客户端
        return openai.Client(api_key=ModelConfig.LLM_API_KEY, base_url=ModelConfig.LLM_API_BASE)

    @staticmethod
    def get_async_model_client():
        """
        获取模型客户端-异步
        :return:
        """
        # 返回模型客户端
        return openai.AsyncClient(api_key=ModelConfig.LLM_API_KEY, base_url=ModelConfig.LLM_API_BASE)



# 基础消息类
class Message:
    def __init__(self, role: str, content: str):
        self.role = role
        self.content = content

    def to_dict(self):
        return {
            "role": self.role,
            "content": self.content
        }

# 用户消息类
class UserMessage(Message):
    def __init__(self, content: str):
        super().__init__(role="user", content=content)


# 消息转换器类
class MessageConverter:
    # 将字典列表转换为类实例列表，再转换回字典列表的方法
    @staticmethod
    def convert_messages(messages) -> list[dict]:
        # 将类实例列表转换为字典列表
        return [message.to_dict() for message in messages]







# 图生文基础消息类
class Message_for_vl:
    def __init__(self, role: str, image, text: str):
        self.role = role
        self.image = image
        self.text = text

    # def is_base64(self, image):
    #     """
    #     检查给定的字符串是否是Base64编码
    #     """
    #     try:
    #         if isinstance(image, str) and image.startswith('data:image'):
    #             return True
    #         elif isinstance(image, str) and len(image) % 4 == 0:
    #             return True
    #         else:
    #             return False
    #     except:
    #         return False

    # def encode_image(self, image_path_or_base64):
    #     """
    #     如果image是路径，则转换为Base64，如果是Base64字符串，则直接返回
    #     """
    #     if self.is_base64(image_path_or_base64):
    #         return image_path_or_base64
    #     else:
    #         with open(image_path_or_base64, "rb") as image_file:
    #             return base64.b64encode(image_file.read()).decode("utf-8")

    def encode_to_base64(self, image_input):
        """
        将图片对象、图片路径或Base64编码的图片数据转换为Base64编码的图片数据。
        :param image_input: 图片对象、图片路径或Base64编码的图片数据。
        :return: Base64编码的图片数据。
        """
        # 检查是否已经是Base64编码的字符串
        if isinstance(image_input, str) and (image_input.startswith('data:image') or (len(image_input) % 4 == 0 and not image_input.strip().lower().startswith('http'))):
            return image_input

        # 检查是否是PIL图片对象
        elif isinstance(image_input, Image.Image):
            # 将PIL图片对象转换为Base64
            buffered = BytesIO()
            image_input.save(buffered, format="PNG")
            return base64.b64encode(buffered.getvalue()).decode("utf-8")

        # 检查是否是文件路径
        elif isinstance(image_input, str):
            try:
                # 尝试将文件路径转换为Base64
                with open(image_input, "rb") as image_file:
                    return base64.b64encode(image_file.read()).decode("utf-8")
            except IOError:
                # 如果路径无效，返回错误信息
                return "Invalid image path."

        # 如果输入类型不支持，返回错误信息
        else:
            return "Unsupported input type."
    
    
    def to_dict_for_vl(self):
        b64_img = self.encode_to_base64(self.image)
        return {
            "role": self.role,
            "content": [
                {"type": "text", "text": self.text},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{b64_img}",
                    },
                },
            ],
        }


# 图生文用户消息类
class UserMessage_for_vl(Message_for_vl):
    def __init__(self, image, text: str):
        super().__init__(role="user", image=image, text=text)

# 图生文消息转换器类
class MessageConverter_for_vl:

    # 将字典列表转换为类实例列表，再转换回字典列表的方法
    @staticmethod
    def convert_messages_for_vl(messages) -> list[dict]:
        # 将类实例列表转换为字典列表
        return [message.to_dict_for_vl() for message in messages]



class Llm_Service(object):
    """
    大模型问答服务
    """

    def __init__(self):
        """
        初始化
        """
        # 大语言模型客户端-同步
        self.llm_model_client = LlmModel.get_model_client()
        # 大语言模型客户端-异步
        self.sync_llm_model_client = LlmModel.get_async_model_client()

    def answer_question(self, messages: List[dict[str, str]], model_name: str, max_tokens=3000):
        """
        回答问题
        :param messages: 模型对话信息，用法：MessageConverter.convert_messages([UserMessage("你好")])，
                        最终传入的参数形式为：[{"role":"user","content": "你好"}]
        :param model_name: 模型名称
        :param max_tokens: 最大token数量
        :return:
        """
        # 调用模型
        reply = self.llm_model_client.chat.completions.create(
            model=model_name,
            messages=messages,
            max_tokens=max_tokens
        )
        # 返回答案
        return reply.choices[0].message.content

    
    def answer_question_for_vl(self, messages: List, model_name: str, max_tokens=1024):
    
        # 调用模型
        reply = self.llm_model_client.chat.completions.create(
            model=model_name,
            messages=messages,
            max_tokens=max_tokens
        )
        # 返回答案
        return reply.choices[0].message.content

    def stream_answer_question(self, messages, model_name: str, max_tokens=512):
        """
        流式回答问题
        :param messages: 模型对话信息
        :param model_name: 模型名称
        :param max_tokens: 最大token数量
        :return:
        """
        reply_stream = self.sync_llm_model_client.chat.completions.create(
            model=model_name,
            stream=True,
            messages=messages,
            max_tokens=max_tokens
        )
        # 返回答案流
        return reply_stream



if __name__ == "__main__":
    import time
    llm_service = Llm_Service()

    # response = llm_service.answer_question(MessageConverter.convert_messages([UserMessage("你好")]),
    #                                        ModelConfig.MAX_LLM_MODEL_NAME)

    # print(f"response: {response}")
    
    

                         
    # 该图生文接口支持输入【图片路径】或者【转为base64字节流的图片对象】或者【image图像】
    file_path = "/root/ShanShuo/Document_Parse/pdf_data/testpng2.png"
    start_time = time.time()
    response = llm_service.answer_question_for_vl(MessageConverter_for_vl.convert_messages_for_vl([UserMessage_for_vl(file_path,PromptConfig.TextExtractPrompt)]),
                                           ModelConfig.IMAGE_MAX_LLM_MODEL_NAME)
    end_time = time.time()
    print(len(response))
    print(f'图像开销：{(end_time - start_time)}')
    
    print("-------------------------------------")
    # print(f"response: {response}")

