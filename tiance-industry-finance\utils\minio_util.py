#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
# @Time    : 2024/10/21 15:29
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email   : <EMAIL>
# @File    : minio_util.py
# @Project : tiance-industry-finance
"""

from typing import Optional
from minio import Minio
from configs.minio_config import MinioConfig

class MinIoUtil(object):
    """
    MinIo工具类
    """

    # 客户端
    min_io_client = None

    @staticmethod
    def connect():
        """
        连接
        :return:
        """
        MinIoUtil.min_io_client = Minio(endpoint=MinioConfig.END_POINT,
                                        access_key=MinioConfig.ACCESS_KEY,
                                        secret_key=MinioConfig.SECRET_KEY,
                                        secure=False)

    @staticmethod
    def reconnect():
        """
        重新连接MinIo
        :return:
        """
        MinIoUtil.min_io_client = None
        MinIoUtil.connect()

    @staticmethod
    def create_bucket(bucket_name):
        """
        创建存储桶
        :param bucket_name: 存储桶名称
        :return:
        """
        # 存储桶是否存在
        if not MinIoUtil.min_io_client.bucket_exists(bucket_name=bucket_name):
            MinIoUtil.min_io_client.make_bucket(bucket_name=bucket_name)

    @staticmethod
    def remove_bucket(bucket_name):
        """
        删除存储桶
        :param bucket_name: 存储桶名称
        :return:
        """
        MinIoUtil.min_io_client.remove_bucket(bucket_name=bucket_name)

    @staticmethod
    def upload_file(bucket_name, remote_path, local_path):
        """
        上传文件
        :param bucket_name: 存储桶名称
        :param remote_path: 远程文件路径
        :param local_path: 本地文件路径
        :return:
        """
        MinIoUtil.min_io_client.fput_object(bucket_name=bucket_name, object_name=remote_path,
                                            file_path=local_path)

    @staticmethod
    def download_file(bucket_name, remote_path, local_path):
        MinIoUtil.min_io_client.fget_object(bucket_name=bucket_name, object_name=remote_path,
                                            file_path=local_path)

    @staticmethod
    def get_file_list(bucket_name, prefix):
        """
        获取文件列表
        :param bucket_name: 存储桶名称
        :param prefix: 前缀
        :return:
        """
        file_list = list()
        object_list = MinIoUtil.min_io_client.list_objects(bucket_name=bucket_name, prefix=prefix)
        for obj in object_list:
            file_list.append(obj.object_name)
        return file_list

    @staticmethod
    def remove_file(bucket_name, file_name):
        """
        删除文件
        :param bucket_name: 存储桶名称
        :param remote_path: 远程文件路径
        :return:
        """
        remote_path = f"pytest/{file_name}"
        MinIoUtil.min_io_client.remove_object(bucket_name, remote_path)

    @staticmethod
    def get_all_object(bucket_name, prefix: Optional[str] = None):
        """
        获取文件列表
        :param bucket_name: 存储桶名称
        :param prefix: 前缀
        :return:
        """
        file_list = []
        object_list = MinIoUtil.min_io_client.list_objects(
            bucket_name=bucket_name, recursive=True
        )
        for obj in object_list:
            file_list.append(obj)
        return file_list


if __name__ == '__main__':
    MinIoUtil.connect()

    # 创建存储桶
    b_n = "tiance-industry-finance"
    # MinIoUtil.create_bucket(b_n)

    # # 上传文件
    # r_p = "2024/08/27/audio/模型管理接口-20240826.xmind"
    # l_p = "C:/Users/<USER>/Desktop/模型管理接口-20240826.xmind"
    # MinIoUtil.get_all_object(b_n)

    # 下载文件
    result = MinIoUtil.get_all_object(
        bucket_name=b_n
    )
    for item in result:
        print(item.object_name)
