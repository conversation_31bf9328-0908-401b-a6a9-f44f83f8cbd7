#!/usr/bin/env python
# -*- coding:utf-8 -*-

'''
# @Time    : 2024/11/25 22:13
# <AUTHOR> LINRUI XU
# @FileName: reranker_util.py
# @Project : Czi_RAG
'''

from xinference.client import Client
from configs.run_config import RunConfig


class QueryReranker:
    """
    QueryReranker 类，用于通过 Xinference 客户端对查询进行文档重排序。
    """

    def __init__(self, server_url=RunConfig.XINFERENCE_API, model_name=RunConfig.RERANK_MODEL_NAME,
                 model_type="rerank"):
        """
        初始化 QueryReranker 类，配置服务器信息和模型参数。

        :param server_url: Xinference 服务器的 URL，默认为配置中的 API 地址。
        :param model_name: 模型名称，默认为配置中的模型名称。
        :param model_type: 模型类型（默认是 "rerank"）。
        """
        self.server_url = server_url  # 服务器 URL
        self.model_name = model_name  # 模型名称
        self.model_type = model_type  # 模型类型
        self.client = Client(server_url)  # 初始化 Xinference 客户端
        # 启动模型并获取模型的唯一标识符
        # print(model_name)
        # self.model_uid = self.client.launch_model(model_name=model_name, model_type=model_type)
        # 获取模型元数据
        # model_metadata = self.client.get_model_metadata(model_name)
        # 获取正在运行的模型列表
        models = self.client.list_models()
        # print(models)
        self.model_uid = None
        return_model = models[self.model_name]
        # print(return_model)
        if return_model:
            self.model_uid = return_model['id']
        else:
            self.model_uid = self.client.launch_model(model_name=model_name, model_type=model_type)
        # if(self.model_uid is None):
        #     self.model_uid = self.client.launch_model(model_name=model_name, model_type=model_type)
        
        # 获取模型实例
        self.model = self.client.get_model(self.model_uid)
        # print(self.model)

    def rerank_query(self, query, corpus):
        """
        对文档列表进行重排序。

        :param query: 查询字符串。
        :param corpus: 文档列表（字符串列表）。
        :return: 包含重排序结果的列表。
        """
        # 调用模型进行重排序
        # print(query)
        # print(corpus)
        reranked_corpus = self.model.rerank(corpus, query)
        # print(reranked_corpus)
        return reranked_corpus

    @staticmethod
    def get_top_x_indices(input_data, top_k):
        """
        获取重排序结果中 top-k 文档的索引。

        :param input_data: 包含相关性得分的重排序结果。
        :param top_k: 需要获取的文档数量。
        :return: top-k 文档的索引列表。
        """
        # 根据相关性得分从高到低排序
        sorted_results = sorted(input_data['results'], key=lambda item: item['relevance_score'], reverse=True)
        # 提取 top-k 的文档索引
        top_x_indexes = [result['index'] for result in sorted_results[:top_k]]
        return top_x_indexes

    @staticmethod
    def get_top_x_corpus(corpus, top_x_indexes):
        """
        根据索引从文档列表中提取 top-k 文档内容。

        :param corpus: 原始文档列表。
        :param top_x_indexes: top-k 文档的索引列表。
        :return: top-k 文档内容的列表。
        """
        # 根据索引提取文档
        top_x_corpus = [corpus[i] for i in top_x_indexes]
        return top_x_corpus


if __name__ == "__main__":

    query = "A man is eating pasta."
    corpus = [
        "A man is eating a piece of bread.",
        "The girl is carrying a baby.",
        "A man is riding a horse.",
        "A woman is playing violin.",
        "A man is eating food."
    ]
    reranker = QueryReranker()

    # 使用模型对文档进行重排序
    reranked_results = reranker.rerank_query(query, corpus)
    print(f"reranked_results:{reranked_results}")

    # 获取重排序结果中的 top-k 索引
    top_k = 2  # 获取排名前 2 的文档
    top_k_indices = reranker.get_top_x_indices(reranked_results, top_k)

    # 获取 top-k 的文档内容
    top_k_corpus = reranker.get_top_x_corpus(corpus, top_k_indices)

    # 输出结果
    print("Top-k 文档内容:", top_k_corpus)

