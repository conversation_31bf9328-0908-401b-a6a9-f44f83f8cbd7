#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :text_utils.py
@Description  :
<AUTHOR>
@Date         :2025/03/04 10:47:25
'''

import re

class TextUtil(object):
    """
    文本工具类
    """
    @staticmethod
    def remove_think(text):
        return re.sub(r"<think>.*</think>\n\n", "", text, flags=re.DOTALL)
    
    @staticmethod
    def get_json_from_text(text):
        return re.search( r'\{.*\}', text, flags=re.DOTALL)
    
txt ="""<think>\n好的，我现在需要处理用户提供的产业链信息，并按照指定的格式进行合并和输出。首先，我要仔细阅读用户提供的内容，理解每个标签的含义以及它们之间的关系。\n\n用户提供的标签包括：\n1. '坐标机器人系统'\n2. '并联机器人(Delta-3)'\n3. AGV搬运机器人\n4. 谐波减速器(HD-20)\n5. 行星减速器（型号：PL-50）减速比1:50\n6. 关节机器人手臂（J6-700）\n\n接下来，我需要根据技能中的规则进行合并。规则指出，如果两个标签在语义上相同，就可以合并。同时，每个标签只能被一个标签替换，且输出必须是JSON格式，包含result键和合并后的标签项。\n\n首先，我会检查每个标签是否有重复或相似的含义。例如，'并联机器人(Delta-3)'和'关节机器人手臂（J6-700）'是否属于同一类别。看起来它们都是机器人，但类型不同，一个是并联机器人，另一个是关节机器人手臂，所以它们属于不同的标签，不能合并。\n\n同样，AGV搬运机器人是一种移动机器人，与坐标机器人系统和并联机器人属于不同的类别，所以它们各自独立。\n\n接下来是减速器部分，谐波减速器和行星减速器是两种不同的减速器类型，因此它们也不能合并。\n\n因此，所有提供的标签都是独立的，没有重复或相同的语义，所以不需要进行合并。每个标签都是唯一的，直接作为结果输出即可。\n\n最后，我需要确保输出的JSON格式正确，包含result键，其值为合并后的标签数组，以及每个标签项的详细信息。由于没有合并，每个标签项的数组为空，只包含标签本身。\n\n总结一下，最终的JSON结构应该是：\n{\n  "result": [\n    "坐标机器人系统",\n    "并联机器人(Delta-3)",\n    "AGV搬运机器人",\n    "谐波减速器(HD-20)",\n    "行星减速器（型号：PL-50）减速比1:50",\n    "关节机器人手臂（J6-700）"\n  ],\n  "坐标机器人系统": [],\n  "并联机器人(Delta-3)": [],\n  "AGV搬运机器人": [],\n  "谐波减速器(HD-20)": [],\n  "行星减速器（型号：PL-50）减速比1:50": [],\n  "关节机器人手臂（J6-700）": []\n}\n\n这样，用户的需求就得到了满足，所有标签都被正确识别和输出，没有遗漏或错误合并的情况。\n</think>\n\n```json\n{\n  "result": [\n    "坐标机器人系统",\n    "并联机器人(Delta-3)",\n    "AGV搬运机器人",\n    "谐波减速器(HD-20)",\n    "行星减速器（型号：PL-50）减速比1:50",\n    "关节机器人手臂（J6-700）"\n  ],\n  "坐标机器人系统": [],\n  "并联机器人(Delta-3)": [],\n  "AGV搬运机器人": [],\n  "谐波减速器(HD-20)": [],\n  "行星减速器（型号：PL-50）减速比1:50": [],\n  "关节机器人手臂（J6-700）": []\n}\n"""
if __name__ == "__main__":
    import json 
    prettify_json = TextUtil.get_json_from_text(txt)
    if prettify_json:
        try:
            prettify_json = json.loads(prettify_json.group())
        except Exception as e:
            print(prettify_json.group())
            raise