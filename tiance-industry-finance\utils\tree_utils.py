#!/usr/bin/env python
# -*- encoding: utf-8 -*-
'''
@File         :tree_utils.py
@Description  :
<AUTHOR>
@Date         :2025/03/02 10:22:14
'''

class TreeUtils(object):

    @staticmethod
    def extract_nodes(data, parent_path=""):
        """
        递归函数，用于提取父子关系节点
        """
        nodes = []
        for key, value in data.items():
            current_path = f"{parent_path}|{key}" if parent_path else key
            nodes.append(current_path)
            if isinstance(value, dict):
                nodes.extend(TreeUtils.extract_nodes(value, current_path))
        return nodes
    @staticmethod
    def nodes2listmap(data, parent_path="",default_value={
                    "company": [],
                    "product": [],
                    "source_list": []
                }):
        """
        递归函数，用于提取父子关系节点
        """
        nodes = {}
        for key, value in data.items():
            if key == "data":
                continue
            current_path = f"{parent_path}|{key}" if parent_path else key
            # current_path 
            # nodes.append(current_path)
            if isinstance(value, dict):
                if "data" in value:
                    nodes.update({current_path:value["data"]})
                else:
                    nodes.update({current_path:default_value})
                nodes.update(TreeUtils.nodes2listmap(value, current_path))
            else:
                nodes.update({current_path:default_value})
        return nodes

    @staticmethod
    def restore_tree(nodes):
        """
        从节点路径列表反向还原为嵌套字典结构
        """
        tree = {}
        for node in nodes:
            # 按照路径分隔符分割路径
            path = node.split('|')
            current_dict = tree
            for i, key in enumerate(path):
                # 如果是最后一个节点，直接赋值为空字典
                if i == len(path) - 1:
                    current_dict[key] = {}
                # 如果中间节点不存在，则创建一个空字典
                elif key not in current_dict:
                    current_dict[key] = {}
                # 移动到下一层
                current_dict = current_dict[key]
        return tree
    
    @staticmethod
    def merge_data(data):
        """
        合并数据
        """
        keys = list(data.keys())  # 获取所有键
        keys.sort(key=lambda x: len(x.split("|")), reverse=True)  # 按层级从低到高排序

        for key in keys:
            parent_key = "|".join(key.split("|")[:-1])  # 获取当前键的父级键
            if parent_key:  # 如果有父级键
                data[parent_key].extend(data[key])  # 将当前键的值合并到父级键的值中
                data[parent_key] = list(set(data[parent_key]))  # 去重

        return data
    @staticmethod
    def expand_dict(original,default_value={
                    "company": [],
                    "product": [],
                    "source_list": []
                }):
        result = {}
        for key in original:
            parts = key.split('|')
            # 生成所有父级键并添加到结果中，如果不存在的话
            for i in range(1, len(parts)):
                parent_key = '|'.join(parts[:i])
                if parent_key not in result:
                    result[parent_key] = default_value
            # 添加当前键及其对应的值
            result[key] = original[key]
        return result
if __name__ == "__main__":
    print("test")